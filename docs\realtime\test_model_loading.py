#!/usr/bin/env python3
"""
测试模型加载功能

验证修复后的模型加载是否正常工作。
"""

import sys
sys.path.insert(0, '.')

def test_speech_recognizer_model_loading():
    """测试语音识别器的模型加载"""
    print("🔍 测试语音识别器模型加载...")
    
    try:
        from v2subpy.realtime.models import RealtimeConfig
        from v2subpy.realtime.speech_recognizer import SpeechRecognizer
        
        # 创建配置
        config = RealtimeConfig(
            whisper_model='medium',  # 使用较小的模型进行测试
            whisper_device='auto',
            source_language='en'
        )
        
        # 创建语音识别器
        recognizer = SpeechRecognizer(config)
        print("✅ SpeechRecognizer 创建成功")
        
        # 测试模型加载
        print("🔄 正在加载模型...")
        recognizer.load_model()
        print("✅ 模型加载成功")
        
        # 检查模型是否已加载
        if recognizer.model is not None:
            print("✅ 模型实例验证成功")
            print(f"📋 模型信息: {type(recognizer.model)}")
        else:
            print("❌ 模型实例为空")
            return False
        
        # 测试模型信息获取
        model_info = recognizer.get_model_info()
        print(f"📊 模型信息: {model_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimized_speech_recognizer():
    """测试优化的语音识别器"""
    print("\n🔍 测试优化的语音识别器...")
    
    try:
        from v2subpy.realtime.models import RealtimeConfig
        from v2subpy.realtime.recognition_optimizer import OptimizedSpeechRecognizer
        
        # 创建配置
        config = RealtimeConfig(
            whisper_model='medium',
            whisper_device='auto',
            source_language='en'
        )
        
        # 创建优化的语音识别器
        optimizer = OptimizedSpeechRecognizer(config)
        print("✅ OptimizedSpeechRecognizer 创建成功")
        
        # 测试预加载模型
        print("🔄 正在预加载模型...")
        optimizer.preload_model()
        print("✅ 模型预加载成功")
        
        # 检查性能统计
        stats = optimizer.get_performance_stats()
        print(f"📊 性能统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 模型加载功能测试")
    print("=" * 50)
    
    tests = [
        ("语音识别器模型加载", test_speech_recognizer_model_loading),
        ("优化语音识别器", test_optimized_speech_recognizer),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模型加载测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查模型加载逻辑。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(0)