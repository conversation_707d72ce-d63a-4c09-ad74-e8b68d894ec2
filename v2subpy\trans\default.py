"""
handle translation related tasks
"""
from __future__ import annotations
import traceback
import requests
import jwt
import datetime
from typing import Any
from urllib.parse import quote

from v2subpy.trans.base import BaseTranslator, TimeOutException
from v2subpy.utils import config
from v2subpy.utils.log import logger


class GoogleTranslator(BaseTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def convert_lang(self, lang_code: str) -> str:
        '''
        yue -> zh-TW
        '''
        lang_code_d = {
            'yue': 'zh-TW',
        }
        if lang_code in lang_code_d:
            return lang_code_d[lang_code]
        else:
            return lang_code

    def google_translate(self, text, source_language, destination_language):

        url = 'https://translate.googleapis.com/translate_a/'
        params = 'single?client=gtx&sl='+source_language + \
            '&tl='+destination_language+'&dt=t&q='+text
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                   'Referer': 'https://translate.google.com', }
        timeout = self.translator_config['timeout']
        response = requests.get(
            url+params, headers=headers, proxies=self.proxies, timeout=timeout)
        # print('response.status_code = {}'.format(response.status_code))
        if response.status_code == 200:
            response_json = response.json()[0]
            # print('response_json = {}'.format(response_json))
            length = len(response_json)
            # print('length = {}'.format(length))
            translation = ""
            for i in range(length):
                # print("{} {}".format(i, response_json[i][0]))
                translation = translation + response_json[i][0]
            return translation
        else:
            raise ValueError("error status code")

    def _translate(self, text: str, source_language: str, destination_language: str):
        try:
            result = self.google_translate(
                text, source_language, destination_language)
            return result
        except Exception as e:
            raise TimeOutException(config.get_text(
                "error.TranslationTimeoutError"))


class MicrosoftTranslator(BaseTranslator):
    AUTH_URL = "https://edge.microsoft.com/translate/auth"
    TRANSLATE_URL = "https://api-edge.cognitive.microsofttranslator.com/translate"
    ACCESS_TOKEN = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @classmethod
    def is_expire(cls):
        options = {"verify_signature": False}
        offset = 60
        payload = jwt.decode(cls.ACCESS_TOKEN, options=options)
        exp = payload['exp']
        now = datetime.datetime.timestamp(datetime.datetime.now())
        # logger.debug(payload)
        logger.debug(f"{exp=}, {now=}")
        return now - offset > exp

    @classmethod
    def get_token(cls):
        if cls.ACCESS_TOKEN is not None and not cls.is_expire():
            return
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.57'
        headers = {
            'user-agent': user_agent}
        request = requests.get(cls.AUTH_URL,
                               headers=headers)
        if request.status_code == requests.codes.ok:
            # print(request.content)
            cls.ACCESS_TOKEN = request.text
            logger.debug('get new token')
        else:
            logger.debug('error to get token')

    def microsoft_translate(self, text, source_language, destination_language):
        cls = self.__class__
        params = {
            'api-version': '3.0',
            'to': destination_language
        }
        # 微软翻译器如果没有设置from参数， 为自动检测翻译
        if source_language != 'auto':
            params['from'] = source_language

        headers = {
            'Content-type': 'application/json; charset=UTF-8',
        }
        body = [{"text": text}]

        cls.get_token()
        # logger.debug(f"{cls.ACCESS_TOKEN=}")
        headers['Authorization'] = f"Bearer {cls.ACCESS_TOKEN}"
        timeout = self.translator_config['timeout']
        request = requests.post(cls.TRANSLATE_URL, params=params,
                                headers=headers, json=body, timeout=timeout)
        if request.status_code == requests.codes.ok:
            response_json = request.json()[0]
            # logger.debug(response_json)
            translation = response_json["translations"][0]["text"]
            return translation
        else:
            raise ValueError("error status code")

    def _translate(self, text: str, source_language: str, destination_language: str):
        try:
            result = self.microsoft_translate(
                text, source_language, destination_language)
            return result
        except Exception as e:
            raise TimeOutException(config.get_text(
                "error.TranslationTimeoutError"))
