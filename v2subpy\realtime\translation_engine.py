"""
翻译引擎

封装现有的翻译器接口，用于实时字幕翻译。
"""

import time
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, Future, as_completed

from v2subpy.utils.log import logger
from v2subpy.utils import config
from v2subpy.trans.base import (
    BaseTranslator, TranslationError, TimeOutException,
    all_translators, init_all_translators
)
from v2subpy.v2sub import get_translator
from .models import SubtitleSegment, RealtimeConfig
from .exceptions import TranslationError as RealtimeTranslationError
from .retry_manager import RetryManager


class TranslationEngine:
    """翻译引擎类"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化翻译引擎
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        logger.debug(self.config)
        self.translator: Optional[BaseTranslator] = None
        self.translator_type = realtime_config.translator_type
        self.target_language = realtime_config.target_language
        self.translation_cache: Dict[str, str] = {}
        self.retry_manager = RetryManager(
            max_retries=realtime_config.max_retries,
            base_delay=realtime_config.retry_delay
        )
        
        # 初始化所有翻译器
        init_all_translators()
        
        logger.debug(f"TranslationEngine 初始化: type={self.translator_type}, "
                    f"target_lang={self.target_language}")
    
    def create_translator(self, source_language: str) -> BaseTranslator:
        """
        创建翻译器实例
        
        Args:
            source_language: 源语言代码
            
        Returns:
            BaseTranslator: 翻译器实例
            
        Raises:
            RealtimeTranslationError: 翻译器创建失败
        """
        try:
            translator = get_translator(
                source_language,
                self.target_language,
                self.translator_type
            )
            logger.debug(f"翻译器创建成功: {self.translator_type} "
                        f"({source_language} -> {self.target_language})")
            return translator
            
        except Exception as e:
            logger.error(f"翻译器创建失败: {e}")
            raise RealtimeTranslationError(
                f"翻译器创建失败: {e}",
                translator_type=self.translator_type,
                can_retry=False
            )
    
    def translate_text(self, text: str, source_lang: str, target_lang: str = None) -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码（可选，默认使用配置中的目标语言）
            
        Returns:
            str: 翻译后的文本
            
        Raises:
            RealtimeTranslationError: 翻译失败
        """
        if not text or not text.strip():
            return ""
        
        # 使用目标语言参数或配置中的目标语言
        target_lang = target_lang or self.target_language
        
        # 检查缓存
        cache_key = f"{text}_{source_lang}_{target_lang}"
        if cache_key in self.translation_cache:
            logger.debug(f"使用缓存的翻译结果: {text[:30]}...")
            return self.translation_cache[cache_key]
        
        try:
            # 创建或获取翻译器
            if self.translator is None or self.translator.source_lang != source_lang:
                self.translator = self.create_translator(source_lang)
            
            logger.debug(f"开始翻译文本: {text[:50]}...")
            start_time = time.time()
            
            # 使用重试机制进行翻译
            translated_text = self.retry_manager.retry_with_condition(
                self.translator.translate,
                self._should_retry_translation_error,
                text, source_lang, target_lang
            )
            
            translate_time = time.time() - start_time
            logger.debug(f"翻译完成: 耗时 {translate_time:.2f}s, "
                        f"结果: {translated_text[:50]}...")
            
            # 缓存结果
            self.translation_cache[cache_key] = translated_text
            
            return translated_text
            
        except Exception as e:
            logger.error(f"文本翻译失败: {text[:30]}..., {e}")
            raise RealtimeTranslationError(
                f"文本翻译失败: {e}",
                translator_type=self.translator_type,
                source_text=text[:100]
            )
    
    def translate_subtitle_segment(self, subtitle_segment: SubtitleSegment) -> SubtitleSegment:
        """
        翻译字幕段
        
        Args:
            subtitle_segment: 字幕段对象
            
        Returns:
            SubtitleSegment: 翻译后的字幕段对象
          
    """
        if not subtitle_segment.original_text:
            return subtitle_segment
        
        try:
            translated_text = self.translate_text(
                subtitle_segment.original_text,
                subtitle_segment.language
            )
            
            # 更新字幕段
            subtitle_segment.translated_text = translated_text
            subtitle_segment.status = "completed"
            
            return subtitle_segment
            
        except RealtimeTranslationError as e:
            logger.warning(f"字幕段翻译失败: {subtitle_segment.segment_id}, {e}")
            # 保留原文作为备选
            subtitle_segment.translated_text = subtitle_segment.original_text
            subtitle_segment.status = "error"
            return subtitle_segment
    
    def translate_batch(self, subtitle_segments: List[SubtitleSegment]) -> List[SubtitleSegment]:
        """
        批量翻译字幕段
        
        Args:
            subtitle_segments: 字幕段列表
            
        Returns:
            List[SubtitleSegment]: 翻译后的字幕段列表
        """
        if not subtitle_segments:
            return []
        
        logger.debug(f"开始批量翻译: {len(subtitle_segments)} 个字幕段")
        
        results = []
        for segment in subtitle_segments:
            try:
                translated_segment = self.translate_subtitle_segment(segment)
                results.append(translated_segment)
            except Exception as e:
                logger.warning(f"批量翻译中的段落失败: {segment.segment_id}, {e}")
                # 保留原文
                segment.translated_text = segment.original_text
                segment.status = "error"
                results.append(segment)
        
        logger.debug(f"批量翻译完成: {len(results)} 个结果")
        return results
    
    def translate_concurrent(self, subtitle_segments: List[SubtitleSegment], 
                           max_workers: int = 3) -> List[SubtitleSegment]:
        """
        并发翻译字幕段
        
        Args:
            subtitle_segments: 字幕段列表
            max_workers: 最大并发数
            
        Returns:
            List[SubtitleSegment]: 翻译后的字幕段列表
        """
        if not subtitle_segments:
            return []
        
        logger.debug(f"开始并发翻译: {len(subtitle_segments)} 个字幕段, "
                    f"并发数: {max_workers}")
        
        # 创建线程池
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交翻译任务
            future_to_segment = {
                executor.submit(self.translate_subtitle_segment, segment): segment
                for segment in subtitle_segments
            }
            
            # 收集结果
            results = [None] * len(subtitle_segments)
            segment_index_map = {id(segment): i for i, segment in enumerate(subtitle_segments)}
            
            for future in as_completed(future_to_segment):
                segment = future_to_segment[future]
                index = segment_index_map[id(segment)]
                
                try:
                    result = future.result(timeout=self.config.translation_timeout)
                    results[index] = result
                except Exception as e:
                    logger.warning(f"并发翻译失败: {segment.segment_id}, {e}")
                    # 保留原文
                    segment.translated_text = segment.original_text
                    segment.status = "error"
                    results[index] = segment
        
        logger.debug(f"并发翻译完成: {len(results)} 个结果")
        return results
    
    def _should_retry_translation_error(self, exception: Exception) -> bool:
        """
        判断翻译错误是否应该重试
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否应该重试
        """
        # 检查是否是可重试的异常类型
        if isinstance(exception, TimeOutException):
            return True
        
        if isinstance(exception, TranslationError):
            return getattr(exception, 'need_retry', True)
        
        # 检查错误消息中的关键词
        error_message = str(exception).lower()
        retryable_keywords = [
            'timeout', 'rate limit', 'quota exceeded',
            'service unavailable', 'temporary', 'network',
            'connection', 'server error', '503', '502', '429'
        ]
        
        return any(keyword in error_message for keyword in retryable_keywords)
    
    def clear_cache(self) -> None:
        """清理翻译缓存"""
        self.translation_cache.clear()
        logger.debug("翻译缓存已清理")
    
    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self.translation_cache)
    
    def get_engine_info(self) -> Dict[str, Any]:
        """获取翻译引擎信息"""
        return {
            'translator_type': self.translator_type,
            'target_language': self.target_language,
            'cache_size': self.get_cache_size(),
            'is_initialized': self.translator is not None
        }
    
    def test_translation(self) -> bool:
        """
        测试翻译功能
        
        Returns:
            bool: 测试是否成功
        """
        try:
            test_text = "Hello, world!"
            result = self.translate_text(test_text, "en", self.target_language)
            
            if result and result != test_text:
                logger.info(f"翻译测试成功: '{test_text}' -> '{result}'")
                return True
            else:
                logger.warning("翻译测试失败: 结果为空或未翻译")
                return False
                
        except Exception as e:
            logger.error(f"翻译测试失败: {e}")
            return False


class BatchTranslationEngine:
    """批量翻译引擎（优化版）"""
    
    def __init__(self, realtime_config: RealtimeConfig, batch_size: int = 5):
        """
        初始化批量翻译引擎
        
        Args:
            realtime_config: 实时处理配置
            batch_size: 批量大小
        """
        self.engine = TranslationEngine(realtime_config)
        self.batch_size = batch_size
        self.pending_segments = []
        self.processed_count = 0
        
    def add_segment(self, subtitle_segment: SubtitleSegment) -> Optional[List[SubtitleSegment]]:
        """
        添加字幕段到批量处理队列
        
        Args:
            subtitle_segment: 字幕段
            
        Returns:
            List[SubtitleSegment]: 如果批量已满，返回处理结果；否则返回 None
        """
        self.pending_segments.append(subtitle_segment)
        
        if len(self.pending_segments) >= self.batch_size:
            return self.process_batch()
        
        return None
    
    def process_batch(self) -> List[SubtitleSegment]:
        """处理当前批量"""
        if not self.pending_segments:
            return []
        
        logger.debug(f"处理批量翻译: {len(self.pending_segments)} 个字幕段")
        
        results = self.engine.translate_concurrent(
            self.pending_segments,
            max_workers=min(3, len(self.pending_segments))
        )
        
        self.processed_count += len(results)
        self.pending_segments.clear()
        
        return results
    
    def flush(self) -> List[SubtitleSegment]:
        """处理剩余的字幕段"""
        return self.process_batch()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'processed_count': self.processed_count,
            'pending_count': len(self.pending_segments),
            'engine_info': self.engine.get_engine_info()
        }


class AdaptiveTranslationEngine:
    """自适应翻译引擎"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化自适应翻译引擎
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.engine = TranslationEngine(realtime_config)
        self.performance_history = []
        self.current_strategy = "sequential"  # sequential, batch, concurrent
        
    def translate_adaptive(self, subtitle_segments: List[SubtitleSegment]) -> List[SubtitleSegment]:
        """
        自适应翻译字幕段
        
        Args:
            subtitle_segments: 字幕段列表
            
        Returns:
            List[SubtitleSegment]: 翻译后的字幕段列表
        """
        if not subtitle_segments:
            return []
        
        start_time = time.time()
        
        # 根据当前策略选择处理方式
        if self.current_strategy == "sequential":
            results = self.engine.translate_batch(subtitle_segments)
        elif self.current_strategy == "concurrent":
            results = self.engine.translate_concurrent(subtitle_segments)
        else:  # batch
            results = self.engine.translate_batch(subtitle_segments)
        
        # 记录性能
        process_time = time.time() - start_time
        self.performance_history.append({
            'strategy': self.current_strategy,
            'segment_count': len(subtitle_segments),
            'process_time': process_time,
            'avg_time_per_segment': process_time / len(subtitle_segments)
        })
        
        # 调整策略
        self._adjust_strategy()
        
        return results
    
    def _adjust_strategy(self) -> None:
        """根据性能历史调整翻译策略"""
        if len(self.performance_history) < 3:
            return
        
        # 分析最近的性能数据
        recent_performance = self.performance_history[-3:]
        avg_time_per_segment = sum(p['avg_time_per_segment'] for p in recent_performance) / 3
        
        # 如果翻译速度太慢，切换到并发模式
        if avg_time_per_segment > 3.0 and self.current_strategy != "concurrent":
            self.current_strategy = "concurrent"
            logger.debug("切换到并发翻译模式")
        
        # 如果翻译速度适中，使用批量模式
        elif 1.0 < avg_time_per_segment <= 3.0 and self.current_strategy != "batch":
            self.current_strategy = "batch"
            logger.debug("切换到批量翻译模式")
        
        # 如果翻译速度很快，使用顺序模式
        elif avg_time_per_segment <= 1.0 and self.current_strategy != "sequential":
            self.current_strategy = "sequential"
            logger.debug("切换到顺序翻译模式")
        
        # 限制历史记录大小
        if len(self.performance_history) > 10:
            self.performance_history = self.performance_history[-10:]