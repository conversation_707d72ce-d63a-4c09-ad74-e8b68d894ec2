"""
语音识别器

封装 faster-whisper 的语音识别功能，用于实时字幕处理。
"""

import time
from pathlib import Path
from typing import Optional, Dict, Any
from faster_whisper import WhisperModel
from faster_whisper.audio import decode_audio

from v2subpy.utils.log import logger
from v2subpy.utils import config
from v2subpy.model.whisper import (
    calculate_cpu_count, load_options, detect_language,
    ModelLoadError
)
from v2subpy.model.options import load_initial_prompt
from v2subpy.model.format import merge_sentences
from v2subpy.sub.subutils import handle_chinese_segment
from .models import AudioSegment, SubtitleSegment, RealtimeConfig
from .exceptions import SpeechRecognitionError


class SpeechRecognizer:
    """语音识别器类"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化语音识别器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.model: Optional[WhisperModel] = None
        self.model_name = realtime_config.whisper_model
        self.device = realtime_config.whisper_device
        self.source_language = realtime_config.source_language
        self.recognition_cache: Dict[str, SubtitleSegment] = {}
        
        logger.debug(f"SpeechRecognizer 初始化: model={self.model_name}, "
                    f"device={self.device}, language={self.source_language}")
    
    def load_model(self) -> None:
        """加载 Whisper 模型"""
        if self.model is not None:
            logger.debug("Whisper 模型已加载")
            return
        
        try:
            logger.info(f"正在加载 Whisper 模型: {self.model_name}")
            start_time = time.time()
            
            # 使用现有的模型加载逻辑
            from v2subpy.v2sub import init_model
            self.model = init_model(self.model_name)
            
            load_time = time.time() - start_time
            logger.info(f"Whisper 模型加载完成，耗时: {load_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Whisper 模型加载失败: {e}")
            raise ModelLoadError(f"无法加载 Whisper 模型: {e}")
    
    def transcribe_segment(self, audio_segment: AudioSegment) -> SubtitleSegment:
        """
        转录音频段
        
        Args:
            audio_segment: 音频段对象
            
        Returns:
            SubtitleSegment: 字幕段对象
            
        Raises:
            SpeechRecognitionError: 语音识别失败
        """
        # 检查缓存
        if audio_segment.segment_id in self.recognition_cache:
            logger.debug(f"使用缓存的识别结果: {audio_segment.segment_id}")
            return self.recognition_cache[audio_segment.segment_id]
        
        try:
            # 确保模型已加载
            if self.model is None:
                self.load_model()
            
            logger.debug(f"开始转录音频段: {audio_segment.segment_id}")
            start_time = time.time()
            
            # 准备转录选项
            options = self._prepare_transcribe_options()
            
            # 执行转录
            segments, info = self.model.transcribe(
                audio_segment.file_path,
                **options
            )
            
            # 处理转录结果
            subtitle_segment = self._process_transcribe_result(
                audio_segment, segments, info
            )
            
            # 缓存结果
            self.recognition_cache[audio_segment.segment_id] = subtitle_segment
            
            transcribe_time = time.time() - start_time
            logger.debug(f"音频段转录完成: {audio_segment.segment_id}, "
                        f"耗时: {transcribe_time:.2f}s, "
                        f"置信度: {subtitle_segment.confidence:.2f}")
            
            return subtitle_segment
            
        except Exception as e:
            logger.error(f"音频段转录失败: {audio_segment.segment_id}, {e}")
            raise SpeechRecognitionError(
                f"音频段转录失败: {e}",
                segment_id=audio_segment.segment_id,
                model_name=self.model_name
            )
    
    def transcribe_batch(self, audio_segments: list[AudioSegment]) -> list[SubtitleSegment]:
        """
        批量转录音频段
        
        Args:
            audio_segments: 音频段列表
            
        Returns:
            list[SubtitleSegment]: 字幕段列表
        """
        results = []
        for segment in audio_segments:
            try:
                result = self.transcribe_segment(segment)
                results.append(result)
            except SpeechRecognitionError as e:
                logger.warning(f"跳过转录失败的音频段: {e}")
                # 创建空的字幕段
                empty_segment = SubtitleSegment(
                    segment_id=segment.segment_id,
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    original_text="",
                    status="error"
                )
                results.append(empty_segment)
        
        return results
    
    def detect_language_from_segment(self, audio_segment: AudioSegment) -> str:
        """
        从音频段检测语言
        
        Args:
            audio_segment: 音频段对象
            
        Returns:
            str: 检测到的语言代码
        """
        try:
            if self.model is None:
                self.load_model()
            
            # 解码音频
            audio = decode_audio(
                audio_segment.file_path,
                sampling_rate=self.model.feature_extractor.sampling_rate
            )
            
            # 检测语言
            language, probability, _ = self.model.detect_language(audio)
            
            logger.debug(f"检测到语言: {language}, 置信度: {probability:.2f}")
            return language
            
        except Exception as e:
            logger.warning(f"语言检测失败: {e}")
            return self.source_language if self.source_language != "auto" else "en"
    

    
    def _prepare_transcribe_options(self) -> Dict[str, Any]:
        """准备转录选项"""
        options = {
            'language': None if self.source_language == "auto" else self.source_language,
            'word_timestamps': True,
            'vad_filter': True,  # 启用 VAD 过滤
            'vad_parameters': {
                'threshold': 0.35,
                'min_speech_duration_ms': 100,
                'min_silence_duration_ms': 20,
                'speech_pad_ms': 30,
            }
        }
        
        # 添加初始提示
        if self.source_language != "auto":
            prompt_options = load_initial_prompt(self.source_language)
            options.update(prompt_options)
        
        return options
    
    def _process_transcribe_result(self, audio_segment: AudioSegment, 
                                 segments, info) -> SubtitleSegment:
        """
        处理转录结果
        
        Args:
            audio_segment: 原始音频段
            segments: 转录段落
            info: 转录信息
            
        Returns:
            SubtitleSegment: 处理后的字幕段
        """
        # 合并所有转录段落的文本
        texts = []
        total_confidence = 0.0
        segment_count = 0
        
        for segment in segments:
            text = segment.text.strip()
            if text:
                # 处理中文简繁体
                if info.language == 'zh':
                    text = handle_chinese_segment(segment)
                texts.append(text)
                
                # 计算置信度（如果有词级时间戳）
                if hasattr(segment, 'words') and segment.words:
                    word_confidences = [
                        getattr(word, 'probability', 0.8) 
                        for word in segment.words
                    ]
                    if word_confidences:
                        total_confidence += sum(word_confidences) / len(word_confidences)
                        segment_count += 1
        
        # 合并文本
        original_text = ' '.join(texts) if texts else ""
        
        # 计算平均置信度
        confidence = (total_confidence / segment_count) if segment_count > 0 else 0.8
        
        # 创建字幕段
        subtitle_segment = SubtitleSegment(
            segment_id=audio_segment.segment_id,
            start_time=audio_segment.start_time,
            end_time=audio_segment.end_time,
            original_text=original_text,
            confidence=confidence,
            language=info.language,
            status="completed" if original_text else "empty"
        )
        
        return subtitle_segment
    
    def clear_cache(self) -> None:
        """清理识别缓存"""
        self.recognition_cache.clear()
        logger.debug("语音识别缓存已清理")
    
    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self.recognition_cache)
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_name': self.model_name,
            'device': self.device,
            'source_language': self.source_language,
            'is_loaded': self.model is not None,
            'cache_size': self.get_cache_size()
        }
    
    def unload_model(self) -> None:
        """卸载模型以释放内存"""
        if self.model is not None:
            del self.model
            self.model = None
            logger.info("Whisper 模型已卸载")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.unload_model()
            self.clear_cache()
        except Exception:
            pass  # 忽略析构时的异常


class BatchSpeechRecognizer:
    """批量语音识别器（用于优化性能）"""
    
    def __init__(self, realtime_config: RealtimeConfig, batch_size: int = 3):
        """
        初始化批量语音识别器
        
        Args:
            realtime_config: 实时处理配置
            batch_size: 批量大小
        """
        self.recognizer = SpeechRecognizer(realtime_config)
        self.batch_size = batch_size
        self.pending_segments = []
    
    def add_segment(self, audio_segment: AudioSegment) -> Optional[list[SubtitleSegment]]:
        """
        添加音频段到批量处理队列
        
        Args:
            audio_segment: 音频段
            
        Returns:
            list[SubtitleSegment]: 如果批量已满，返回处理结果；否则返回 None
        """
        self.pending_segments.append(audio_segment)
        
        if len(self.pending_segments) >= self.batch_size:
            return self.process_batch()
        
        return None
    
    def process_batch(self) -> list[SubtitleSegment]:
        """处理当前批量"""
        if not self.pending_segments:
            return []
        
        results = self.recognizer.transcribe_batch(self.pending_segments)
        self.pending_segments.clear()
        return results
    
    def flush(self) -> list[SubtitleSegment]:
        """处理剩余的音频段"""
        return self.process_batch()