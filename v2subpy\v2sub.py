import time
import traceback
from typing import Optional
from faster_whisper import WhisperModel
from v2subpy.model.writers import format_timestamp, get_writer
from pathlib import Path
from v2subpy.model.whisper import ModelLoadError, calculate_cpu_count, transcribe, write_result, detect_language
from v2subpy.sub import subutils
from v2subpy.trans.base import BaseTranslator, TimeOutException, TranslationError
from v2subpy.trans.base import all_translators, init_all_translators
from v2subpy.sub.sub_file import SubFile
from v2subpy.sub import file_readers
from v2subpy.sub.subutils import handle_chinese_result, make_aligned_merge_sub
from v2subpy.utils import config
from v2subpy.utils import checksys
from v2subpy.utils.loadfile import get_file
from v2subpy.utils.log import logger
from v2subpy.utils.config import get_key, get_text, get_lang_name
from v2subpy.video import ff
from v2subpy.video import tts
from v2subpy.utils.version_manage import Version<PERSON>ana<PERSON>, Fn, init_verson_manage
from v2subpy.utils.common import FnNotSupport, TaskStoppedError, is_task_stopped, set_share_value, get_profile
model = None
loaded_config = {
    'device': None,
    'model_name': None,
    'cpu_balance_mode': None
}


RETRY_TIMES = 3
WAIT_SECONDS = 10


def gen_subtitle(video_path: str, output_dir: str = None, source_lang: str = None) -> tuple:
    """
    generate subtitle for a video
    put the file in same folder as video
    default to srt format
    """
    pf = Path(video_path)
    # output_format = '.txt'
    output_format = get_key(Fn.SUB_FORMAT)
    if not VersionManager.check_fn_value(Fn.SUB_FORMAT, output_format):
        logger.debug(f'function not support {Fn.SUB_FORMAT}={output_format}')
        output_format = 'srt'
    output_format = "."+output_format
    output_file = str(pf.with_suffix(output_format))
    if need_skip(output_file):
        logger.info(get_text('info.SkipFile').format(output_file))
        return output_file, source_lang
    # 开始时， 如果是覆盖方式， 删掉.old文件
    if not get_key('sub.need_skip'):
        old_file = pf.with_suffix('.old')
        if old_file.exists():
            logger.debug(f'remove old file: {old_file}')
            old_file.unlink()
    if not output_dir:
        output_dir = str(pf.parent)

    # 免费版提取10分钟音频, 其他版本提取全部
    if config.app_is_free():
        file_path = ff.extract_audio(video_path, 10 * 60)
    else:
        file_path = ff.extract_audio(video_path)
    logger.debug(f'generate wav file: {file_path}')

    # in whisper , None mean auto detect language
    if source_lang == 'auto':
        source_lang = detect_language(model, file_path)
    options = {}
    options['language'] = source_lang

    result = transcribe(model, file_path, options)
    language_code = result.language

    if language_code == 'zh':
        handle_chinese_result(result)
    write_result(output_file, result)

    logger.info(get_text('info.SaveSubFile').format(output_file))
    return output_file, language_code


def init_model(model_name: str):
    """
    do some initialization tasks
    like load model
    """
    global model, loaded_config

    # load global cfg
    cfg = config.get_sys_config()
    device = cfg.get('model')['device']
    cpu_balance_mode = cfg.get('model')['cpu_balance']
    compute_type = "auto"
    if device == 'auto' and checksys.check_gpu():
        device = 'cuda'
        # use compute_type = default mode for GPU
        compute_type = "default"
        logger.info(
            config.get_text("info.GPUIsEnabled"))
    else:
        device = 'cpu'
    logger.debug(f'{device=}, {compute_type=}')
        
    model_config = dict(device=device, model_name=model_name,
                        cpu_balance_mode=cpu_balance_mode)
    if not cpu_balance_mode:
        logger.info(
            config.get_text("info.CPURunAtFullSpeed"))

    # check if already loaded
    if model and model_config == loaded_config:
        return model

    # whispser device None mean auto detect device, cuda is prefered
    model_folder = get_file(config.MODEL_PATH)
    model_path = str(Path(model_folder).joinpath(model_name))
    logger.debug(get_text('info.LoadModel').format(model_path))
    model_text_name = get_text(f"model.{model_name}")
    logger.info(get_text('info.LoadModel').format(model_text_name))
    device_index = 0
    cpu_threads = calculate_cpu_count()
    try:
        model = WhisperModel(
            model_path,
            device=device,
            device_index=device_index,
            compute_type=compute_type,
            download_root=None,
            local_files_only=True,
            cpu_threads=cpu_threads
        )
        loaded_config.update(model_config)
        logger.debug(f'{loaded_config=}')
        # logger.debug(
        #     f'model suppored languages : {model.supported_languages}')
        return model
    except RuntimeError as e:
        logger.debug(f'exception: {e}')
        raise ModelLoadError(get_text('error.ModelLoadError'))


def get_translator(source_lang: str, target_lang: str, translator_name: str) -> BaseTranslator:
    # init model
    cfg = config.get_sys_config()
    proxy_config = cfg.get('proxy', None)
    translator_config = None

    if cfg.get('translate', None):
        translator_config = cfg['translate'].get(translator_name, None)

    if translator_config:
        translator_config = {
            **config.DEFAULT_TRANSLATOR_CONFIG, **translator_config}
    else:
        translator_config = config.DEFAULT_TRANSLATOR_CONFIG

    kwargs = {'proxy_config': proxy_config,
              'translator_config': translator_config,
              'source_lang': source_lang,
              'target_lang': target_lang,
              }
    logger.debug(kwargs)
    translator = all_translators[translator_name](**kwargs)
    return translator


def test_translator(translator_name: str) -> bool:
    '''
    测试翻译器
    根据当前保存的配置， 测试翻译api是否可用
    '''
    source_lang = 'en'
    target_lang = 'zh'
    test_text = get_text('translator.ApiTestText')
    test_answer = get_text('translator.ApiTestAnswer')
    translator = get_translator(source_lang, target_lang, translator_name)
    translated_text = translator.translate(
        test_text, source_lang, target_lang)
    logger.debug(translated_text)
    if test_answer in translated_text:
        return True

    return False


def translate_file(file_path: str, source_lang: str, target_lang: str, translator_name: str) -> str:
    """
    translate subtitle
    返回翻译后的字幕文件路径
    """

    pf = Path(file_path)
    output_file = str(pf.with_stem(pf.stem + '_' + target_lang))
    # 根据是否删除源字幕检查是否需要跳过
    delete_source = get_key('sub.delete_source')
    if delete_source:
        to_check_file = str(pf.with_suffix('.old'))
    else:
        to_check_file = output_file
    # 当有.old时， 直接跳过， 无论是否时覆盖选项, 因为无法覆盖
    if need_skip(to_check_file):
        logger.info(get_text('info.SkipFile').format(output_file))
        if delete_source:
            return file_path
        else:
            return to_check_file
    # srt = SrtFile(file_path, target_lang)
    ext = pf.suffix[1:]
    reader = SubFile.get_reader(ext)
    srt = reader(file_path, target_lang)
    if srt.is_empty():
        logger.debug('empty file, skip')
        return

    translator = get_translator(source_lang, target_lang, translator_name)
    translator_names_dict = config.get_translator_names_dict()
    logger.info(get_text("info.UseTranslator").format(
        translator_names_dict[translator_name]))
    logger.info(get_text("info.StartTranslate"))

    for i in range(RETRY_TIMES):
        try:
            srt.translate(translator, source_lang, target_lang)
            srt.save(output_file)
            srt.clean()
            translator.finished(output_file)

        except TranslationError as e:
            # try again
            # logger.debug(e)
            need_retry = e._args.get('need_retry', True)
            if (i < RETRY_TIMES - 1) and need_retry:
                logger.info(
                    get_text("error.TranslationErrorRetry").format(i+1, WAIT_SECONDS))
                time.sleep(WAIT_SECONDS)
            else:
                logger.debug(str(e))
                raise
        else:
            result_path = post_translate_process(
                file_path, source_lang, target_lang, output_file)
            logger.debug(f"after translation: result path is {result_path}")
            logger.info(
                get_text("info.TranslateSaveSubFile").format(result_path))
            return result_path


def post_translate_process(source_file: str, source_lang: str, target_lang: str, target_file: str) -> str:
    '''
    处理一些翻译完成后的工作
    比如删除源语言字幕， 重命名目标语言字幕
    返回最终翻译后文件路径
    '''
    delete_source = get_key('sub.delete_source')
    # delete_source = True时， 重命名原srt 为 .old 文件
    if delete_source:
        target_p = Path(target_file)
        source_p = Path(source_file)
        if source_p.exists():
            logger.debug("重命名原字慕为.old后缀")
            source_p.replace(source_p.with_suffix('.old'))
        if target_p.exists():
            logger.debug(f'重命名翻译字慕为原字幕名')
            target_p.replace(source_file)
        return source_file
    else:
        return target_file


def gen_subtitles(file_path: str, source_lang: str, target_lang: str, translator_name: Optional[str], model_name: str,
                  batch_subtitle=False, update_progress=None, auto_retry=False):
    """
    main function

    :param file_path: can be video file path , or dir
    """
    success_files = []
    failure_files = []
    retry_wait_time_start = 10
    if auto_retry:
        retry_times = 3
    else:
        retry_times = 1

    wait_before_trans = 2
    pf = Path(file_path)
    path_list = []
    if not pf.is_dir():
        path_list.append(file_path)
    else:
        if batch_subtitle:
            path_list = [str(p) for p in pf.rglob(
                '*') if p.suffix.lower() in ff.get_sub_formats()]
        else:
            path_list = [str(p) for p in pf.rglob(
                '*') if p.suffix.lower() in ff.all_formats()]
        path_list = make_filtered_list(path_list)
    # logger.debug(f'after filterd list: {path_list}')
    language_code = None
    trans_sub_path = None
    audio_path = None
    video_path = None
    audio_lang = None
    batch_process = False
    path_list.sort()
    total_tasks = len(path_list)
    if update_progress is not None:
        if total_tasks > 1:
            batch_process = True
            progress_info = (0, total_tasks)
            task_message = f"({progress_info[0]}/{progress_info[1]})"
            status_message = config.get_text(
                'info.StatusProcessTask').format(task_message)
            update_progress(status_message, progress_info)
        else:
            update_progress(get_text('info.StatusTaskStart'), (0, total_tasks))
    fn_update_progress = None if batch_process else update_progress
    set_share_value('update_progress', fn_update_progress)
    for i, path in enumerate(path_list, start=1):
        retry_wait_time = retry_wait_time_start
        for k in range(1, retry_times + 1):
            try:
                if is_task_stopped():
                    raise TaskStoppedError()
                if need_transcribe(path):
                    init_model(model_name)
                    logger.info(get_text("info.ProcessFile").format(path))
                    start = time.time()
                    sub_path, language_code = gen_subtitle(
                        path, source_lang=source_lang)
                    end = time.time()
                    logger.info(
                        get_text("info.TranscribeComplete").format(end - start))
                    if config.app_is_free():
                        logger.info(
                            get_text("info.FreeVersionNotice").format(10))
                else:
                    # logger.debug('handle subtitle')
                    sub_path = path
                audio_lang = language_code or source_lang
                if translator_name:
                    # 开始翻译
                    # wait for file write complete
                    time.sleep(wait_before_trans)
                    start = time.time()
                    trans_sub_path = translate_file(sub_path, language_code if language_code else source_lang,
                                                    target_lang, translator_name)
                    end = time.time()
                    logger.info(
                        get_text("info.TranslateComplete").format(end - start))
                    audio_lang = target_lang

                trans_sub_path = trans_sub_path or sub_path
                # 处理生成txt，转录任务生成原始语言txt， 翻译任务生成目标语言txt
                if config.fn_enabled(Fn.GENERATE_TXT):
                    txt_file = subutils.write_sub_to_txt(trans_sub_path)
                    logger.info(config.get_text(
                        'info.GenerateTxtSub').format(txt_file))

                # 非视频文件处理时（翻译或音频处理） 应该跳过语音合成， 视频合成
                if ff.is_video_file(path):
                    # 根据配置执行语音合成
                    # 不翻译时使用原始字幕

                    if need_make_audio():
                        logger.info(get_text("info.MediaMakeAudioStart"))
                        start = time.time()
                        # 检查是否跳过
                        audio_path = need_skip_audio(trans_sub_path)
                        if not audio_path:
                            audio_path = tts.make_audio(
                                trans_sub_path, audio_lang=audio_lang, update_progress=fn_update_progress)
                        end = time.time()
                        logger.info(
                            get_text("info.MediaAutoMakeAudio").format(audio_path))
                        logger.info(
                            get_text("info.MediaMakeAudioComplete").format(end - start))

                    # 根据配置执行视频合成
                    # 如果此项开启， 则嵌入字幕
                    if not need_make_video():
                        trans_sub_path = None

                    # 字幕或音频不为None ， 则说明要制作视频
                    if trans_sub_path or audio_path:
                        logger.info(get_text("info.MediaMakeVideoStart"))
                        # 检查是否需要使用双语字幕嵌入， 并已经生成双语字幕
                        merge_sub_path = need_use_merge_subtitle(trans_sub_path)
                        # 检查是否有音频并且有aligned 字幕
                        aligned_sub_path = check_aligned_sub(audio_path, trans_sub_path)
                        if aligned_sub_path and merge_sub_path:
                            # 当对齐字幕存在且需要用双语字幕时, 生成一个临时调整过时间轴得双语字幕
                            trans_sub_path = make_aligned_merge_sub(aligned_sub_path, merge_sub_path)
                        elif aligned_sub_path:
                            trans_sub_path = aligned_sub_path
                        start = time.time()
                        video_path = need_skip_video(path)
                        if not video_path:
                            video_path = ff.merge_video(
                                path, audio_path, trans_sub_path, update_progress=fn_update_progress)
                        end = time.time()
                        logger.info(
                            get_text("info.MediaAutoMakeVideo").format(video_path))
                        logger.info(
                            get_text("info.MediaMakeVideoComplete").format(end - start))

                # 更新进度信息
                progress_info = (i, total_tasks)
                task_message = f"({progress_info[0]}/{progress_info[1]})"
                status_message = config.get_text(
                    'info.StatusProcessTask').format(task_message)
                if update_progress is not None:
                    update_progress(status_message, progress_info)
            except TaskStoppedError:
                raise
            except Exception as e:
                # 重试

                if k < retry_times and check_need_retry(e):
                    logger.info(str(e))
                    logger.info(
                        get_text("error.SystemErrorTry").format(k+1, retry_wait_time))
                    time.sleep(retry_wait_time)
                    retry_wait_time *= 2

                else:
                    logger.info(str(e))
                    logger.info(
                        get_text("error.AfterRetrySkipFile").format(path))
                    fail_f = Path(path)
                    failure_files.append(fail_f.name)
                    break
            else:
                success_files.append(path)
                break
    # 返回运行报告
    report = {
        'total': total_tasks,
        'success': len(success_files),
        'fail': len(failure_files),
        'fail_files': failure_files
    }
    return report


def check_need_retry(ex: Exception) -> bool:
    '''
    检查一个exception 是否运行重试，
    并检查need_retry 参数设置
    '''
    retry_exceptions = (TranslationError,)
    if not isinstance(ex, retry_exceptions):
        return False
    need_retry = ex._args.get('need_retry', True)
    return need_retry


def filer_path(path: str, plist: list) -> bool:
    '''
    如果没有_， 返回True
    如果有_, 检查是否list中有对应文件
    '''
    pf = Path(path)
    name = pf.stem
    suffix = pf.suffix
    # 过滤相应的en1_output.mp3和en1_output.mp4生成文件
    if '_output' in name:
        return False
    # 过滤可能的批量字幕翻译， en1.srt, en1_zh.srt同时出现的情况
    # 这种情况下， 只检测字幕相关文件的下划线
    sub_formats = ff.get_sub_formats()
    # logger.debug(f'{sub_formats=}')
    # 不是字幕文件直接返回True， 可以用下划线
    if not suffix in sub_formats:
        return True
    if '_' in name:
        left, right = name.rsplit('_', 1)
        # 检查是否右边为语言代码或merge， 是返回False
        if config.is_lang_code(right) or right == "merge":
            return False
        else:
            return True
    else:
        return True


def make_filtered_list(path_list):
    '''
    返回过滤后的字幕文件列表， 去掉已翻译的文件
    '''
    final_list = []
    for p in path_list:
        if filer_path(p, final_list):
            final_list.append(p)

    return final_list


def need_transcribe(filepath: str) -> bool:
    '''
    检查是否为字幕文件， 则不需要转录处理

    Args:
    filepath: 检查的目标文件

    Returns:
        True - need transcribe
        Flase - need skip
    '''
    pf = Path(filepath)
    sub_formats = ff.get_sub_formats()
    if pf.suffix.lower() in sub_formats:
        return False
    else:
        return True


def need_skip(filepath: str) -> bool:
    '''
    检查是否已有相关文件， 并检查选项"覆盖生成是否开启"

    Args:
        filepath: 检查的目标文件

    Returns:
        True - need skip
        Flase - not skip

    '''
    pf = Path(filepath)
    if pf.exists() and pf.suffix == '.old':
        return True
    if pf.exists() and get_key('sub.need_skip'):
        return True
    return False


def need_make_audio() -> bool:
    '''
    检查选项"自动合成音频"

    Args:

    Returns:
        True - 自动生成
        Flase - 不生成

    '''

    if VersionManager.check_fn(Fn.MEDIA_AUTO_AUDIO):
        return get_key(Fn.MEDIA_AUTO_AUDIO)
    return False


def need_skip_audio(srt_path):
    '''
    检查是否需要跳过音频生成
    '''
    pf = Path(srt_path)
    pf = pf.with_stem(f'{pf.stem}_output')
    pf = pf.with_suffix(".mp3")
    audio_path = str(pf)
    if need_skip(audio_path):
        logger.info(get_text('info.SkipFile').format(audio_path))
        return audio_path
    else:
        return ""


def need_skip_video(video_path):
    pf = Path(video_path)
    pf = pf.with_stem(f'{pf.stem}_output')
    pf = pf.with_suffix(".mp4")
    output_path = str(pf)
    if need_skip(output_path):
        logger.info(get_text('info.SkipFile').format(output_path))
        return output_path
    else:
        return ""


def need_make_video() -> bool:
    '''
    检查选项"自动合成视频"

    Args:

    Returns:
        True - 自动生成
        Flase - 不生成

    '''

    if VersionManager.check_fn(Fn.MEDIA_AUTO_VIDEO):
        return get_key(Fn.MEDIA_AUTO_VIDEO)
    return False


def need_use_merge_subtitle(sub_path) -> str:
    '''
    检查是否需要使用双语字幕嵌入
    并且双语字幕以及生成
    returns: str
    返回merge文件路径或None
    '''
    use_merge_subtitle = get_key(Fn.USE_MERGE_SUBTITLE)
    if use_merge_subtitle:
        pf = Path(sub_path)
        pf_merge = pf.with_stem(pf.stem.rsplit('_', 1)[0] + '_' + 'merge')
        if pf_merge.exists():
            logger.debug(f'need use merge subtitle: {pf_merge}')
            return str(pf_merge)
    return None

def check_aligned_sub(audio_path, trans_sub_path):
    '''
    检查是否生成音频
    并且有aligned字幕生成
    '''
    if audio_path:
        p = Path(audio_path)
        srt_p = Path(trans_sub_path)
        name = p.stem
        if name.endswith('_output'):
            aligned_name = name[:-7] + '_aligned'
            aligned_path = srt_p.with_stem(aligned_name)
            logger.debug(f'found aligned path: {aligned_path}')
            return str(aligned_path) if aligned_path.exists() else None


def init_sys():
    '''
    init system related funcions, language config, log config...
    '''
    import warnings
    warnings.filterwarnings("ignore")
    config.read_lang_config()
    ff.set_video_formats()
    init_all_translators()
    init_verson_manage()

init_sys()