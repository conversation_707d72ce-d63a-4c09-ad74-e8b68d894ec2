"""
实时字幕处理的核心接口

提供统一的 realtime_trans 函数作为主要入口点，以及新的异步接口。
"""

import os
import time
import threading
from pathlib import Path
from typing import Optional, Callable, Dict, Any

from v2subpy.utils.log import logger
try:
    from .models import RealtimeConfig
except ImportError:
    # 如果旧的models不存在，使用新的配置
    RealtimeConfig = None
try:
    from .exceptions import RealtimeProcessingError, ConfigurationError
except ImportError:
    # 使用新的异常处理
    from .utils.error_handling import RealtimeProcessingError, ConfigurationError
from .async_core.task_manager import global_task_manager
from .async_core.callbacks import create_callbacks_from_functions


def realtime_trans_async(
    video_path: str,
    config: Optional[Dict[str, Any]] = None,
    on_subtitle_ready: Optional[Callable[[str, dict], None]] = None,
    on_progress_update: Optional[Callable[[str, dict], None]] = None,
    on_error: Optional[Callable[[str, dict], None]] = None,
    on_finished: Optional[Callable[[str, dict], None]] = None
) -> str:
    """
    异步实时翻译接口（立即返回任务ID）
    
    Args:
        video_path: 视频文件路径
        config: 配置参数字典
        on_subtitle_ready: 字幕段准备就绪回调函数 callback(task_id, subtitle_dict)
        on_progress_update: 进度更新回调函数 callback(task_id, progress_dict)
        on_error: 错误回调函数 callback(task_id, error_dict)
        on_finished: 处理完成回调函数 callback(task_id, result_dict)
        
    Returns:
        str: 任务ID，用于后续控制
        
    Raises:
        FileNotFoundError: 视频文件不存在
        ConfigurationError: 配置参数无效
        RealtimeProcessingError: 处理过程中的其他错误
    """
    logger.info(f"启动异步实时翻译处理: {video_path}")
    
    try:
        # 验证视频文件
        _validate_video_file(video_path)
        
        # 创建任务
        task_id = global_task_manager.create_task(video_path, config)
        
        # 创建回调函数字典
        callbacks = {
            'on_subtitle_ready': on_subtitle_ready,
            'on_progress_update': on_progress_update,
            'on_error': on_error,
            'on_finished': on_finished
        }
        
        # 启动任务
        success = global_task_manager.start_task(task_id, callbacks)
        
        if success:
            logger.info(f"异步实时翻译任务启动成功: {task_id}")
            return task_id
        else:
            raise RealtimeProcessingError(f"启动任务失败: {task_id}")
            
    except Exception as e:
        logger.error(f"异步实时翻译处理失败: {e}")
        raise


def realtime_trans(
    video_path: str, 
    config: Optional[Dict[str, Any]] = None,
    on_subtitle_ready: Optional[Callable[[dict], None]] = None,
    on_progress_update: Optional[Callable[[str, tuple], None]] = None,
    on_error: Optional[Callable[[str], None]] = None,
    on_finished: Optional[Callable[[], None]] = None
) -> None:
    """
    统一的实时翻译接口（同步，向后兼容）
    
    Args:
        video_path: 视频文件路径
        config: 配置参数字典
        on_subtitle_ready: 字幕段准备就绪回调函数 callback(subtitle_dict)
        on_progress_update: 进度更新回调函数 callback(status_message, progress_info)
        on_error: 错误回调函数 callback(error_message)
        on_finished: 处理完成回调函数 callback()
        
    Raises:
        FileNotFoundError: 视频文件不存在
        ConfigurationError: 配置参数无效
        RealtimeProcessingError: 处理过程中的其他错误
    """
    logger.info(f"启动同步实时翻译处理: {video_path}")
    
    # 使用事件来等待完成
    finished_event = threading.Event()
    error_result = {'error': None}
    
    def wrapped_subtitle_ready(task_id: str, subtitle_dict: dict):
        if on_subtitle_ready:
            on_subtitle_ready(subtitle_dict)
    
    def wrapped_progress_update(task_id: str, progress_dict: dict):
        if on_progress_update:
            # 转换为旧格式
            message = progress_dict.get('message', '')
            progress_info = progress_dict.get('progress_info', (0, 0))
            on_progress_update(message, progress_info)
    
    def wrapped_error(task_id: str, error_dict: dict):
        error_result['error'] = error_dict
        finished_event.set()
        if on_error:
            # 转换为旧格式
            import json
            error_message = json.dumps(error_dict, ensure_ascii=False)
            on_error(error_message)
    
    def wrapped_finished(task_id: str, result_dict: dict):
        finished_event.set()
        if on_finished:
            on_finished()
    
    try:
        # 调用异步版本
        task_id = realtime_trans_async(
            video_path=video_path,
            config=config,
            on_subtitle_ready=wrapped_subtitle_ready,
            on_progress_update=wrapped_progress_update,
            on_error=wrapped_error,
            on_finished=wrapped_finished
        )
        
        # 等待完成
        logger.debug(f"等待任务完成: {task_id}")
        finished_event.wait()
        
        # 检查是否有错误
        if error_result['error']:
            error_dict = error_result['error']
            error_message = error_dict.get('message', 'Unknown error')
            raise RealtimeProcessingError(error_message)
            
        logger.info(f"同步实时翻译处理完成: {task_id}")
        
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        # 尝试停止任务
        try:
            if 'task_id' in locals():
                global_task_manager.stop_task(task_id)
        except:
            pass
        raise
    except Exception as e:
        logger.error(f"同步实时翻译处理失败: {e}")
        raise


def _validate_video_file(video_path: str) -> None:
    """验证视频文件是否存在且可访问"""
    if not video_path:
        raise ConfigurationError("视频文件路径不能为空")
        
    video_file = Path(video_path)
    if not video_file.exists():
        raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
    if not video_file.is_file():
        raise ConfigurationError(f"路径不是有效的文件: {video_path}")
        
    # 检查文件是否可读
    if not os.access(video_path, os.R_OK):
        raise ConfigurationError(f"无法读取视频文件: {video_path}")


def _create_config(config_dict: Dict[str, Any]):
    """创建并验证配置对象"""
    try:
        if RealtimeConfig:
            # 使用旧的配置系统
            realtime_config = RealtimeConfig.from_dict(config_dict)
            realtime_config.validate()
            logger.debug(f"实时翻译配置: {realtime_config.to_dict()}")
            return realtime_config
        else:
            # 使用新的配置系统
            from .async_core.models import TranslationConfig
            translation_config = TranslationConfig.from_dict(config_dict)
            logger.debug(f"实时翻译配置: {translation_config.to_dict()}")
            return translation_config
    except Exception as e:
        raise ConfigurationError(f"配置参数无效: {e}")


# 任务控制函数
def stop_realtime_task(task_id: str) -> bool:
    """
    停止实时翻译任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        bool: 是否成功停止
    """
    return global_task_manager.stop_task(task_id)


def pause_realtime_task(task_id: str) -> bool:
    """
    暂停实时翻译任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        bool: 是否成功暂停
    """
    return global_task_manager.pause_task(task_id)


def resume_realtime_task(task_id: str) -> bool:
    """
    恢复实时翻译任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        bool: 是否成功恢复
    """
    return global_task_manager.resume_task(task_id)


def get_realtime_task_status(task_id: str) -> Optional[Dict[str, Any]]:
    """
    获取实时翻译任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        Optional[Dict[str, Any]]: 任务状态字典，如果任务不存在则返回None
    """
    return global_task_manager.get_task_status(task_id)


def list_realtime_tasks() -> Dict[str, Dict[str, Any]]:
    """
    列出所有实时翻译任务
    
    Returns:
        Dict[str, Dict[str, Any]]: 任务ID到状态字典的映射
    """
    return global_task_manager.list_tasks()


# 为了向后兼容，提供一个简化的接口
def start_realtime_translation(video_path: str, **kwargs) -> None:
    """
    简化的实时翻译启动接口（同步）
    
    Args:
        video_path: 视频文件路径
        **kwargs: 其他参数，包括配置和回调函数
    """
    return realtime_trans(video_path, **kwargs)


def start_realtime_translation_async(video_path: str, **kwargs) -> str:
    """
    简化的异步实时翻译启动接口
    
    Args:
        video_path: 视频文件路径
        **kwargs: 其他参数，包括配置和回调函数
        
    Returns:
        str: 任务ID
    """
    return realtime_trans_async(video_path, **kwargs)