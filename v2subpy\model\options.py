'''
提供一些初始化参数, 比如initial_prompt
'''

from v2subpy.utils.log import logger

def load_initial_prompt(language_code: str | None) -> dict :
    '''
    根据语言提供相应prompt
    '''
    prompt_dict = {
        "en" : "Add punctuation at the end of each line, and punctuation at the end of each sentence.",
        "zh" : "你好，这是第一句话。这是第二个。稍稍停顿一下……我们回来了。",
        "ja" : "こんにちは、最初の文です。そしてこれが2つ目です。少し休憩してください...そして戻ってきます。",
        "yue": "你好，這是第一句話。 這是第二個。 稍作停頓... 我哋返嚟喇."  ,
        "fr": "Bonjour, c'est la première phrase. Et c'est le deuxième. Une petite pause... et nous voilà de retour.",
        "ko":"안녕하세요, 첫문장입니다. 그리고 이것이 두 번째입니다. 잠시 쉬고... 그리고 우리는 돌아왔습니다.",
        "de": "<PERSON><PERSON>, das ist der erste Satz. Und das ist der zweite. Eine kleine Pause... und wir sind zurück.",
        "ru": "Здравствуйте, это первое предложение. И это второй. Небольшая пауза... и мы вернулись.",
        "es": "Hola, esta es la primera frase. Y este es el segundo. Una pequeña pausa... y volvemos.",
        "pt": "Olá, esta é a primeira frase. E este é o segundo. Uma pequena pausa... e estamos de volta.",
        "it": "Ciao, questa è la prima frase. E questo è il secondo. Una piccola pausa... e siamo tornati.",
        "tr": "Merhaba, bu ilk cümlem. Ve bu ikincisi. Biraz ara verdik... ve geri döndük.",
        "cs": "Dobrý den, toto je první věta. A tohle je ta druhá. Malá pauza... a jsme zpět.",
        "hi": "नमस्ते, यह पहला वाक्य है. और ये दूसरा है. थोड़ा रुकें... और हम वापस आ गए।",
        "uk": "Привіт, це перше речення. А це вже другий. Невелика пауза... і ми повернулися.",
        "th": "สวัสดี นี่คือประโยคแรก และนี่คืออันที่สอง พักสักหน่อย...ก็กลับมาแล้ว",
        "vi": "Xin chào, đây là câu đầu tiên. Và đây là cái thứ hai. Tạm dừng một chút... và chúng tôi sẽ quay lại.",
        "ms": "Hello, ini ayat pertama. Dan ini adalah yang kedua. Jeda sedikit... dan kami kembali.",
        "pl": "Witam, to jest pierwsze zdanie. A to jest drugie. Mała przerwa... i wracamy.",
        "nl": "Hallo, dit is de eerste zin. En dit is de tweede. Een korte pauze... en we zijn terug.",
        "ar": "مرحبا، هذه هي الجملة الأولى. وهذا هو الثاني. وقفة بسيطة...وقد عدنا.",
        "sv": "Hej, det här är den första meningen. Och det här är den andra. En liten paus... och vi är tillbaka.",
        "id": "Halo, ini kalimat pertama. Dan ini yang kedua. Jeda sebentar... dan kami kembali.",
        "fi": "Hei, tämä on ensimmäinen lause. Ja tämä on toinen. Pieni tauko... ja olemme takaisin.",
        "el": "Γεια σας, αυτή είναι η πρώτη πρόταση. Και αυτό είναι το δεύτερο. Μια μικρή παύση... και επιστρέφουμε.",
    }
    default_prompt = None
    prompt = prompt_dict.get(language_code, default_prompt)
    logger.debug(f'{prompt=}')
    return {'initial_prompt': prompt}
