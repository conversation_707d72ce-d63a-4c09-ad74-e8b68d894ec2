# 实时字幕播放器设计文档

## 概述

本设计文档描述了为 v2sub 项目开发实时字幕播放器功能的技术架构和实现方案。该功能将允许用户在视频播放的同时实时生成和显示翻译字幕，无需等待完整处理流程完成。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[用户界面层] --> B[播放器控制层]
    A --> C[字幕显示层]
    B --> D[媒体播放引擎]
    B --> E[实时处理管道]
    E --> F[音频分段器]
    E --> G[语音识别服务]
    E --> H[翻译服务]
    E --> I[字幕缓冲区]
    I --> C
    J[配置管理] --> E
    K[异常处理器] --> E
    L[文件管理器] --> E
```

### 核心组件

#### 1. 实时播放器窗口 (RealtimePlayerWindow)
- 继承自现有的 QMainWindow 架构
- 集成视频播放控件和字幕显示区域
- 提供播放控制界面（播放/暂停/进度条/音量等）
- 管理播放器状态和用户交互

#### 2. 实时处理管道 (RealtimeProcessingPipeline)
- 协调音频提取、语音识别、翻译等处理流程
- 管理多线程处理和任务调度
- 处理异常情况和自动重试逻辑
- 维护处理状态和进度信息

#### 3. 音频分段器 (AudioSegmenter)
- 从视频文件实时提取音频流
- 将音频分割成适合处理的时间段（5-10秒）
- 处理音频格式转换和预处理
- 管理音频缓冲区和内存使用

#### 4. 字幕缓冲管理器 (SubtitleBufferManager)
- 管理原始字幕和翻译字幕的缓冲区
- 处理字幕时间轴同步
- 提供字幕查询和获取接口
- 处理字幕片段的合并和优化

#### 5. 媒体播放引擎 (MediaPlaybackEngine)
- 基于 libmpv (python-mpv) 的视频播放
- 支持广泛的视频格式和编解码器
- 提供毫秒级精确的时间控制和同步
- 优秀的硬件加速和性能表现
- 原生字幕渲染支持

## 组件接口设计

### RealtimePlayerWindow 接口

```python
class RealtimePlayerWindow(QMainWindow):
    def __init__(self, parent=None):
        """初始化实时播放器窗口"""
        
    def load_video(self, video_path: str) -> bool:
        """加载视频文件"""
        
    def start_realtime_processing(self) -> None:
        """启动实时处理流程"""
        
    def play(self) -> None:
        """开始播放"""
        
    def pause(self) -> None:
        """暂停播放"""
        
    def seek(self, position: int) -> None:
        """跳转到指定位置（毫秒）"""
        
    def set_volume(self, volume: int) -> None:
        """设置音量（0-100）"""
        
    def toggle_fullscreen(self) -> None:
        """切换全屏模式"""
```

### RealtimeProcessingPipeline 接口

```python
class RealtimeProcessingPipeline(QObject):
    # 信号定义
    subtitle_ready = Signal(dict)  # 字幕准备就绪
    processing_error = Signal(str)  # 处理错误
    buffer_status_changed = Signal(int)  # 缓冲状态变化
    
    def __init__(self, config: dict):
        """初始化处理管道"""
        
    def start_processing(self, video_path: str) -> None:
        """开始处理流程"""
        
    def stop_processing(self) -> None:
        """停止处理流程"""
        
    def get_subtitle_at_time(self, timestamp: int) -> dict:
        """获取指定时间的字幕"""
        
    def set_translation_config(self, config: dict) -> None:
        """设置翻译配置"""
```

### MediaPlaybackEngine 接口

```python
class MediaPlaybackEngine(QObject):
    # 信号定义
    position_changed = Signal(float)  # 播放位置变化
    duration_changed = Signal(float)  # 视频时长变化
    playback_finished = Signal()      # 播放完成
    error_occurred = Signal(str)      # 播放错误
    
    def __init__(self, parent_widget: QWidget):
        """初始化 mpv 播放引擎"""
        
    def load_file(self, file_path: str) -> bool:
        """加载视频文件"""
        
    def play(self) -> None:
        """开始播放"""
        
    def pause(self) -> None:
        """暂停播放"""
        
    def seek(self, position: float) -> None:
        """跳转到指定位置（秒）"""
        
    def get_position(self) -> float:
        """获取当前播放位置"""
        
    def set_volume(self, volume: int) -> None:
        """设置音量（0-100）"""
        
    def set_subtitle_text(self, text: str, duration: float) -> None:
        """设置字幕文本"""
```

### AudioSegmenter 接口

```python
class AudioSegmenter(QObject):
    segment_ready = Signal(dict)  # 音频段准备就绪
    
    def __init__(self, segment_duration: int = 8):
        """初始化音频分段器"""
        
    def start_segmentation(self, video_path: str) -> None:
        """开始音频分段"""
        
    def get_next_segment(self) -> dict:
        """获取下一个音频段"""
```

## 数据模型

### 字幕数据模型

```python
@dataclass
class SubtitleSegment:
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    original_text: str # 原始文本
    translated_text: str = ""  # 翻译文本
    confidence: float = 0.0    # 识别置信度
    segment_id: str = ""       # 段落ID
    status: str = "pending"    # 状态：pending/processing/completed/error
```

### 音频段数据模型

```python
@dataclass
class AudioSegment:
    segment_id: str    # 段落ID
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    audio_data: bytes  # 音频数据
    sample_rate: int   # 采样率
    channels: int      # 声道数
```

### 处理状态模型

```python
@dataclass
class ProcessingStatus:
    current_segment: int     # 当前处理段落
    total_segments: int      # 总段落数
    buffer_level: int        # 缓冲区水平（0-100）
    processing_speed: float  # 处理速度倍率
    error_count: int         # 错误计数
    last_error: str = ""     # 最后错误信息
```

## 错误处理策略

### 异常类型定义

```python
class RealtimeProcessingError(Exception):
    """实时处理基础异常"""
    pass

class AudioExtractionError(RealtimeProcessingError):
    """音频提取异常"""
    pass

class SpeechRecognitionError(RealtimeProcessingError):
    """语音识别异常"""
    pass

class TranslationError(RealtimeProcessingError):
    """翻译服务异常"""
    pass

class BufferUnderrunError(RealtimeProcessingError):
    """缓冲区不足异常"""
    pass
```

### 错误处理流程

1. **网络异常处理**
   - 自动重试机制（最多3次）
   - 指数退避策略
   - 降级到离线模式

2. **缓冲区管理**
   - 监控缓冲区水平
   - 动态调整处理优先级
   - 自动暂停/恢复播放

3. **服务异常处理**
   - 翻译服务故障时显示原始字幕
   - 语音识别失败时跳过该段
   - 记录详细错误日志

## 测试策略

### 单元测试

1. **音频分段测试**
   - 测试不同格式视频的音频提取
   - 验证分段时间准确性
   - 测试内存使用和性能

2. **字幕缓冲测试**
   - 测试缓冲区的读写操作
   - 验证时间轴同步准确性
   - 测试并发访问安全性

3. **异常处理测试**
   - 模拟网络中断场景
   - 测试服务超时处理
   - 验证自动重试机制

### 集成测试

1. **端到端流程测试**
   - 完整的视频播放和字幕生成流程
   - 不同视频格式和时长的兼容性
   - 多种翻译引擎的集成测试

2. **性能测试**
   - 长时间播放的稳定性测试
   - 内存泄漏检测
   - CPU和内存使用率监控

3. **用户体验测试**
   - 播放控制响应性测试
   - 字幕显示延迟测试
   - 界面交互流畅性测试

### 压力测试

1. **高负载测试**
   - 同时处理多个音频段
   - 大文件和长视频处理
   - 系统资源限制下的表现

2. **异常场景测试**
   - 频繁的网络中断
   - 翻译服务高延迟
   - 系统资源不足情况

## 性能优化

### 内存管理

1. **音频数据管理**
   - 使用循环缓冲区减少内存分配
   - 及时释放已处理的音频段
   - 限制同时在内存中的段数

2. **字幕缓存优化**
   - LRU缓存策略
   - 压缩存储长期不用的字幕
   - 预加载机制

### 处理优化

1. **并行处理**
   - 音频提取和语音识别并行
   - 多个翻译请求并发处理
   - 异步I/O操作

2. **智能调度**
   - 根据缓冲区状态调整处理优先级
   - 动态调整处理线程数
   - 预测性处理

### 网络优化

1. **请求优化**
   - 批量翻译请求
   - 连接池管理
   - 请求去重

2. **缓存策略**
   - 翻译结果本地缓存
   - 智能预取
   - 离线模式支持

## 安全考虑

### 数据安全

1. **敏感信息保护**
   - API密钥安全存储
   - 音频数据加密传输
   - 临时文件安全清理

2. **网络安全**
   - HTTPS通信
   - 证书验证
   - 请求签名验证

### 隐私保护

1. **数据处理**
   - 最小化数据收集
   - 本地处理优先
   - 用户数据控制权

2. **存储管理**
   - 临时文件自动清理
   - 用户选择数据保留策略
   - 安全删除机制

## 部署和配置

### 配置管理

1. **播放器配置**
   - 默认音频分段长度
   - 缓冲区大小设置
   - 显示偏好设置
   - mpv 播放器选项配置

2. **处理配置**
   - 翻译引擎选择
   - 重试策略参数
   - 性能调优参数

### libmpv 部署考虑

1. **库文件分发**
   - Windows: 包含 mpv-1.dll 和相关依赖
   - macOS: 包含 libmpv.dylib
   - Linux: 系统包管理器安装或静态链接

2. **打包策略**
   - 使用 PyInstaller 时需要特殊处理 libmpv
   - 确保所有 mpv 依赖库都被正确包含
   - 测试不同平台的库加载

### 依赖管理

1. **新增依赖**
   - python-mpv (视频播放引擎)
   - libmpv (底层播放库)
   - asyncio (异步处理)
   - concurrent.futures (线程池)

2. **可选依赖**
   - ffmpeg-python (音频处理增强)
   - pillow (字幕渲染优化)

## 与现有系统集成

### 配置系统集成

- 复用现有的 `v2subqt.config` 配置管理
- 扩展配置项支持播放器设置
- 保持与批处理模式的配置兼容性

### UI系统集成

- 在主窗口添加"实时播放"入口
- 复用现有的翻译器配置界面
- 保持界面风格一致性

### 后端系统集成

- 复用现有的 faster-whisper 集成
- 复用现有的翻译引擎接口
- 扩展 worker_thread 支持实时处理

这个设计确保了新功能与现有系统的无缝集成，同时提供了强大的实时字幕处理能力。