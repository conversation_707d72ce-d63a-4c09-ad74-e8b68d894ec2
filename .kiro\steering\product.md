# Product Overview

v2subpy is an AI-powered video-to-subtitle tool that provides both batch processing and real-time subtitle generation capabilities.

## Core Features

- **Video Transcription**: Extract audio from videos and generate subtitles using Whisper models
- **Multi-language Translation**: Support for multiple translation engines (OpenAI, Google, Baidu, DeepL)
- **Real-time Processing**: Live subtitle generation during video playback
- **Batch Processing**: Handle multiple videos in directories
- **Format Support**: Wide range of video formats and subtitle formats (SRT, TXT, etc.)
- **Audio Synthesis**: Text-to-speech generation for translated subtitles
- **Video Merging**: Embed subtitles and audio into output videos

## Target Users

- Content creators needing multilingual subtitles
- Developers integrating real-time subtitle functionality
- Users requiring batch video processing workflows
- Applications needing embedded subtitle solutions

## Key Value Propositions

- High-quality AI transcription using Whisper models
- Multiple translation engine support for accuracy
- Real-time processing for live applications
- Comprehensive API for integration into other applications
- Free tier with 10-minute processing limit