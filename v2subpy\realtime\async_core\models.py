"""
V2subpy异步核心数据模型

定义任务状态、配置和任务类等核心数据结构。
"""

import threading
import uuid
from concurrent.futures import Future
from enum import Enum
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, field


class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"


@dataclass
class TranslationConfig:
    """翻译配置类"""
    # Whisper模型配置
    whisper_model: str = "small"
    device: str = "auto"
    cpu_balance: int = 4
    
    # 翻译配置
    target_language: str = "zh"
    translation_engine: str = "default"
    
    # 音频处理配置
    audio_segment_length: float = 10.0
    audio_overlap: float = 1.0
    segment_duration: float = 10.0  # 兼容旧配置
    segment_overlap: float = 1.0    # 兼容旧配置
    buffer_size: int = 10
    
    # 其他配置
    max_retries: int = 3
    timeout: float = 30.0
    retry_delay: float = 1.0
    max_concurrent_segments: int = 2
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "whisper_model": self.whisper_model,
            "device": self.device,
            "cpu_balance": self.cpu_balance,
            "target_language": self.target_language,
            "translation_engine": self.translation_engine,
            "audio_segment_length": self.audio_segment_length,
            "audio_overlap": self.audio_overlap,
            "segment_duration": self.segment_duration,
            "segment_overlap": self.segment_overlap,
            "buffer_size": self.buffer_size,
            "max_retries": self.max_retries,
            "timeout": self.timeout,
            "retry_delay": self.retry_delay,
            "max_concurrent_segments": self.max_concurrent_segments
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TranslationConfig':
        """从字典创建配置"""
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})


@dataclass
class TaskInfo:
    """任务信息类"""
    task_id: str
    video_path: str
    config: TranslationConfig
    status: TaskStatus = TaskStatus.CREATED
    created_at: float = field(default_factory=lambda: __import__('time').time())
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    progress: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "video_path": self.video_path,
            "config": self.config.to_dict(),
            "status": self.status.value,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "error_message": self.error_message,
            "progress": self.progress
        }


class TranslationTask:
    """翻译任务类"""
    
    def __init__(self, task_id: str, video_path: str, config: TranslationConfig):
        self.task_id = task_id
        self.video_path = video_path
        self.config = config
        self.info = TaskInfo(task_id, video_path, config)
        
        # 控制信号
        self.stop_requested = threading.Event()
        self.pause_requested = threading.Event()
        self.resume_requested = threading.Event()
        
        # 处理相关
        self.pipeline = None
        self.future: Optional[Future] = None
        self.callbacks: Optional[Dict[str, Callable]] = None
        
        # 线程安全锁
        self._lock = threading.RLock()
    
    def set_callbacks(self, callbacks: Dict[str, Callable]):
        """设置回调函数"""
        with self._lock:
            self.callbacks = callbacks
    
    def set_future(self, future: Future):
        """设置Future对象"""
        with self._lock:
            self.future = future
    
    def set_pipeline(self, pipeline):
        """设置处理管道"""
        with self._lock:
            self.pipeline = pipeline
    
    def update_status(self, status: TaskStatus, error_message: Optional[str] = None):
        """更新任务状态"""
        with self._lock:
            self.info.status = status
            if error_message:
                self.info.error_message = error_message
            
            if status == TaskStatus.RUNNING and not self.info.started_at:
                self.info.started_at = __import__('time').time()
            elif status in [TaskStatus.COMPLETED, TaskStatus.ERROR, TaskStatus.CANCELLED]:
                self.info.completed_at = __import__('time').time()
    
    def update_progress(self, progress: float):
        """更新任务进度"""
        with self._lock:
            self.info.progress = max(0.0, min(1.0, progress))
    
    def request_stop(self):
        """请求停止任务"""
        self.stop_requested.set()
        if self.pipeline:
            self.pipeline.stop_processing()
    
    def request_pause(self):
        """请求暂停任务"""
        self.pause_requested.set()
        if self.pipeline:
            self.pipeline.pause_processing()
    
    def request_resume(self):
        """请求恢复任务"""
        self.pause_requested.clear()
        self.resume_requested.set()
        if self.pipeline:
            self.pipeline.resume_processing()
    
    def get_status(self) -> Dict[str, Any]:
        """获取任务状态信息"""
        with self._lock:
            return self.info.to_dict()
    
    def is_finished(self) -> bool:
        """检查任务是否已完成"""
        with self._lock:
            return self.info.status in [
                TaskStatus.COMPLETED, 
                TaskStatus.ERROR, 
                TaskStatus.CANCELLED
            ]
    
    def is_running(self) -> bool:
        """检查任务是否正在运行"""
        with self._lock:
            return self.info.status == TaskStatus.RUNNING
    
    def is_paused(self) -> bool:
        """检查任务是否已暂停"""
        with self._lock:
            return self.info.status == TaskStatus.PAUSED


def generate_task_id() -> str:
    """生成唯一的任务ID"""
    return f"task_{uuid.uuid4().hex[:8]}"