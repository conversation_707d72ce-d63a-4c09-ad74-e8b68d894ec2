"""
V2subpy通用集成接口

提供框架无关的异步翻译接口，可以被各种GUI框架和应用程序使用。
"""

import threading
from typing import Dict, Any, Optional, Callable, List
import logging

from ..async_core.task_manager import global_task_manager
from ..async_core.models import TranslationConfig, TaskStatus
from ..async_core.callbacks import TranslationCallbacks, FunctionCallbacks
from ..utils.error_handling import V2subpyError, TaskNotFoundError

logger = logging.getLogger(__name__)


class AsyncRealtimeTranslator:
    """通用异步实时翻译器"""
    
    def __init__(self, max_concurrent_tasks: int = 2):
        """
        初始化异步翻译器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        logger.info(f"初始化异步实时翻译器，最大并发任务数: {max_concurrent_tasks}")
    
    def start_translation(self, 
                         video_path: str, 
                         config: Optional[Dict[str, Any]] = None,
                         callbacks: Optional[Dict[str, Callable]] = None) -> str:
        """
        启动翻译任务
        
        Args:
            video_path: 视频文件路径
            config: 翻译配置字典
            callbacks: 回调函数字典，包含：
                - on_subtitle_ready: 字幕准备就绪回调 callback(task_id, subtitle_data)
                - on_progress_update: 进度更新回调 callback(task_id, progress_data)
                - on_error: 错误回调 callback(task_id, error_data)
                - on_finished: 完成回调 callback(task_id, result_data)
        
        Returns:
            str: 任务ID
        
        Raises:
            V2subpyError: 启动失败
        """
        with self._lock:
            # 检查并发任务数量限制
            running_count = len([t for t in self.active_tasks.values() 
                               if t.get('status') == TaskStatus.RUNNING.value])
            
            if running_count >= self.max_concurrent_tasks:
                raise V2subpyError(
                    f"已达到最大并发任务数限制 ({self.max_concurrent_tasks})",
                    error_code="MAX_CONCURRENT_TASKS_REACHED"
                )
        
        try:
            # 创建任务
            task_id = global_task_manager.create_task(video_path, config or {})
            
            # 包装回调函数以更新本地状态
            wrapped_callbacks = self._wrap_callbacks(task_id, callbacks or {})
            
            # 启动任务
            success = global_task_manager.start_task(task_id, wrapped_callbacks)
            
            if success:
                with self._lock:
                    self.active_tasks[task_id] = {
                        'video_path': video_path,
                        'config': config or {},
                        'status': TaskStatus.RUNNING.value,
                        'callbacks': callbacks or {}
                    }
                
                logger.info(f"启动翻译任务成功: {task_id}")
                return task_id
            else:
                raise V2subpyError(f"启动任务失败: {task_id}")
                
        except Exception as e:
            logger.error(f"启动翻译任务失败: {e}")
            if isinstance(e, V2subpyError):
                raise
            else:
                raise V2subpyError(f"启动翻译任务失败: {e}")
    
    def stop_translation(self, task_id: str) -> bool:
        """
        停止翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功停止
        """
        try:
            success = global_task_manager.stop_task(task_id)
            
            if success:
                with self._lock:
                    if task_id in self.active_tasks:
                        self.active_tasks[task_id]['status'] = TaskStatus.CANCELLED.value
                
                logger.info(f"停止翻译任务成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"停止翻译任务失败: {task_id}, {e}")
            return False
    
    def pause_translation(self, task_id: str) -> bool:
        """
        暂停翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功暂停
        """
        try:
            success = global_task_manager.pause_task(task_id)
            
            if success:
                with self._lock:
                    if task_id in self.active_tasks:
                        self.active_tasks[task_id]['status'] = TaskStatus.PAUSED.value
                
                logger.info(f"暂停翻译任务成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"暂停翻译任务失败: {task_id}, {e}")
            return False
    
    def resume_translation(self, task_id: str) -> bool:
        """
        恢复翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            success = global_task_manager.resume_task(task_id)
            
            if success:
                with self._lock:
                    if task_id in self.active_tasks:
                        self.active_tasks[task_id]['status'] = TaskStatus.RUNNING.value
                
                logger.info(f"恢复翻译任务成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"恢复翻译任务失败: {task_id}, {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            Optional[Dict[str, Any]]: 任务状态字典
        """
        # 从任务管理器获取最新状态
        status = global_task_manager.get_task_status(task_id)
        
        if status:
            # 合并本地信息
            with self._lock:
                local_info = self.active_tasks.get(task_id, {})
                status.update({
                    'video_path': local_info.get('video_path'),
                    'config': local_info.get('config')
                })
        
        return status
    
    def list_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有任务
        
        Returns:
            Dict[str, Dict[str, Any]]: 任务ID到状态字典的映射
        """
        result = {}
        
        with self._lock:
            for task_id in self.active_tasks.keys():
                status = self.get_task_status(task_id)
                if status:
                    result[task_id] = status
        
        return result
    
    def get_running_tasks(self) -> List[str]:
        """
        获取所有运行中的任务ID
        
        Returns:
            List[str]: 运行中的任务ID列表
        """
        running_tasks = []
        
        with self._lock:
            for task_id, task_info in self.active_tasks.items():
                if task_info.get('status') == TaskStatus.RUNNING.value:
                    running_tasks.append(task_id)
        
        return running_tasks
    
    def stop_all_tasks(self) -> int:
        """
        停止所有任务
        
        Returns:
            int: 成功停止的任务数量
        """
        stopped_count = 0
        
        with self._lock:
            task_ids = list(self.active_tasks.keys())
        
        for task_id in task_ids:
            if self.stop_translation(task_id):
                stopped_count += 1
        
        logger.info(f"停止了 {stopped_count}/{len(task_ids)} 个任务")
        return stopped_count
    
    def cleanup_finished_tasks(self) -> int:
        """
        清理已完成的任务
        
        Returns:
            int: 清理的任务数量
        """
        cleaned_count = 0
        
        with self._lock:
            finished_task_ids = []
            
            for task_id, task_info in self.active_tasks.items():
                status = task_info.get('status')
                if status in [TaskStatus.COMPLETED.value, TaskStatus.ERROR.value, TaskStatus.CANCELLED.value]:
                    finished_task_ids.append(task_id)
            
            for task_id in finished_task_ids:
                del self.active_tasks[task_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个已完成的任务")
        
        return cleaned_count
    
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒），None表示无限等待
        
        Returns:
            bool: 任务是否在超时前完成
        """
        return global_task_manager.wait_for_task_completion(task_id, timeout)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self._lock:
            status_counts = {}
            for task_info in self.active_tasks.values():
                status = task_info.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            return {
                'total_tasks': len(self.active_tasks),
                'status_counts': status_counts,
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'task_manager_stats': global_task_manager.get_task_count_by_status()
            }

    def adjust_translation_priority(self, task_id: str, target_position: float) -> bool:
        """
        调整翻译队列优先级

        当用户跳转到视频的某个位置时，调整翻译任务队列的优先级，
        使目标位置及其后续片段能够优先翻译。

        Args:
            task_id: 任务ID
            target_position: 目标播放位置（秒）

        Returns:
            bool: 是否调整成功
        """
        print(f"[DEBUG] AsyncRealtimeTranslator.adjust_translation_priority 被调用: task_id={task_id}, target_position={target_position}")
        try:
            # 检查任务是否存在且正在运行
            with self._lock:
                if task_id not in self.active_tasks:
                    print(f"[DEBUG] 任务 {task_id} 不存在于 active_tasks 中")
                    print(f"[DEBUG] 当前 active_tasks: {list(self.active_tasks.keys())}")
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                task_status = self.active_tasks[task_id].get('status')
                print(f"[DEBUG] 任务 {task_id} 状态: {task_status}")
                if task_status != TaskStatus.RUNNING.value:
                    logger.warning(f"任务 {task_id} 未在运行状态，当前状态: {task_status}")
                    return False

            # 调用任务管理器的优先级调整方法
            print(f"[DEBUG] 调用 global_task_manager.adjust_translation_priority")
            success = global_task_manager.adjust_translation_priority(task_id, target_position)
            print(f"[DEBUG] global_task_manager 调用结果: {success}")

            if success:
                logger.info(f"成功调整任务 {task_id} 的翻译优先级到位置 {target_position}s")
            else:
                logger.warning(f"调整任务 {task_id} 的翻译优先级失败")

            return success

        except Exception as e:
            print(f"[DEBUG] AsyncRealtimeTranslator 调用异常: {e}")
            logger.error(f"调整翻译优先级时发生异常: {task_id}, {e}")
            return False
    
    def _wrap_callbacks(self, task_id: str, callbacks: Dict[str, Callable]) -> Dict[str, Callable]:
        """
        包装回调函数以更新本地状态
        
        Args:
            task_id: 任务ID
            callbacks: 原始回调函数字典
        
        Returns:
            Dict[str, Callable]: 包装后的回调函数字典
        """
        def wrapped_finished(tid: str, result_data: Dict[str, Any]):
            with self._lock:
                if tid in self.active_tasks:
                    self.active_tasks[tid]['status'] = TaskStatus.COMPLETED.value
            
            original_callback = callbacks.get('on_finished')
            if original_callback:
                try:
                    original_callback(tid, result_data)
                except Exception as e:
                    logger.error(f"用户完成回调执行失败: {e}")
        
        def wrapped_error(tid: str, error_data: Dict[str, Any]):
            with self._lock:
                if tid in self.active_tasks:
                    self.active_tasks[tid]['status'] = TaskStatus.ERROR.value
            
            original_callback = callbacks.get('on_error')
            if original_callback:
                try:
                    original_callback(tid, error_data)
                except Exception as e:
                    logger.error(f"用户错误回调执行失败: {e}")
        
        wrapped = callbacks.copy()
        wrapped['on_finished'] = wrapped_finished
        wrapped['on_error'] = wrapped_error
        
        return wrapped
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.stop_all_tasks()


# 便捷函数
def create_async_translator(**kwargs) -> AsyncRealtimeTranslator:
    """
    创建异步翻译器的便捷函数
    
    Args:
        **kwargs: 传递给AsyncRealtimeTranslator的参数
    
    Returns:
        AsyncRealtimeTranslator: 异步翻译器实例
    """
    return AsyncRealtimeTranslator(**kwargs)