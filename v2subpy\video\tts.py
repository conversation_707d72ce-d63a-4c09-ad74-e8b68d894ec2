'''
根据文字, 字幕生成音频
'''
from pathlib import Path
import asyncio
import os
import random
import re
import shutil
import subprocess
import sys
import tempfile
import io
import edge_tts
import srt
from pysubs2 import SSAFile

from v2subpy.utils import common
from v2subpy.sub.subutils import read_file
from v2subpy.utils.log import logger
from v2subpy.utils.config import get_text

default_voice_dict = {
    'zh' : 'zh-CN-YunxiNeural',
    'en' : 'en-US-AriaNeural',
    'ja' : 'ja-JP-NanamiNeural',
    'ko' : 'ko-KR-SunHiNeural',
    'de' : 'de-DE-AmalaNeural',
    'fr' : 'fr-FR-DeniseNeural',
    'ru' : 'ru-RU-SvetlanaNeural',
    'es' : 'es-ES-ElviraNeural',
    'yue': 'zh-HK-HiuMaanNeural' ,
}

def shell_args():
    '''
    根据平台判断， 是否需要使用shell=True的变量
    win需要使用
    '''
    args = {
        'shell': False
    }
    if common.is_win():
        args['shell'] = True
    return args


def get_default_voice_profile(audio_lang: str) -> dict:
    '''
    返回默认profile, 在没有找到已保存模板时使用
    '''
    default_voice = default_voice_dict.get(audio_lang)
    if default_voice is None:
        return None
    default_voice_rofile = {
        'voice_lang': default_voice[:5],
        'voice_name': default_voice,
        'voice_speed': 0,
        'voice_volume': 0,
        'voice_pitch': 0,
    }
    return default_voice_rofile


def load_voice_profile(audio_lang: str | None) -> dict:
    '''
    从文件加载voice_config,没有返回默认
    '''
    profile = common.get_profile()
    voice_profile = profile.get('voice_config')
    if voice_profile and audio_lang is None:
        return voice_profile
    elif voice_profile and audio_lang is not None:
        voice_profile_lang = voice_profile.get('voice_lang')[:2]
        if audio_lang == voice_profile_lang:
            return voice_profile
        else:
            return get_default_voice_profile(audio_lang)
    else:
        return get_default_voice_profile(audio_lang)

def spinoff_sentence(sentence):
    last_word = sentence[-1]
    last_word_num = sentence.count(last_word)
    return (sentence, last_word, last_word_num)


def get_pattern(voice: str) -> str:
    '''
    根据语言代码， 返回合适的解析正则表达式
    '''
    pattern_dict = {
        'en': r"[,.!<>;:?\{}]",
        'zh': r"[：""''──{}【】·《》〈〉（）…，、；。？！]"
    }
    pattern_dict['ja'] = pattern_dict['zh']
    pattern_dict['ko'] = pattern_dict['en']

    default_pattern = pattern_dict['en']
    lang_code, _ = voice.split('-', 1)
    if lang_code in pattern_dict:
        return pattern_dict[lang_code]
    return default_pattern


async def tts_subtitle(text, voice, voice_speed, voice_volume, voice_pitch):

    retry_limit = 3
    wait_time = 3
    proxies = common.get_proxy_from_cfg()
    proxy = proxies['http'] if proxies else None

    text_length = len(text)
    logger.debug(f'total text length: {text_length}')
    for i in range(retry_limit):
        try:
            file = io.BytesIO()
            subs = []
            communicate = edge_tts.Communicate(text, voice,
                                               rate=voice_speed,
                                               volume=voice_volume,
                                               pitch=voice_pitch,
                                               proxy=proxy)
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    file.write(chunk["data"])
                elif chunk["type"] == "WordBoundary":
                    subs.append(
                        (chunk["offset"], chunk["duration"],  chunk["text"]))
        except Exception as e:
            if (i+1) == retry_limit:
                logger.exception(e)
                raise Exception(f"Too many retries") from e
            logger.debug(f"Retry {i+1} times")
            await asyncio.sleep(wait_time)
            wait_time *= 2
        else:
            data = file.getvalue()
            return data, subs
        finally:
            file.close()


async def make_audio_txt(txt_path: str, voice: str, audio_path: str, voice_speed: str, voice_volume: str, voice_pitch: str, test_run: int = 0, update_progress=None) -> str:
    '''
    根据文本内容生成音频
    '''
    batch_size = 50
    batch_wait_time = 5
    batch_gap_time = 0.5 * (10 ** 7)
    with open(txt_path, 'r', encoding='utf-8') as file:
        text = file.read()
    pattern = get_pattern(voice)
    logger.debug(f'use pattern: {pattern}')
    sentences = re.split(pattern, text)
    sentences = [sentence.strip()
                 for sentence in sentences if sentence.strip()]
    three_dimensional_list = []
    if test_run > 0:
        logger.debug(f'test run: {test_run}')
        sentences = sentences[:test_run]
        text = ''.join(sentences)
    for sentence in sentences:
        three_dimensional_list.append(spinoff_sentence(sentence))
    pf_txt = Path(txt_path)
    subtitle_path = str(pf_txt.with_suffix('.srt'))
    submaker = edge_tts.SubMaker()
    total_lines = len(sentences)
    logger.debug(f'total lines: {total_lines}')
    last_offset = 0
    # 生成batch， 一次20句
    with open(audio_path, "wb") as audio_file:
        for i in range(0, total_lines, batch_size):
            batch = sentences[i: i+batch_size]
            batch_text = '\n'.join(batch)
            audio_data, sub_data = await tts_subtitle(batch_text, voice, voice_speed, voice_volume, voice_pitch)
            audio_file.write(audio_data)
            for chunk in sub_data:
                submaker.create_sub(
                    (chunk[0] + last_offset, chunk[1]), chunk[2])
            last_offset = last_offset + chunk[0] + chunk[1] + batch_gap_time
            # logger.debug(f'{last_offset=}')
            if update_progress is not None:
                status_message = get_text('info.StatusGenerateAudio')
                progress_info = (i, total_lines)
                update_progress(status_message, progress_info)
            await asyncio.sleep(batch_wait_time)

    if update_progress is not None:
        update_progress(get_text('info.StatusMergeAudio'), (1, 1))
    # 检查是否已经有字幕文件， 没有才生成
    subtitle_pf = Path(subtitle_path)
    if not subtitle_pf.exists():
        with open(subtitle_path, "w", encoding="utf-8") as file:
            sub_str = submaker.generate_subs(
                three_dimensional_list=three_dimensional_list)
            file.write(sub_str)


def fix_sub_gap(subtitles):
    '''
    去掉每句字幕之间的gap
    返回修改过的subtitles
    '''
    for i in range(len(subtitles) - 1):
        subtitles[i].end = subtitles[i+1].start
        content = subtitles[i].content
        content = content.replace('\n', '')
        subtitles[i].content = content

    return subtitles

def get_srt_subtitles(srt_path):
    '''检查是否为srt格式, 如果不是转为srt'''
    pf = Path(srt_path)
    if pf.suffix[1:] == common.SubtitleFormatEnum.SRT.value:
        srt_text = pf.read_text(encoding='utf-8')
    else:
        subs = SSAFile.load(srt_path)
        srt_text = subs.to_string(common.SubtitleFormatEnum.SRT.value)
    
    srt_data = list(srt.parse(srt_text))
    return srt_data

async def make_audio_srt(srt_path: str, voice: str, audio_path: str, voice_speed: str, voice_volume: str, voice_pitch: str,  test_run: int = 0, update_progress=None) -> str:
    '''
    根据srt生成音频
    '''
    srt_data = get_srt_subtitles(srt_path)
    if test_run > 0:
        logger.debug(f'test run: {test_run}')
        srt_data = srt_data[:test_run]
    batch_size = 30
    enhanced_srt = True
    if not srt_data or len(srt_data) == 0:
        raise ValueError("srt_data is empty")
    with tempfile.TemporaryDirectory() as temp_dir:
        # Debug line - consider removing in production
        # temp_dir = r"D:\test_files\test_250328\temp"
        
        # Step 1: Prepare batches of audio generation tasks
        args, input_files, input_files_start_end, total_subtitle_duration, max_duration = get_batch(
            srt_data, voice, voice_speed, voice_volume, voice_pitch, enhanced_srt, temp_dir
        )
        
        # Step 2: Generate audio files
        await gen_audios(args, batch_size, update_progress)
       
        # Step 3: remove the audio silence part 
        trim_audio_files(input_files, update_progress)
        
        # Step 4: Adjust audio speed to maintain consistent speaking rate
        adjusted_audio_files, global_speed_ratio = adjust_audio_speed(
            input_files, total_subtitle_duration, temp_dir, update_progress
        )
        
        # Step 5: Align audio with silence to maintain sync with subtitles
        concat_file_list, silence_info = align_audio_silence(
            adjusted_audio_files, input_files, input_files_start_end, max_duration, temp_dir, update_progress
        )
        
        # Step 6: Concatenate all audio files into the final output
        await concat_audios(concat_file_list, audio_path, update_progress)
        
        # Step 7: Generate new subtitle file with aligned timings
        aligned_subtitle_path = gen_aligned_subtitle(
            srt_path, srt_data, adjusted_audio_files, input_files, input_files_start_end, 
            global_speed_ratio, silence_info, update_progress
        )
        logger.debug(f'Created aligned subtitle file: {aligned_subtitle_path}')
        
    output_audio_length = get_duration(audio_path)
    logger.debug(f'create audio, {audio_path}, length={output_audio_length}, subtitle_duration={max_duration}')


def get_batch(srt_data, voice, rate, volume, pitch, enhanced_srt, temp_dir):
    '''
    Prepare batches of audio generation tasks from subtitle data
    
    Args:
        srt_data: List of subtitle entries
        voice: Voice profile to use
        rate: Speaking rate
        volume: Voice volume
        pitch: Voice pitch
        enhanced_srt: Whether to use enhanced SRT features
        temp_dir: Directory to store temporary files
    
    Returns:
        args: List of arguments for audio generation
        input_files: List of audio file paths
        input_files_start_end: Dictionary mapping file paths to (start, end) times
        total_subtitle_duration: Total duration of all subtitles
        max_duration: Maximum duration of the audio
    '''
    if not srt_data or len(srt_data) == 0:
        raise ValueError("srt_data is empty")
    
    max_duration = srttime_to_seconds(srt_data[-1].end)
    input_files = []
    input_files_start_end = {}
    args = []
    total_subtitle_duration = 0
    
    # Collect subtitle durations and prepare audio generation
    for i, subtitle in enumerate(srt_data):
        fname = os.path.join(temp_dir, f"{i}.mp3")
        input_files.append(fname)

        start = srttime_to_seconds(subtitle.start)
        end = srttime_to_seconds(subtitle.end)
        duration = end - start
        total_subtitle_duration += duration

        input_files_start_end[fname] = (start, end)

        args.append({
            "fname": fname,
            "text": subtitle.content,
            "rate": rate,
            "volume": volume,
            "pitch": pitch,
            "voice": voice,
            "duration": duration,
            "enhanced_srt": enhanced_srt,
        })
    
    return args, input_files, input_files_start_end, total_subtitle_duration, max_duration


async def gen_audios(args, batch_size, update_progress=None):
    '''
    Generate audio files in batches
    
    Args:
        args: List of arguments for audio generation
        batch_size: Number of audio files to generate in each batch
        update_progress: Function to update progress
    '''
    queue = asyncio.Queue()
    args_len = len(args)
    batch_wait_time = 3
    
    # Generate all audio files without speed adjustment
    for i in range(0, args_len, batch_size):
        tasks = []
        for j in range(i, min(i + batch_size, args_len)):
            tasks.append(audio_gen(queue))
            await queue.put(args[j])

        # Tasks counter for progress
        t = 0
        for f in asyncio.as_completed(tasks):
            await f
            t += 1
            if update_progress is not None:
                status_message = get_text('info.StatusGenerateAudio')
                progress_info = (t + i, args_len)
                update_progress(status_message, progress_info)
        # Wait for a while before processing the next batch
        await asyncio.sleep(batch_wait_time)


def adjust_audio_speed(input_files, total_subtitle_duration, temp_dir, update_progress):
    '''
    Calculate global speed ratio and adjust audio files speed
    
    Args:
        input_files: List of audio file paths
        total_subtitle_duration: Total duration of all subtitles
        temp_dir: Directory to store temporary files
    
    Returns:
        adjusted_audio_files: List of adjusted audio file paths
        global_speed_ratio: The calculated global speed ratio
    '''
    # Calculate total audio duration and global speed ratio
    total_audio_duration = 0
    file_length = len(input_files)
    for audio_file in input_files:
        if os.path.getsize(audio_file) > 0:
            total_audio_duration += get_duration(audio_file)
    
    # Calculate global speed ratio
    global_speed_ratio = total_audio_duration / total_subtitle_duration
    # a fixed ratio to handle some mismatch
    mismatch_ratio = 1
    global_speed_ratio = global_speed_ratio * mismatch_ratio
    logger.debug(f"Total audio duration: {total_audio_duration}, subtitle duration: {total_subtitle_duration}")
    logger.debug(f"Global speed ratio: {global_speed_ratio}")
    
    # Apply consistent speed adjustment to all audio files
    adjusted_audio_files = []
    for i, audio_file in enumerate(input_files):
        adjusted_file = os.path.join(temp_dir, f"adjusted_{i}.mp3")
        adjusted_audio_files.append(adjusted_file)
        
        if os.path.getsize(audio_file) > 0:
            # Apply global speed ratio to maintain consistent speaking rate
            audio_adjust_raito(audio_file, adjusted_file, global_speed_ratio)
        else:
            # For empty files
            shutil.copyfile(audio_file, adjusted_file)
        if update_progress is not None:
            status_message = get_text('info.StatusAdjustAudio')
            progress_info = (i + 1, file_length)
            update_progress(status_message, progress_info)
    
    return adjusted_audio_files, global_speed_ratio


def align_audio_silence(adjusted_audio_files, input_files, input_files_start_end, max_duration, temp_dir, update_progress=None):
    '''
    Add appropriate silence between audio segments to maintain sync with subtitles
    
    Args:
        adjusted_audio_files: List of adjusted audio file paths
        input_files: List of original audio file paths
        input_files_start_end: Dictionary mapping file paths to (start, end) times
        max_duration: Maximum duration of the audio
        temp_dir: Directory to store temporary files
        update_progress: Function to update progress
        
    Returns:
        tuple: (concat_file_list, silence_info)
            concat_file_list: Path to the temporary file containing the list of files to concatenate
            silence_info: Dictionary with information about silence files and audio timing
    '''
    logger.debug("Generating silence and joining...")
    f = tempfile.NamedTemporaryFile(
        mode="w", encoding="utf-8", delete=False)

    last_end = 0
    current_time_offset = 0
    original_last_end = 0
    small_mismatch = 0
    
    # Track all silence and audio files with their durations for subtitle alignment
    silence_info = {
        'files': [],  # List of all files in order (silence and audio)
        'durations': [],  # Duration of each file
        'types': [],  # Type of each file ('silence' or 'audio')
        'segment_indices': []  # Original subtitle index for each audio segment
    }
    total_siience_diff = 0
    # Generate output with adjusted silence durations to maintain sync
    for i, audio_file in enumerate(adjusted_audio_files):
        audio_duration = get_duration(audio_file)
        current_duration = input_files_start_end[input_files[i]][1] - input_files_start_end[input_files[i]][0]
        end = input_files_start_end[input_files[i]][1]
        
        if i == 0 and audio_duration > current_duration:
            start = max(0.001, input_files_start_end[input_files[i]][0] - (audio_duration - current_duration))
        else:
            start = input_files_start_end[input_files[i]][0]
            
        logger.debug(f'{start=}, {last_end=}')
        logger.debug(f'{audio_file=}: current_duration={current_duration:.4f}, audio_duration={audio_duration:.4f}')

        if i > 0:
            original_last_end = input_files_start_end[input_files[i - 1]][1]
            
        # Calculate ideal silence duration
        ideal_silence = start - last_end
        original_silence = start - original_last_end
        # Use ideal silence directly without secondary adjustment
        adjusted_silence = max(0.0001, ideal_silence)
        logger.debug(f'{original_silence=}, {ideal_silence=}, {adjusted_silence=}')
        
        if adjusted_silence > 0.0001:
            sfname = os.path.join(temp_dir, f"silence_{i}.mp3")
            silence_gen(sfname, adjusted_silence)
            f.write(f"file '{sfname}'\n")
            actual_silence_duration = get_duration(sfname)
            last_end += actual_silence_duration
            silence_diff = actual_silence_duration - adjusted_silence
            total_siience_diff += silence_diff
            logger.debug(f'{adjusted_silence=}, {actual_silence_duration=}, diff={silence_diff}, ratio={silence_diff/adjusted_silence:.4f}')             # Track silence file for subtitle alignment
            silence_info['files'].append(sfname)
            silence_info['durations'].append(actual_silence_duration)
            silence_info['types'].append('silence')
            silence_info['segment_indices'].append(-1)  # -1 for silence
        
        f.write(f"file '{audio_file}'\n")
        last_end += audio_duration
        
        # Track audio file for subtitle alignment
        silence_info['files'].append(audio_file)
        silence_info['durations'].append(audio_duration)
        silence_info['types'].append('audio')
        silence_info['segment_indices'].append(i)  # Original index
        
        # Track for debugging purposes
        ideal_end = input_files_start_end[input_files[i]][1]
        current_time_offset = last_end - ideal_end
        # if abs(current_time_offset) > 0.5:  # Only log significant drift
        #     logger.debug(f"Time drift at segment {i}: {current_time_offset:.3f}s")
        if abs(current_time_offset) < 0.1:  # Only log small mismatch
            small_mismatch += 1
            logger.debug(f"Time drift at segment with small mismatch {i}: {current_time_offset:.3f}s") 
        if update_progress is not None:
            status_message = get_text('info.StatusAdjustAudio')
            progress_info = (i+1, len(adjusted_audio_files))
            update_progress(status_message, progress_info)

    logger.debug(f'{total_siience_diff=}')
    logger.debug(f'total {small_mismatch=}')
    # Add final silence if needed
    needed = max(0, max_duration - last_end)
    if needed > 0:
        sfname = os.path.join(temp_dir, "silence_final.mp3")
        silence_gen(sfname, needed)
        f.write(f"file '{sfname}'\n")
        
        # Track final silence for subtitle alignment
        silence_info['files'].append(sfname)
        silence_info['durations'].append(needed)
        silence_info['types'].append('silence')
        silence_info['segment_indices'].append(-1)  # -1 for silence
    
    f.flush()
    f.close()
    
    return f.name, silence_info


async def concat_audios(concat_file_list, out_file, update_progress=None):
    '''
    Concatenate audio files listed in the concat file
    
    Args:
        concat_file_list: Path to the file containing the list of files to concatenate
        out_file: Path to the output file
        update_progress: Function to update progress
    '''
    if update_progress is not None:
        update_progress(get_text('info.StatusMergeAudio'), (0, 1))

    # Use ffmpeg to concatenate all files
    retcode = subprocess.call(
        [
            "ffmpeg",
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            concat_file_list,
            "-c",
            "copy",
            out_file,
        ],
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
        **shell_args()
    )
    
    # Clean up the concat file list
    try:
        os.remove(concat_file_list)
    except Exception as e:
        logger.debug(f"Failed to remove concat file list: {e}")
        
    if retcode != 0:
        raise subprocess.CalledProcessError(retcode, "ffmpeg")
        
    if update_progress is not None:
        update_progress(get_text('info.StatusMergeAudio'), (1, 1))
        
    logger.debug(f"Completed {out_file}")


def make_audio(srt_txt_path: str, *, test_run: int = 0, audio_lang: str | None = None, update_progress=None) -> str:
    '''
    根据srt生成相应音频

    Args:
        srt_path: srt文件路径
        voice_name: 使用的声音名称， 如 "zh-CN-YunxiNeural"
        test_run: 试运行， 取前若干条字幕
        update_progress: 前端传来的进度更新回调函数

    Return:
        audio_path: 返回生成的音频文件路径
    '''
    logger.debug(f'make audio: {srt_txt_path=}, {test_run=}, {audio_lang=}')
    pf = Path(srt_txt_path)
    pf = pf.with_stem(f'{pf.stem}_output')
    audio_path = str(pf.with_suffix(".mp3"))
    voice_profile = load_voice_profile(audio_lang)
    if not voice_profile:
        raise common.V2subError(get_text('error.TTSLanguageNotSupport'))
    logger.debug(f'{voice_profile}')
    voice_name = voice_profile['voice_name']
    voice_speed = f"{voice_profile['voice_speed']:+}%"
    voice_volume = f"{voice_profile['voice_volume']:+}%"
    voice_pitch = f"{voice_profile['voice_pitch']:+}Hz"
    try:
        if pf.suffix == '.txt':
            task = make_audio_txt(srt_txt_path, voice_name, audio_path,
                                  voice_speed, voice_volume, voice_pitch, test_run, update_progress)
        else:
            task = make_audio_srt(srt_txt_path, voice_name, audio_path,
                                  voice_speed, voice_volume, voice_pitch, test_run, update_progress)
        asyncio.run(task)
        return audio_path
    except Exception as e:
        logger.debug(str(e))
        raise common.V2subError(get_text('error.TTSGenerateError'))


def trim_audio_to_video_length(audio_path: str, video_path: str, update_progress=None) -> str:
    '''
    Checks if audio length is longer than video length and trims it if needed
    
    Args:
        audio_path: Path to the audio file
        video_path: Path to the video file
        update_progress: Function to update progress
        
    Returns:
        str: Path to the trimmed audio file (if trimming was needed) or original audio file (if no trimming needed)
    '''
    if not os.path.exists(audio_path) or not os.path.exists(video_path):
        logger.debug(f"Audio or video file doesn't exist, cannot trim")
        return audio_path
    
    try:
        audio_length = get_duration(audio_path)
        video_length = get_duration(video_path)
        diff_length = audio_length - video_length
        allowed_diff_length = 3
        logger.debug(f"Audio length: {audio_length}, Video length: {video_length}")
        
        # Return original audio if it's not longer than the video
        if diff_length < allowed_diff_length:
            logger.debug("Audio is not longer than video, no trimming needed")
            return audio_path
        
        if update_progress is not None:
            update_progress(get_text('info.StatusAdjustAudio'), (0, 1))
        
        # Create a temp file for trimmed audio
        with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as tmp_file:
            trimmed_audio_path = tmp_file.name
        
        # Trim audio to match video length
        retcode = subprocess.call(
            [
                "ffmpeg",
                "-y",
                "-i",
                audio_path,
                "-t",
                str(video_length),
                "-c:a",
                "copy",
                trimmed_audio_path,
            ],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            **shell_args()
        )
        
        if retcode != 0:
            logger.debug(f"Error trimming audio: {retcode}")
            return audio_path
        
        if update_progress is not None:
            update_progress(get_text('info.StatusAdjustAudio'), (1, 1))
        
        logger.debug(f"Trimmed audio from {audio_length}s to {video_length}s")
        return trimmed_audio_path
    
    except Exception as e:
        logger.debug(f"Error in trim_audio_to_video_length: {e}")
        return audio_path


def srttime_to_seconds(t):
    return t.total_seconds()


def get_duration(in_file):
    duration = subprocess.check_output(
        [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            in_file,
        ],
        **shell_args()
    ).decode("utf-8")
    return float(duration)


def get_enhanced_srt_params(text, arg):
    text_ = text.split("\n")[-1]
    if text_.startswith("edge_tts{") and text_.endswith("}"):
        text_ = text_[len("edge_tts{"): -len("}")]
        text_ = text_.split(",")
        text_ = dict([x.split(":") for x in text_])
        for x in text_.keys():
            if x not in ["rate", "volume", "voice", "pitch"]:
                raise ValueError("edge_tts{} is invalid")
        for k, v in text_.items():
            arg[k] = v
        return arg, "\n".join(text.split("\n")[:-1])
    return arg, text

def audio_adjust_raito(in_file, out_file, speed_ratio):
    ''''
    根据速度比率生成新的音频
    '''
    atempo = speed_ratio
    if atempo < 0.5:
        atempo = 0.5
    elif atempo > 100:
        atempo = 100
    if atempo > 1:
        # logger.debug(f'adjust speed: {in_file}')
        retcode = subprocess.call(
            [
                "ffmpeg",
                "-y",
                "-i",
                in_file,
                "-filter:a",
                f"atempo={atempo}",
                out_file,
            ],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            **shell_args()
        )
        if retcode != 0:
            raise subprocess.CalledProcessError(retcode, "ffmpeg")
    else:
        shutil.copyfile(in_file, out_file)

def ensure_audio_length(in_file, out_file, length):
    # shutil.copyfile(in_file, out_file)
    # return
    duration = get_duration(in_file)
    speed_ratio = duration / length
    audio_adjust_raito(in_file, out_file, speed_ratio)



def silence_gen(out_file, duration):
    # logger.debug("Generating silence %s...", out_file)
    from pydub import AudioSegment
    above_duration = 0.1
    adjust_duration = 0.05
    if duration > above_duration:
        duration -= adjust_duration
    silent_audio = AudioSegment.silent(duration * 1000, frame_rate=24000)
    silent_audio.export(out_file, format='mp3')
    actual_duration = get_duration(out_file)
    logger.debug("Generated silence %s with duration %.4f, actual_duration: %.4f", out_file, duration, actual_duration)


async def audio_gen(queue):
    retry_count = 0
    retry_limit = 5
    file_length = 0
    arg = await queue.get()
    fname, text, duration, enhanced_srt = (
        arg["fname"],
        arg["text"],
        arg["duration"],
        arg["enhanced_srt"],
    )
    # logger.debug(f'audio gen for {fname} with args: {arg}')
    if enhanced_srt:
        arg, text = get_enhanced_srt_params(text, arg)
    text = " ".join(text.split("\n"))
    proxies = common.get_proxy_from_cfg()
    proxy = proxies['http'] if proxies else None
    # logger.debug(f'tts proxy: {proxy}')
    while True:
        # logger.debug("Generating %s...", fname)
        try:
            communicate = edge_tts.Communicate(
                text,
                rate=arg["rate"],
                volume=arg["volume"],
                voice=arg["voice"],
                pitch=arg["pitch"],
                proxy=proxy
            )
            await communicate.save(fname)
        except edge_tts.exceptions.NoAudioReceived:
            with open(fname, "wb") as fobj:
                fobj.write(b"")
        except Exception as e:
            if retry_count > retry_limit:
                raise Exception(f"Too many retries for {fname}") from e
            retry_count += 1
            logger.debug("Retrying %s...", fname)
            await asyncio.sleep(retry_count + random.randint(3, 10))
            continue
        break
    # logger.debug("Generated %s", fname)
    file_length = os.path.getsize(fname)
    if not file_length > 0:
        silence_gen(fname, duration)
    
    queue.task_done()

def gen_aligned_subtitle(srt_path, srt_data, adjusted_audio_files, input_files, input_files_start_end, 
                         global_speed_ratio, silence_info, update_progress=None):
    '''
    Generate a new subtitle file with timings aligned to the audio output
    
    Args:
        srt_path: Path to the original subtitle file
        srt_data: List of original subtitle entries
        adjusted_audio_files: List of adjusted audio file paths
        input_files: List of original audio file paths
        input_files_start_end: Dictionary mapping file paths to (start, end) times
        global_speed_ratio: The global speed ratio used to adjust audio
        silence_info: Dictionary with information about silence files and audio timing
        update_progress: Function to update progress
        
    Returns:
        aligned_subtitle_path: Path to the generated aligned subtitle file
    '''
    if update_progress is not None:
        update_progress(get_text('info.StatusAlignSubtitle'), (0, 1))
    
    # Create new subtitle list with adjusted timings
    aligned_subs = []
    
    # Create a mapping from original subtitle index to timing in the final audio
    index_to_timing = {}
    cumulative_time = 0.0
    
    # First, calculate the precise start times for each subtitle based on the actual 
    # concatenated audio files (including all silences)
    for i in range(len(silence_info['files'])):
        file_type = silence_info['types'][i]
        duration = silence_info['durations'][i]
        segment_index = silence_info['segment_indices'][i]
        
        # Only care about audio segments (not silence) that correspond to subtitles
        if file_type == 'audio' and segment_index >= 0 and segment_index < len(srt_data):
            # This is the start time of this subtitle in the final audio
            index_to_timing[segment_index] = {
                'start': cumulative_time,
                'end': cumulative_time + duration
            }
        
        # Update cumulative time
        cumulative_time += duration
    
    # Now create new subtitle entries with the precise timings
    for i, subtitle in enumerate(srt_data):
        if i in index_to_timing:
            timing = index_to_timing[i]
            
            # Create new subtitle with aligned timing
            new_sub = srt.Subtitle(
                index=subtitle.index,
                start=srt.timedelta(seconds=timing['start']),
                end=srt.timedelta(seconds=timing['end']),
                content=subtitle.content
            )
            aligned_subs.append(new_sub)
    
    # Generate the new subtitle file
    pf = Path(srt_path)
    aligned_subtitle_path = str(pf.with_stem(f'{pf.stem}_aligned'))
    
    # Write the new subtitle file
    srt_text = srt.compose(aligned_subs)
    out_format = pf.suffix[1:]
    with open(aligned_subtitle_path, 'w', encoding='utf-8') as f:
        if out_format == common.SubtitleFormatEnum.SRT.value:
            f.write(srt_text)
        else:
            out_subs = SSAFile.from_string(srt_text)
            subtitle_text = out_subs.to_string(out_format)
            f.write(subtitle_text)
    
    if update_progress is not None:
        update_progress(get_text('info.StatusAlignSubtitle'), (1, 1))
        
    logger.debug(f'Generated aligned subtitle: {aligned_subtitle_path}')
    return aligned_subtitle_path

def trim_audio_files(input_files, update_progress=None):
    total_length = len(input_files)
    for i, audio_file in enumerate(input_files, start=1):
        remove_silence(audio_file)
        if update_progress is not None:
            update_progress(get_text('info.StatusAdjustAudio'), (i, total_length))


def remove_silence(input_file: str) -> bool:
    '''
    移除静音部分
    '''
    from pydub import AudioSegment
    from pydub.silence import split_on_silence
    if not os.path.exists(input_file):
        raise FileNotFoundError(f'输入文件不存在: {input_file}')
    logger.debug(f'remove silence: {input_file}')
    audio = AudioSegment.from_file(input_file)
    chunks = split_on_silence(audio, min_silence_len=500, silence_thresh=(-40), keep_silence=100)
    pf = Path(input_file)
    out_pf = pf.with_stem(pf.stem+"_trimed")
    if not chunks:
        # 没检测音频直接复制
        shutil.copyfile(input_file, str(out_pf))
    else:
        non_silent_audio = sum(chunks)
        non_silent_audio.export(str(out_pf), format='mp3')

    # 删除原文件
    out_pf.replace(pf)
    return True

def patch_subprocess_noconsole():
    '''
    防止打包后subprocess执行会弹出console窗口
    '''
    class NoWindowPopen(subprocess.Popen):
        def __init__(self, *args, **kwargs):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags = subprocess.CREATE_NEW_CONSOLE | subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            kwargs["startupinfo"] = startupinfo
            super().__init__(*args, **kwargs)


    subprocess.Popen = NoWindowPopen
    
if common.is_win():
    patch_subprocess_noconsole()