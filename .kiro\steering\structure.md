# Project Structure

## Root Directory Layout

```
v2subpy/
├── v2subpy/                 # Main package source code
├── tests/                   # Test suite
├── examples/                # Integration examples and demos
├── docs/                    # Documentation files
├── notebooks/               # Jupyter notebooks for experimentation
├── play/                    # Experimental and research code
├── pack/                    # Packaging and distribution files
├── scripts/                 # Utility scripts
├── logs/                    # Runtime logs
├── data/                    # Configuration and data files
├── build/                   # Build artifacts (generated)
├── dist/                    # Distribution packages (generated)
└── main.py                  # Entry point for standalone execution
```

## Core Package Structure (`v2subpy/`)

### Primary Modules
- **`v2sub.py`**: Main API and orchestration logic
- **`version.py`**: Version information
- **`__init__.py`**: Package initialization

### Subpackages
- **`model/`**: Whisper model management and transcription
- **`trans/`**: Translation engine abstractions and implementations
- **`video/`**: FFmpeg integration and media processing
- **`sub/`**: Subtitle file handling and format support
- **`realtime/`**: Real-time processing pipeline
- **`utils/`**: Configuration, logging, and utility functions

## Key Directories

### `/tests/`
Comprehensive test suite organized by functionality:
- `test_*.py`: Unit tests for specific modules
- `conftest.py`: Pytest configuration and fixtures
- Test coverage for all major components

### `/examples/`
Integration examples and usage demonstrations:
- `qt_integration_example.py`: PyQt5 desktop integration
- `simple_integration_example.py`: Command-line usage
- `test_integration.py`: Integration testing
- `README.md`: Usage documentation

### `/docs/`
Project documentation:
- `realtime_player_design.md`: Architecture documentation
- `realtime_playe_requirements.md`: Requirements specification
- `dev.md`: Development guidelines
- `versions.md`: Version history

### `/play/`
Experimental and research code:
- Various translation service experiments
- Model testing and evaluation
- Third-party library integrations
- Prototype implementations

## File Naming Conventions

### Python Files
- **Module files**: `snake_case.py`
- **Test files**: `test_<module_name>.py`
- **Example files**: `<purpose>_example.py`

### Configuration Files
- **Main config**: `config.toml`
- **Build config**: `setup.py`, `pytest.ini`
- **Batch scripts**: `*.bat` (Windows-specific)

### Documentation
- **Markdown files**: `UPPERCASE.md` for root-level docs
- **Module docs**: `lowercase.md` in subdirectories

## Import Patterns

### Internal Imports
```python
# Absolute imports from package root
from v2subpy.model.whisper import transcribe
from v2subpy.trans.base import BaseTranslator
from v2subpy.utils.config import get_key

# Relative imports within subpackages
from .base import BaseTranslator
from ..utils.log import logger
```

### External Dependencies
```python
# AI/ML libraries
from faster_whisper import WhisperModel
import torch

# GUI framework
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QMainWindow

# Media processing
import ffmpeg
```

## Configuration Structure

### Main Configuration (`data/config.toml`)
- Model settings (device, model_name, cpu_balance)
- Translation service configurations
- Proxy settings
- Feature flags and version management

### Runtime Configuration
- Logging configuration
- Temporary file management
- Resource allocation settings

## Build Artifacts

### Generated Directories (Git-ignored)
- `build/`: Cython compilation artifacts
- `dist/`: Wheel and source distributions
- `v2subpy.egg-info/`: Package metadata
- `__pycache__/`: Python bytecode cache
- `.pytest_cache/`: Pytest cache files

### Packaging Files
- `v2subpy.spec`: PyInstaller specification
- `app.spec`: Application packaging configuration

## Development Workflow

### Code Organization
1. **Core logic** in main package modules
2. **Tests** mirror the package structure
3. **Examples** demonstrate usage patterns
4. **Documentation** explains architecture and usage

### File Placement Guidelines
- **New features**: Add to appropriate subpackage
- **Utilities**: Place in `v2subpy/utils/`
- **Tests**: Mirror source structure in `tests/`
- **Examples**: Add to `examples/` with documentation
- **Experiments**: Use `play/` directory for prototypes

### Import Best Practices
- Use absolute imports for cross-package references
- Keep relative imports within subpackages
- Avoid circular dependencies
- Import only what's needed for performance