# v2subpy 实时字幕后端服务设计文档

## 概述

本设计文档描述了 v2subpy 实时字幕后端服务的技术架构和实现方案。该服务为 v2subqt 提供统一的实时字幕翻译接口，通过 Qt Worker Thread 和信号机制实现高效的进程间通信。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "v2subqt (前端)"
        A[主界面线程] --> B[Worker Thread]
        B --> C[字幕缓冲管理器]
        C --> D[播放器显示]
    end
    
    subgraph "v2subpy (后端)"
        E[realtime_trans 接口] --> F[实时处理管道]
        F --> G[音频分段器]
        F --> H[语音识别器]
        F --> I[翻译引擎]
        F --> J[信号发射器]
    end
    
    B -.调用.-> E
    J -.Qt信号.-> C
    
    subgraph "共享组件"
        K[配置管理器]
        L[错误处理器]
        M[日志记录器]
    end
    
    F --> K
    F --> L
    F --> M
```

### 核心组件设计

#### 1. 统一实时翻译接口

这是后端的主要接口函数，提供统一的实时翻译功能。

```python
def realtime_trans(video_path: str, config: dict, 
                  on_subtitle_ready=None, 
                  on_progress_update=None, 
                  on_error=None, 
                  on_finished=None) -> None:
    """
    统一的实时翻译接口
    
    Args:
        video_path: 视频文件路径
        config: 配置参数字典
        on_subtitle_ready: 字幕段准备就绪回调函数 callback(subtitle_dict)
        on_progress_update: 进度更新回调函数 callback(status_message, progress_info)
        on_error: 错误回调函数 callback(error_message)
        on_finished: 处理完成回调函数 callback()
    """
    pass
```

#### 2. RealtimeProcessingPipeline (实时处理管道)

负责协调整个处理流程的核心组件。

```python
class RealtimeProcessingPipeline:
    def __init__(self, config: dict, callbacks: dict):
        self.config = config
        self.callbacks = callbacks  # 存储所有回调函数
        self.audio_segmenter = AudioSegmenter(config)
        self.speech_recognizer = SpeechRecognizer(config)
        self.translator = TranslationEngine(config)
        self.is_running = False
        self.stop_flag = False
        
    def start_processing(self, video_path: str) -> None:
        """启动处理流程"""
        pass
        
    def process_audio_segment(self, segment: AudioSegment) -> None:
        """处理单个音频段"""
        pass
        
    def _call_callback(self, callback_name: str, *args):
        """安全调用回调函数"""
        callback = self.callbacks.get(callback_name)
        if callback:
            try:
                callback(*args)
            except Exception as e:
                logger.error(f"Callback {callback_name} error: {e}")
```

#### 3. AudioSegmenter (音频分段器)

负责从视频中提取音频并分段处理。

```python
class AudioSegmenter:
    def __init__(self, config: dict):
        self.segment_duration = config.get('segment_duration', 8)  # 默认8秒
        self.overlap_duration = config.get('overlap_duration', 1)  # 重叠1秒
        self.temp_dir = tempfile.gettempdir()
        
    def extract_and_segment(self, video_path: str) -> Generator[AudioSegment, None, None]:
        """提取音频并分段"""
        # 复用现有的 extract_audio 函数
        from v2subpy.video.ff import extract_audio
        audio_path = extract_audio(video_path)
        
        # 使用 ffmpeg 分段处理音频
        yield from self._segment_audio(audio_path)
        
    def _segment_audio(self, audio_path: str) -> Generator[AudioSegment, None, None]:
        """将音频文件分段"""
        pass
```

#### 4. SpeechRecognizer (语音识别器)

封装 faster-whisper 的语音识别功能。

```python
class SpeechRecognizer:
    def __init__(self, config: dict):
        self.model = None
        self.config = config
        self._load_model()
        
    def _load_model(self) -> None:
        """加载 faster-whisper 模型"""
        pass
        
    def transcribe_segment(self, audio_segment: AudioSegment) -> SubtitleSegment:
        """转录音频段"""
        pass
```

#### 5. TranslationEngine (翻译引擎)

复用现有的翻译引擎接口。

```python
class TranslationEngine:
    def __init__(self, config: dict):
        self.config = config
        self.translator = self._create_translator()
        
    def _create_translator(self):
        """根据配置创建翻译器实例"""
        pass
        
    def translate_text(self, text: str, source_lang: str, target_lang: str) -> str:
        """翻译文本"""
        pass
```

## 数据模型设计

### 音频段数据模型

```python
@dataclass
class AudioSegment:
    segment_id: str          # 段落唯一标识
    start_time: float        # 开始时间（秒）
    end_time: float          # 结束时间（秒）
    audio_data: np.ndarray   # 音频数据
    sample_rate: int         # 采样率
    channels: int            # 声道数
    file_path: str = ""      # 临时文件路径（如果需要）
```

### 字幕段数据模型

```python
@dataclass
class SubtitleSegment:
    segment_id: str          # 段落标识
    start_time: float        # 开始时间（秒）
    end_time: float          # 结束时间（秒）
    original_text: str       # 原始文本
    translated_text: str     # 翻译文本
    confidence: float        # 识别置信度
    language: str           # 识别语言
    status: str = "completed"  # 状态
    
    def to_dict(self) -> dict:
        """转换为字典格式用于信号传输"""
        return asdict(self)
```

### 处理状态模型

```python
@dataclass
class ProcessingStatus:
    current_segment: int     # 当前处理段落索引
    total_segments: int      # 总段落数（估算）
    processed_duration: float # 已处理时长
    total_duration: float    # 总时长
    processing_speed: float  # 处理速度倍率
    buffer_level: int        # 缓冲区水平（0-100）
    
    def to_dict(self) -> dict:
        return asdict(self)
```

## 接口设计

### 主要接口函数

```python
def realtime_trans(video_path: str, config: dict = None) -> RealtimeTranslator:
    """
    创建并启动实时翻译处理
    
    Args:
        video_path: 视频文件路径
        config: 配置参数字典
        
    Returns:
        RealtimeTranslator: 实时翻译器实例
        
    Raises:
        FileNotFoundError: 视频文件不存在
        ValueError: 配置参数无效
        RuntimeError: 初始化失败
    """
    pass
```

### 回调函数接口

```python
# 字幕段准备就绪回调
def on_subtitle_ready(subtitle_dict: dict):
    """
    字幕段准备就绪回调函数
    
    Args:
        subtitle_dict: 字幕段数据
        {
            'segment_id': 'seg_001',
            'start_time': 10.5,
            'end_time': 18.2,
            'original_text': 'Hello world',
            'translated_text': '你好世界',
            'confidence': 0.95,
            'language': 'en'
        }
    """
    pass

# 进度更新回调（参考 merge_video 的模式）
def on_progress_update(status_message: str, progress_info: tuple):
    """
    进度更新回调函数
    
    Args:
        status_message: 状态消息，如 "正在处理音频段..."
        progress_info: (当前进度, 总进度) 元组，如 (45.2, 1800.0)
    """
    pass

# 错误回调
def on_error(error_message: str):
    """
    错误回调函数
    
    Args:
        error_message: 错误消息（JSON字符串格式）
        {
            'error_type': 'TranslationError',
            'error_message': '翻译服务暂时不可用',
            'segment_id': 'seg_003',
            'retry_count': 2,
            'can_retry': True
        }
    """
    pass

# 处理完成回调
def on_finished():
    """处理完成回调函数"""
    pass
```

## 处理流程设计

### 主处理流程

```mermaid
sequenceDiagram
    participant F as v2subqt Frontend
    participant W as Worker Thread
    participant B as v2subpy Backend
    participant P as Processing Pipeline
    
    F->>W: 启动实时翻译
    W->>B: realtime_trans(video_path, config, callbacks)
    B->>P: 创建处理管道
    P->>P: 提取音频并分段
    
    loop 处理每个音频段
        P->>P: 语音识别
        P->>P: 文本翻译
        P->>B: 调用 on_subtitle_ready 回调
        B->>W: 回调函数执行
        W->>F: 更新字幕显示
    end
    
    P->>B: 调用 on_finished 回调
    B->>W: 回调函数执行
    W->>F: 通知完成
```

### 错误处理流程

```mermaid
flowchart TD
    A[处理音频段] --> B{语音识别成功?}
    B -->|是| C[翻译文本]
    B -->|否| D[记录错误]
    D --> E[跳过该段]
    E --> F[继续下一段]
    
    C --> G{翻译成功?}
    G -->|是| H[发送字幕信号]
    G -->|否| I{重试次数<3?}
    I -->|是| J[等待后重试]
    J --> C
    I -->|否| K[发送原文]
    K --> H
    
    H --> F
```

## 性能优化设计

### 内存管理策略

1. **音频数据管理**
   - 使用循环缓冲区存储音频段
   - 及时释放已处理的音频数据
   - 限制同时在内存中的音频段数量（默认最多5个）

2. **字幕缓存策略**
   - 后端不保存字幕，处理完立即发送给前端
   - 前端负责字幕的缓存和管理
   - 使用弱引用避免内存泄漏

### 并发处理设计

```python
class ConcurrentProcessor:
    def __init__(self, max_workers: int = 3, callbacks: dict = None):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.speech_semaphore = Semaphore(2)  # 限制语音识别并发数
        self.translation_semaphore = Semaphore(3)  # 限制翻译并发数
        self.callbacks = callbacks or {}
        
    def process_segment_async(self, segment: AudioSegment) -> Future:
        """异步处理音频段"""
        return self.executor.submit(self._process_segment, segment)
        
    def _process_segment(self, segment: AudioSegment):
        """处理单个音频段"""
        try:
            # 语音识别
            with self.speech_semaphore:
                subtitle_segment = self.speech_recognizer.transcribe_segment(segment)
            
            # 翻译
            with self.translation_semaphore:
                translated_text = self.translator.translate_text(
                    subtitle_segment.original_text,
                    subtitle_segment.language,
                    self.config['target_language']
                )
                subtitle_segment.translated_text = translated_text
            
            # 调用回调
            if 'on_subtitle_ready' in self.callbacks:
                self.callbacks['on_subtitle_ready'](subtitle_segment.to_dict())
                
        except Exception as e:
            if 'on_error' in self.callbacks:
                self.callbacks['on_error'](str(e))
```

### 智能调度策略

1. **优先级处理**
   - 当前播放位置附近的段落优先处理
   - 根据缓冲区水平动态调整处理优先级

2. **自适应处理速度**
   - 监控处理速度与播放速度的比率
   - 动态调整并发数和处理策略

## 错误处理设计

### 异常类型定义

```python
class RealtimeProcessingError(Exception):
    """实时处理基础异常"""
    def __init__(self, message: str, segment_id: str = None, can_retry: bool = True):
        super().__init__(message)
        self.segment_id = segment_id
        self.can_retry = can_retry

class AudioExtractionError(RealtimeProcessingError):
    """音频提取异常"""
    pass

class SpeechRecognitionError(RealtimeProcessingError):
    """语音识别异常"""
    pass

class TranslationError(RealtimeProcessingError):
    """翻译异常"""
    pass
```

### 重试机制设计

```python
class RetryManager:
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        
    def retry_with_backoff(self, func, *args, **kwargs):
        """指数退避重试"""
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == self.max_retries:
                    raise
                delay = self.base_delay * (2 ** attempt)
                time.sleep(delay)
```

## 配置管理设计

### 配置参数结构

```python
@dataclass
class RealtimeConfig:
    # 音频处理配置
    segment_duration: float = 8.0      # 音频段长度（秒）
    overlap_duration: float = 1.0      # 重叠时长（秒）
    audio_sample_rate: int = 16000     # 音频采样率
    
    # 语音识别配置
    whisper_model: str = "base"        # Whisper模型
    whisper_device: str = "auto"       # 设备选择
    source_language: str = "auto"      # 源语言
    
    # 翻译配置
    translator_type: str = "openai"    # 翻译器类型
    target_language: str = "zh"        # 目标语言
    translation_timeout: float = 10.0  # 翻译超时
    
    # 性能配置
    max_concurrent_segments: int = 3   # 最大并发段数
    buffer_size: int = 10             # 缓冲区大小
    
    # 错误处理配置
    max_retries: int = 3              # 最大重试次数
    retry_delay: float = 1.0          # 重试延迟
    
    def from_dict(self, config_dict: dict) -> 'RealtimeConfig':
        """从字典创建配置"""
        pass
```

## 集成设计

### 与现有系统的集成

1. **复用现有组件**
   - 使用现有的 faster-whisper 集成代码
   - 复用现有的翻译引擎接口
   - 继承现有的配置管理系统

2. **扩展现有接口**
   - 在现有翻译器基础上添加实时处理能力
   - 扩展配置系统支持实时处理参数

### 前后端接口约定

```python
# v2subqt 调用示例
class RealtimeSubtitleWorker(QObject):
    # 定义信号用于与主线程通信
    subtitle_ready = Signal(dict)
    progress_updated = Signal(str, tuple)  # (status_message, progress_info)
    error_occurred = Signal(str)
    processing_finished = Signal()
    
    def __init__(self, video_path: str, config: dict):
        super().__init__()
        self.video_path = video_path
        self.config = config
        self.stop_flag = False
        
    def start_processing(self):
        """在 Worker Thread 中调用"""
        from v2subpy.realtime import realtime_trans
        
        # 定义回调函数
        def on_subtitle_ready(subtitle_dict):
            if not self.stop_flag:
                self.subtitle_ready.emit(subtitle_dict)
                
        def on_progress_update(status_message, progress_info):
            if not self.stop_flag:
                self.progress_updated.emit(status_message, progress_info)
                
        def on_error(error_message):
            if not self.stop_flag:
                self.error_occurred.emit(error_message)
                
        def on_finished():
            if not self.stop_flag:
                self.processing_finished.emit()
        
        # 调用后端接口
        realtime_trans(
            self.video_path, 
            self.config,
            on_subtitle_ready=on_subtitle_ready,
            on_progress_update=on_progress_update,
            on_error=on_error,
            on_finished=on_finished
        )
        
    def stop_processing(self):
        """停止处理"""
        self.stop_flag = True
```

## 测试策略

### 单元测试设计

1. **音频分段测试**
   - 测试不同时长视频的分段准确性
   - 验证重叠处理的正确性
   - 测试边界条件处理

2. **语音识别测试**
   - 测试不同语言的识别准确性
   - 验证置信度计算
   - 测试异常情况处理

3. **翻译功能测试**
   - 测试各种翻译引擎的集成
   - 验证重试机制
   - 测试并发翻译处理

### 集成测试设计

1. **端到端流程测试**
   - 完整的视频处理流程测试
   - 信号传输的准确性验证
   - 性能基准测试

2. **错误场景测试**
   - 网络中断恢复测试
   - 资源不足处理测试
   - 异常恢复能力测试

这个设计确保了实时字幕处理的高效性和稳定性，同时保持了与现有系统的良好集成。