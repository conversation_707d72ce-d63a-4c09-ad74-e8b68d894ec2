import os
from enum import Enum
import httpx
from urllib.parse import urljoin
from v2subpy.trans import langutil
from v2subpy.trans.chatgpt import ChatgptTranslator, GptConfigEnum
from v2subpy.utils.common import LLMMaxcharEnum
from v2subpy.utils.log import logger

class DeepSeekModel(str, Enum):
    DEEPSEEK_CHAT = 'deepseek-chat'
    DEEPSEEK_REASONER = 'deepseek-reasoner'
    
    def __str__(self) -> str:
        return str.__str__(self)

class DeepseekTranslator(ChatgptTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_openai(self.translator_config.get('api_key'))
        
    def setup_api_domain(self):
        '设置deepseek api 地址'
        api_domain = 'https://api.deepseek.com'
        self.replace_openai_domain(api_domain)
        
    def setup_openai(self, api_key: str) -> None:
        logger.debug(f'setup openai: {api_key}')
        os.environ[GptConfigEnum.OPENAI_API_KEY] = api_key
        self.setup_api_domain()
        if self.proxies:
            os.environ[GptConfigEnum.OPENAI_PROXY] = self.proxies['http']

    
class ModelbaoTranslator(ChatgptTranslator):
    default_api_domain = 'http://127.0.0.1:3333'
    default_api_key = '123456'
    
    @classmethod
    def get_models(cls, api_domain:str) -> list:
        '''
        获取模型列表
        '''
        if not api_domain:
            api_domain = cls.default_api_domain
        api_models = urljoin(api_domain, 'api/tags')
        try:
            response = httpx.get(api_models)
            response.raise_for_status()
            data = response.json()
            return [model['name'] for model in data.get('models', [])]
        except Exception as e:
            logger.error(f'Failed to get models from {api_models}: {e}')
            return []

    def setup_translator_config(self):
        d = {
            'api_key': self.default_api_key,
            'wait_for_retry' : 10,
            'timeout': (60, 300)
        }
        if langutil.is_cjk_lang(self.source_lang):
            self.translator_config["max_char"] = LLMMaxcharEnum.LOCAL_LLM_MAX_CJK.value
        else:  
            self.translator_config["max_char"] = LLMMaxcharEnum.LOCAL_LLM_MAX_EN.value

        self.translator_config.update(d)
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_openai(self.default_api_key)
        
    def setup_api_domain(self):
        '设置modelbao api 地址'
        api_domain = self.translator_config.get('api_domain', self.default_api_domain)
        self.replace_openai_domain(api_domain)
        
    def setup_openai(self, api_key: str) -> None:
        logger.debug(f'setup openai: {api_key}')
        os.environ[GptConfigEnum.OPENAI_API_KEY] = api_key
        self.setup_api_domain()

        
class CustomLLMTranslator(ChatgptTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_openai(self.translator_config.get('api_key'))
        
    def setup_api_domain(self):
        '设置自定义api地址'
        api_domain = api_domain = self.translator_config.get('api_domain')
        self.replace_openai_domain(api_domain)
        
    def setup_openai(self, api_key: str) -> None:
        logger.debug(f'setup openai: {api_key}')
        os.environ[GptConfigEnum.OPENAI_API_KEY] = api_key
        self.setup_api_domain()
        if self.proxies:
            os.environ[GptConfigEnum.OPENAI_PROXY] = self.proxies['http']
