"""
音频分段器

负责从视频文件中提取音频并将其分割成适合处理的时间段。
"""

import os
import tempfile
import uuid
from pathlib import Path
from typing import Generator, Optional
import ffmpeg

from v2subpy.utils.log import logger
from v2subpy.utils import config
from v2subpy.video.ff import extract_audio
from .models import AudioSegment, RealtimeConfig
from .exceptions import AudioExtractionError


class AudioSegmenter:
    """音频分段器类"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化音频分段器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.temp_dir = tempfile.gettempdir()
        self.segment_counter = 0
        
        logger.debug(f"AudioSegmenter 初始化: segment_duration={self.config.segment_duration}s, "
                    f"overlap_duration={self.config.overlap_duration}s")
    
    def extract_and_segment(self, video_path: str) -> Generator[AudioSegment, None, None]:
        """
        提取音频并分段
        
        Args:
            video_path: 视频文件路径
            
        Yields:
            AudioSegment: 音频段对象
            
        Raises:
            AudioExtractionError: 音频提取或分段失败
        """
        try:
            logger.info(f"开始提取音频: {video_path}")
            
            # 获取视频总时长
            total_duration = self._get_video_duration(video_path)
            logger.debug(f"视频总时长: {total_duration}s")
            
            # 提取完整音频文件
            audio_path = self._extract_full_audio(video_path)
            logger.debug(f"音频提取完成: {audio_path}")
            
            # 生成音频段
            yield from self._segment_audio(audio_path, total_duration)
            
        except Exception as e:
            logger.error(f"音频提取和分段失败: {e}")
            raise AudioExtractionError(
                f"音频提取和分段失败: {e}",
                video_path=video_path,
                can_retry=True
            )
    
    def _get_video_duration(self, video_path: str) -> float:
        """获取视频总时长"""
        try:
            probe = ffmpeg.probe(video_path)
            duration = float(probe['format']['duration'])
            return duration
        except Exception as e:
            logger.warning(f"无法获取视频时长，使用默认值: {e}")
            return 3600.0  # 默认1小时
    
    def _extract_full_audio(self, video_path: str) -> str:
        """提取完整音频文件"""
        try:
            # 复用现有的 extract_audio 函数
            return extract_audio(video_path)
        except Exception as e:
            raise AudioExtractionError(
                f"音频提取失败: {e}",
                video_path=video_path
            )
    
    def _segment_audio(self, audio_path: str, total_duration: float) -> Generator[AudioSegment, None, None]:
        """
        将音频文件分段
        
        Args:
            audio_path: 音频文件路径
            total_duration: 总时长
            
        Yields:
            AudioSegment: 音频段对象
        """
        current_time = 0.0
        segment_duration = self.config.segment_duration
        overlap_duration = self.config.overlap_duration
        
        while current_time < total_duration:
            # 计算段落的开始和结束时间
            start_time = max(0.0, current_time - overlap_duration)
            end_time = min(total_duration, current_time + segment_duration)
            
            # 如果剩余时间太短，合并到当前段落
            if total_duration - end_time < segment_duration * 0.3:
                end_time = total_duration
            
            # 创建音频段
            segment = self._create_audio_segment(
                audio_path, start_time, end_time
            )
            
            logger.debug(f"创建音频段: {segment.segment_id}, "
                        f"时间: {start_time:.2f}s - {end_time:.2f}s")
            
            yield segment
            
            # 移动到下一个段落
            current_time += segment_duration
            
            # 如果已经到达结尾，退出循环
            if end_time >= total_duration:
                break
    
    def _create_audio_segment(self, audio_path: str, start_time: float, end_time: float) -> AudioSegment:
        """
        创建音频段对象
        
        Args:
            audio_path: 原始音频文件路径
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            AudioSegment: 音频段对象
        """
        self.segment_counter += 1
        segment_id = f"seg_{self.segment_counter:04d}_{uuid.uuid4().hex[:8]}"
        
        # 创建临时音频段文件
        segment_file_path = self._extract_audio_segment(
            audio_path, start_time, end_time, segment_id
        )
        
        return AudioSegment(
            segment_id=segment_id,
            start_time=start_time,
            end_time=end_time,
            sample_rate=self.config.audio_sample_rate,
            channels=1,
            file_path=segment_file_path
        )
    
    def _extract_audio_segment(self, audio_path: str, start_time: float, 
                             end_time: float, segment_id: str) -> str:
        """
        提取音频段文件
        
        Args:
            audio_path: 原始音频文件路径
            start_time: 开始时间
            end_time: 结束时间
            segment_id: 段落ID
            
        Returns:
            str: 音频段文件路径
        """
        try:
            # 创建临时文件路径
            segment_filename = f"{segment_id}.wav"
            segment_path = Path(self.temp_dir) / segment_filename
            
            # 使用 ffmpeg 提取音频段
            duration = end_time - start_time
            (
                ffmpeg
                .input(audio_path, ss=start_time, t=duration)
                .output(
                    str(segment_path),
                    acodec="pcm_s16le",
                    ac=1,
                    ar=str(self.config.audio_sample_rate)
                )
                .run(quiet=True, overwrite_output=True)
            )
            
            return str(segment_path)
            
        except Exception as e:
            raise AudioExtractionError(
                f"音频段提取失败: {e}",
                segment_id=segment_id
            )
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            # 清理以 seg_ 开头的临时音频文件
            temp_path = Path(self.temp_dir)
            for file_path in temp_path.glob("seg_*.wav"):
                try:
                    file_path.unlink()
                    logger.debug(f"清理临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {file_path}, {e}")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {e}")
    
    def get_segment_info(self, video_path: str) -> dict:
        """
        获取分段信息（不实际分段）
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            dict: 分段信息
        """
        try:
            total_duration = self._get_video_duration(video_path)
            segment_count = int(total_duration / self.config.segment_duration) + 1
            
            return {
                'total_duration': total_duration,
                'segment_duration': self.config.segment_duration,
                'overlap_duration': self.config.overlap_duration,
                'estimated_segment_count': segment_count
            }
        except Exception as e:
            logger.error(f"获取分段信息失败: {e}")
            return {
                'total_duration': 0.0,
                'segment_duration': self.config.segment_duration,
                'overlap_duration': self.config.overlap_duration,
                'estimated_segment_count': 0
            }