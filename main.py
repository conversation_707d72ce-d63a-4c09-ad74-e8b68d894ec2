import time
import os
import sys
from v2subpy.utils import loadfile

loadfile.set_basedir(__file__)


def run():
    video_path = "d:/testvideo/en1.mp4"
    source_lang = 'en'
    target_lang = 'zh'
    translator_name = 'default'
    model_name = 'small'

    start = time.time()
    v2sub.gen_subtitles(video_path, source_lang=source_lang,
                        target_lang=target_lang, translator_name=translator_name,
                        model_name=model_name)
    end = time.time()
    logger.info(f"total  transcribe time is {end - start} seconds")
    input()


if __name__ == '__main__':
    from v2subpy.utils.log import logger
    from v2subpy.utils.checksys import print_info
    from v2subpy import v2sub
    print_info()
    v2sub.init_sys()
    run()
