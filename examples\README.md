# v2subpy 实时字幕集成示例

这个目录包含了如何将 v2subpy 实时字幕功能集成到不同类型应用中的示例代码。

## 文件说明

### 1. 集成文档
- **`../REALTIME_INTEGRATION_GUIDE.md`** - 完整的集成指南和API文档

### 2. 示例代码
- **`qt_integration_example.py`** - PyQt5 完整集成示例（推荐用于桌面应用）
- **`simple_integration_example.py`** - 命令行简单集成示例（适合脚本和测试）
- **`test_integration.py`** - 集成测试脚本（用于验证功能是否正常）

## 快速开始

### 1. 运行集成测试

首先运行测试脚本，确保实时字幕功能正常工作：

```bash
python examples/test_integration.py
```

这个脚本会：
- 检查所有依赖项是否已安装
- 测试配置类和异常类
- 验证API接口
- 测试回调函数机制

### 2. 命令行示例

使用命令行工具处理视频：

```bash
# 基本用法
python examples/simple_integration_example.py your_video.mp4

# 指定配置参数
python examples/simple_integration_example.py your_video.mp4 \
    --whisper-model base \
    --target-language zh \
    --translator openai

# 保存字幕到文件
python examples/simple_integration_example.py your_video.mp4 \
    --output subtitles.srt

# 查看所有选项
python examples/simple_integration_example.py --help
```

### 3. Qt 桌面应用示例

运行完整的桌面应用示例：

```bash
python examples/qt_integration_example.py
```

这个示例提供了：
- 图形化用户界面
- 配置参数设置
- 实时字幕显示
- 进度监控
- 错误处理
- 字幕保存功能

## 集成到你的项目

### 基本集成步骤

1. **导入模块**
```python
from v2subpy.realtime import realtime_trans
```

2. **定义回调函数**
```python
def on_subtitle_ready(subtitle_dict):
    # 处理新字幕
    print(f"新字幕: {subtitle_dict['translated_text']}")

def on_progress_update(status_message, progress_info):
    # 更新进度显示
    print(f"进度: {status_message}")

def on_error(error_json):
    # 处理错误
    print(f"错误: {error_json}")

def on_finished():
    # 处理完成
    print("翻译完成")
```

3. **调用翻译函数**
```python
config = {
    "whisper_model": "base",
    "target_language": "zh",
    "translator_type": "openai"
}

realtime_trans(
    video_path="your_video.mp4",
    config=config,
    on_subtitle_ready=on_subtitle_ready,
    on_progress_update=on_progress_update,
    on_error=on_error,
    on_finished=on_finished
)
```

### Qt 集成要点

如果你要集成到 Qt 应用中，重要的是：

1. **使用工作线程**
```python
class RealtimeWorker(QThread):
    subtitle_ready = pyqtSignal(dict)
    
    def run(self):
        realtime_trans(
            video_path=self.video_path,
            config=self.config,
            on_subtitle_ready=self._on_subtitle_ready
        )
    
    def _on_subtitle_ready(self, subtitle_dict):
        self.subtitle_ready.emit(subtitle_dict)
```

2. **连接信号到UI更新**
```python
self.worker = RealtimeWorker(video_path, config)
self.worker.subtitle_ready.connect(self.update_subtitle_display)
self.worker.start()
```

3. **正确处理线程生命周期**
```python
def stop_translation(self):
    if self.worker:
        self.worker.terminate()
        self.worker.wait()
```

## 配置参数说明

### 音频处理配置
- `segment_duration`: 音频段长度（秒），默认 8.0
- `overlap_duration`: 重叠时长（秒），默认 1.0
- `audio_sample_rate`: 音频采样率，默认 16000

### 语音识别配置
- `whisper_model`: Whisper模型 (tiny/base/small/medium/large)
- `whisper_device`: 设备选择 (auto/cpu/cuda)
- `source_language`: 源语言 (auto/en/zh/ja等)

### 翻译配置
- `translator_type`: 翻译引擎 (openai/google/baidu/deepl)
- `target_language`: 目标语言 (zh/en/ja等)
- `translation_timeout`: 翻译超时时间（秒）

### 性能配置
- `max_concurrent_segments`: 最大并发段数，默认 3
- `buffer_size`: 缓冲区大小，默认 10
- `max_retries`: 最大重试次数，默认 3

## 回调函数数据格式

### on_subtitle_ready 回调
```python
subtitle_dict = {
    "segment_id": "segment_001",
    "start_time": 10.5,
    "end_time": 15.2,
    "original_text": "Hello world",
    "translated_text": "你好世界",
    "confidence": 0.95,
    "language": "en",
    "status": "completed"
}
```

### on_progress_update 回调
```python
# 参数: (status_message, progress_info)
status_message = "正在处理音频段 3/10"
progress_info = (current_segment, total_segments, processed_duration, total_duration)
```

### on_error 回调
```python
error_data = {
    "error_type": "SpeechRecognitionError",
    "error_message": "识别失败",
    "segment_id": "segment_001",
    "can_retry": True,
    "error_code": "WHISPER_ERROR"
}
```

## 性能优化建议

### 高性能配置（强大硬件）
```python
config = {
    "whisper_model": "large",
    "whisper_device": "cuda",
    "max_concurrent_segments": 5,
    "segment_duration": 6.0
}
```

### 低延迟配置（实时性优先）
```python
config = {
    "whisper_model": "tiny",
    "whisper_device": "cpu",
    "max_concurrent_segments": 2,
    "segment_duration": 4.0
}
```

### 平衡配置（推荐默认）
```python
config = {
    "whisper_model": "base",
    "whisper_device": "auto",
    "max_concurrent_segments": 3,
    "segment_duration": 8.0
}
```

## 常见问题

### 1. 回调函数没有被调用
- 检查线程是否正确启动
- 确认回调函数没有抛出异常
- 验证信号连接是否正确（Qt应用）

### 2. 处理速度慢
- 使用更小的Whisper模型（tiny/base）
- 减少并发段数
- 检查硬件资源使用情况

### 3. 翻译质量差
- 确认源语言设置正确
- 检查翻译API配置
- 尝试不同的翻译引擎

### 4. 内存使用过高
- 减少缓冲区大小
- 降低音频采样率
- 减少并发处理段数

## 调试技巧

### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 性能监控
```python
import psutil
process = psutil.Process()
print(f"CPU: {process.cpu_percent()}%")
print(f"内存: {process.memory_info().rss / 1024 / 1024:.1f}MB")
```

### 错误处理
```python
def robust_error_handler(error_json):
    try:
        error_data = json.loads(error_json)
        error_type = error_data.get('error_type', 'Unknown')
        
        if error_type == 'SpeechRecognitionError':
            print("语音识别失败，建议检查音频质量")
        elif error_type == 'TranslationError':
            print("翻译失败，请检查网络连接和API配置")
        # ... 其他错误类型处理
    except json.JSONDecodeError:
        print(f"错误信息解析失败: {error_json}")
```

## 支持

如果在集成过程中遇到问题：

1. 首先运行 `test_integration.py` 确认基本功能正常
2. 查看详细的集成指南 `../REALTIME_INTEGRATION_GUIDE.md`
3. 参考示例代码中的错误处理方式
4. 检查日志输出获取更多调试信息

祝你集成顺利！🎉