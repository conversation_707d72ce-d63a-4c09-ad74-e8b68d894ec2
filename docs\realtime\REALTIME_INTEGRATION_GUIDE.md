# v2subpy 实时字幕后端集成指南

## 概述

本文档提供了如何将 v2subpy 实时字幕后端服务集成到前端应用（如 v2subqt）的详细指南和示例代码。

## API 接口

### 主要函数

```python
from v2subpy.realtime import realtime_trans

def realtime_trans(
    video_path: str, 
    config: Optional[Dict[str, Any]] = None,
    on_subtitle_ready: Optional[Callable[[dict], None]] = None,
    on_progress_update: Optional[Callable[[str, tuple], None]] = None,
    on_error: Optional[Callable[[str], None]] = None,
    on_finished: Optional[Callable[[], None]] = None
) -> None:
```

### 参数说明

- **video_path**: 视频文件路径（必需）
- **config**: 配置参数字典（可选）
- **on_subtitle_ready**: 字幕段准备就绪回调函数
- **on_progress_update**: 进度更新回调函数
- **on_error**: 错误回调函数
- **on_finished**: 处理完成回调函数

## 配置参数

```python
config = {
    # 音频处理配置
    "segment_duration": 8.0,      # 音频段长度（秒）
    "overlap_duration": 1.0,      # 重叠时长（秒）
    "audio_sample_rate": 16000,   # 音频采样率
    
    # 语音识别配置
    "whisper_model": "base",      # Whisper模型: tiny/base/small/medium/large
    "whisper_device": "auto",     # 设备选择: auto/cpu/cuda
    "source_language": "auto",    # 源语言: auto/en/zh/ja等
    
    # 翻译配置
    "translator_type": "openai",  # 翻译器类型: openai/google/baidu等
    "target_language": "zh",      # 目标语言
    "translation_timeout": 10.0,  # 翻译超时时间
    
    # 性能配置
    "max_concurrent_segments": 3, # 最大并发段数
    "buffer_size": 10,           # 缓冲区大小
    
    # 错误处理配置
    "max_retries": 3,            # 最大重试次数
    "retry_delay": 1.0           # 重试延迟
}
```

## 回调函数数据格式

### on_subtitle_ready 回调

```python
subtitle_data = {
    "segment_id": "segment_001",
    "start_time": 10.5,
    "end_time": 15.2,
    "original_text": "Hello world",
    "translated_text": "你好世界",
    "confidence": 0.95,
    "language": "en",
    "status": "completed"
}
```

### on_progress_update 回调

```python
# 参数格式: (status_message, progress_info)
status_message = "正在处理音频段 3/10"
progress_info = (current_segment, total_segments, processed_duration, total_duration)
```

### on_error 回调

```python
error_data = {
    "error_type": "SpeechRecognitionError",
    "error_message": "识别失败",
    "segment_id": "segment_001",
    "can_retry": True,
    "error_code": "WHISPER_ERROR"
}
```

## Qt 集成示例

### 1. Worker Thread 实现

```python
from PyQt5.QtCore import QThread, pyqtSignal
from v2subpy.realtime import realtime_trans
import json

class RealtimeTranslationWorker(QThread):
    # 定义信号
    subtitle_ready = pyqtSignal(dict)
    progress_updated = pyqtSignal(str, tuple)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()
    
    def __init__(self, video_path, config=None):
        super().__init__()
        self.video_path = video_path
        self.config = config or {}
        self.is_stopped = False
    
    def run(self):
        """在后台线程中运行实时翻译"""
        try:
            realtime_trans(
                video_path=self.video_path,
                config=self.config,
                on_subtitle_ready=self._on_subtitle_ready,
                on_progress_update=self._on_progress_update,
                on_error=self._on_error,
                on_finished=self._on_finished
            )
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _on_subtitle_ready(self, subtitle_dict):
        """字幕准备就绪回调"""
        if not self.is_stopped:
            self.subtitle_ready.emit(subtitle_dict)
    
    def _on_progress_update(self, status_message, progress_info):
        """进度更新回调"""
        if not self.is_stopped:
            self.progress_updated.emit(status_message, progress_info)
    
    def _on_error(self, error_json):
        """错误回调"""
        if not self.is_stopped:
            self.error_occurred.emit(error_json)
    
    def _on_finished(self):
        """完成回调"""
        if not self.is_stopped:
            self.finished.emit()
    
    def stop(self):
        """停止处理"""
        self.is_stopped = True
        self.terminate()
        self.wait()
```

### 2. 主窗口集成

```python
from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QProgressBar, QTextEdit
from PyQt5.QtCore import Qt
import json

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        self.start_button = QPushButton("开始实时翻译")
        self.start_button.clicked.connect(self.start_translation)
        layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止翻译")
        self.stop_button.clicked.connect(self.stop_translation)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)
        
        # 进度显示
        self.progress_label = QLabel("准备就绪")
        layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 字幕显示区域
        self.subtitle_display = QTextEdit()
        self.subtitle_display.setReadOnly(True)
        layout.addWidget(self.subtitle_display)
        
        # 错误日志
        self.error_log = QTextEdit()
        self.error_log.setMaximumHeight(100)
        layout.addWidget(self.error_log)
    
    def start_translation(self):
        """开始实时翻译"""
        video_path = "path/to/your/video.mp4"  # 替换为实际路径
        
        config = {
            "whisper_model": "base",
            "target_language": "zh",
            "translator_type": "openai",
            "segment_duration": 8.0
        }
        
        # 创建并启动工作线程
        self.worker = RealtimeTranslationWorker(video_path, config)
        
        # 连接信号
        self.worker.subtitle_ready.connect(self.on_subtitle_ready)
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.error_occurred.connect(self.on_error_occurred)
        self.worker.finished.connect(self.on_finished)
        
        # 启动线程
        self.worker.start()
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_label.setText("正在启动...")
    
    def stop_translation(self):
        """停止实时翻译"""
        if self.worker:
            self.worker.stop()
            self.worker = None
        
        # 更新UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_label.setText("已停止")
    
    def on_subtitle_ready(self, subtitle_dict):
        """处理字幕准备就绪事件"""
        # 格式化字幕显示
        start_time = subtitle_dict.get('start_time', 0)
        end_time = subtitle_dict.get('end_time', 0)
        original_text = subtitle_dict.get('original_text', '')
        translated_text = subtitle_dict.get('translated_text', '')
        
        # 时间格式化
        start_str = self.format_time(start_time)
        end_str = self.format_time(end_time)
        
        # 添加到显示区域
        subtitle_html = f"""
        <div style="margin-bottom: 10px; padding: 5px; border-left: 3px solid #007acc;">
            <strong>[{start_str} - {end_str}]</strong><br>
            <span style="color: #666;">{original_text}</span><br>
            <span style="color: #000; font-weight: bold;">{translated_text}</span>
        </div>
        """
        
        self.subtitle_display.append(subtitle_html)
        
        # 自动滚动到底部
        scrollbar = self.subtitle_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def on_progress_updated(self, status_message, progress_info):
        """处理进度更新事件"""
        self.progress_label.setText(status_message)
        
        if len(progress_info) >= 4:
            current_segment, total_segments, processed_duration, total_duration = progress_info
            
            # 更新进度条
            if total_duration > 0:
                progress_percent = int((processed_duration / total_duration) * 100)
                self.progress_bar.setValue(progress_percent)
    
    def on_error_occurred(self, error_json):
        """处理错误事件"""
        try:
            error_data = json.loads(error_json)
            error_message = error_data.get('error_message', '未知错误')
            error_type = error_data.get('error_type', 'Error')
            
            self.error_log.append(f"[{error_type}] {error_message}")
            
            # 如果是可重试的错误，可以考虑自动重试
            can_retry = error_data.get('can_retry', False)
            if can_retry:
                self.error_log.append("-> 系统将自动重试...")
        except json.JSONDecodeError:
            self.error_log.append(f"错误: {error_json}")
    
    def on_finished(self):
        """处理完成事件"""
        self.progress_label.setText("翻译完成")
        self.progress_bar.setValue(100)
        
        # 重置UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

# 使用示例
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
```

## 简单集成示例

如果不使用Qt，这里是一个简单的命令行集成示例：

```python
from v2subpy.realtime import realtime_trans
import json

def simple_subtitle_callback(subtitle_dict):
    """简单的字幕回调处理"""
    start_time = subtitle_dict.get('start_time', 0)
    end_time = subtitle_dict.get('end_time', 0)
    original = subtitle_dict.get('original_text', '')
    translated = subtitle_dict.get('translated_text', '')
    
    print(f"[{start_time:.1f}s - {end_time:.1f}s]")
    print(f"原文: {original}")
    print(f"译文: {translated}")
    print("-" * 50)

def simple_progress_callback(status_message, progress_info):
    """简单的进度回调处理"""
    print(f"进度: {status_message}")
    if len(progress_info) >= 4:
        current, total, processed_time, total_time = progress_info
        if total_time > 0:
            percent = (processed_time / total_time) * 100
            print(f"完成度: {percent:.1f}%")

def simple_error_callback(error_json):
    """简单的错误回调处理"""
    try:
        error_data = json.loads(error_json)
        print(f"错误: {error_data.get('error_message', '未知错误')}")
    except:
        print(f"错误: {error_json}")

def simple_finished_callback():
    """简单的完成回调处理"""
    print("实时翻译完成！")

# 使用示例
if __name__ == "__main__":
    video_path = "path/to/your/video.mp4"
    
    config = {
        "whisper_model": "base",
        "target_language": "zh",
        "translator_type": "openai"
    }
    
    try:
        realtime_trans(
            video_path=video_path,
            config=config,
            on_subtitle_ready=simple_subtitle_callback,
            on_progress_update=simple_progress_callback,
            on_error=simple_error_callback,
            on_finished=simple_finished_callback
        )
    except KeyboardInterrupt:
        print("用户中断处理")
    except Exception as e:
        print(f"处理失败: {e}")
```

## 最佳实践

### 1. 错误处理

```python
def robust_error_handler(error_json):
    """健壮的错误处理"""
    try:
        error_data = json.loads(error_json)
        error_type = error_data.get('error_type', 'Unknown')
        
        if error_type == 'SpeechRecognitionError':
            # 语音识别错误，可能需要调整模型或参数
            print("语音识别失败，建议检查音频质量或更换模型")
        elif error_type == 'TranslationError':
            # 翻译错误，可能是网络问题或API配置问题
            print("翻译失败，请检查网络连接和API配置")
        elif error_type == 'ConfigurationError':
            # 配置错误，需要修正配置参数
            print("配置错误，请检查配置参数")
        else:
            print(f"未知错误类型: {error_type}")
            
    except json.JSONDecodeError:
        print(f"错误信息解析失败: {error_json}")
```

### 2. 性能优化配置

```python
# 高性能配置（适用于强大的硬件）
high_performance_config = {
    "whisper_model": "large",
    "whisper_device": "cuda",
    "max_concurrent_segments": 5,
    "segment_duration": 6.0,
    "overlap_duration": 0.5
}

# 低延迟配置（适用于实时性要求高的场景）
low_latency_config = {
    "whisper_model": "tiny",
    "whisper_device": "cpu",
    "max_concurrent_segments": 2,
    "segment_duration": 4.0,
    "overlap_duration": 0.5
}

# 平衡配置（推荐的默认配置）
balanced_config = {
    "whisper_model": "base",
    "whisper_device": "auto",
    "max_concurrent_segments": 3,
    "segment_duration": 8.0,
    "overlap_duration": 1.0
}
```

### 3. 资源管理

```python
class RealtimeTranslationManager:
    """实时翻译管理器，负责资源管理和生命周期控制"""
    
    def __init__(self):
        self.worker = None
        self.is_running = False
    
    def start_translation(self, video_path, config, callbacks):
        """启动翻译"""
        if self.is_running:
            raise RuntimeError("翻译已在进行中")
        
        self.worker = RealtimeTranslationWorker(video_path, config)
        # 设置回调...
        self.worker.start()
        self.is_running = True
    
    def stop_translation(self):
        """停止翻译"""
        if self.worker and self.is_running:
            self.worker.stop()
            self.worker = None
            self.is_running = False
    
    def __del__(self):
        """确保资源清理"""
        self.stop_translation()
```

## 故障排除

### 常见问题

1. **回调函数没有被调用**
   - 检查线程是否正确启动
   - 确认回调函数没有抛出异常
   - 验证信号连接是否正确

2. **处理速度慢**
   - 尝试使用更小的Whisper模型
   - 减少并发段数
   - 检查硬件资源使用情况

3. **翻译质量差**
   - 确认源语言设置正确
   - 检查翻译API配置
   - 尝试不同的翻译引擎

4. **内存使用过高**
   - 减少缓冲区大小
   - 降低音频采样率
   - 减少并发处理段数

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 添加性能监控
import time
import psutil

def monitor_performance():
    """监控性能指标"""
    process = psutil.Process()
    print(f"CPU使用率: {process.cpu_percent()}%")
    print(f"内存使用: {process.memory_info().rss / 1024 / 1024:.1f}MB")
```

这个集成指南提供了完整的API文档、示例代码和最佳实践，应该能帮助你顺利完成前端集成。如果在集成过程中遇到具体问题，我可以进一步协助解决。