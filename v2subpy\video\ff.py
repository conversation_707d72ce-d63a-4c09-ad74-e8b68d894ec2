"""
all video process related function
"""
import sys
from pathlib import Path
import tempfile
import subprocess
import ffmpeg
from threading import Thread
from queue import Queue
from v2subpy.utils import common, config
from v2subpy.utils.checksys import check_gpu
from v2subpy.utils.log import logger
from v2subpy.utils.version_manage import VersionManager, Fn
from v2subpy.sub import subutils
from v2subpy.video.tts import trim_audio_to_video_length
VIDEO_FORMATS = None
AUDIO_FORMATS = None

def set_video_formats():
    global VIDEO_FORMATS, AUDIO_FORMATS
    if config.app_is_free():
        VIDEO_FORMATS = ['.avi', '.mp4', '.mkv']
        AUDIO_FORMATS = []
    else:
        VIDEO_FORMATS = ['.wmv', '.avi', '.asf', '.mpeg', '.mpg', '.ts', '.mp4', '.3gp', '.mkv', '.rm', '.rmvb',
                         '.webm', '.f4v', '.divx', '.vob', '.mov', '.flv', '.m4v']
        AUDIO_FORMATS = ['.wav', '.wma', '.mpa', '.mp2', '.mp3', '.ogg', '.m4a', '.aac', '.mka', '.ra', '.flac', '.ape', '.mpc',
                         '.mod', '.ac3', '.eac3', '.dts', '.dtshd', '.wv', '.tak', '.tta', '.aiff', '.aif', '.opus', '.amr']


def all_formats() -> list:
    '''
    return all supported suffixes in a list
    '''
    return VIDEO_FORMATS + AUDIO_FORMATS


def is_video_file(filepath: str) -> bool:
    '''
    检查是否为视频文件
    '''
    pf = Path(filepath)
    return pf.suffix in VIDEO_FORMATS


def get_sub_formats() -> list:
    sub_formats = VersionManager.get_values(Fn.SUB_FORMAT)
    if VersionManager.check_fn(Fn.GENERATE_TXT):
        sub_formats.append('txt')
    sub_formats = [f'.{ext}' for ext in sub_formats]
    return sub_formats


def build_formats_str(exts: list) -> str:
    '''
    转换一个后缀list 为一个 文件选择框的过滤器
    ['.avi', '.mp4', '.mkv']
    (*.avi *.mp4 *.mkv)
    '''
    ext_list = [f'*{ext} ' for ext in exts]
    ext_list_str = f"({''.join(ext_list).strip()})"
    return ext_list_str


def all_formats_front() -> dict:
    '''
    return supported formats for front end use in a dict
    example
    {
    'video': "(*.avi *.mp4 *.mkv)"
    }
    '''
    formats_d = {}
    video_list_str = build_formats_str(VIDEO_FORMATS)
    formats_d['video'] = video_list_str
    if not config.app_is_free():
        audio_list_str = build_formats_str(AUDIO_FORMATS)
        formats_d['audio'] = audio_list_str
        sub_formats = get_sub_formats()
        subtitle_list_str = build_formats_str(sub_formats)
        formats_d['subtitle'] = subtitle_list_str
    return formats_d


def extract_audio(file_path, duration=100000):
    # create an ffmpeg input stream from the video path
    in_path = Path(file_path)
    temp_dir = tempfile.gettempdir()
    out_path = Path(temp_dir).joinpath(f"{in_path.stem}.wav")
    try:
        logger.info(config.get_text('info.ExtractAudio'))
        ffmpeg.input(file_path, ss=0, t=duration).output(
        str(out_path),
        acodec="pcm_s16le", ac=1, ar="16k"
    ).run(quiet=True, overwrite_output=True)
    # logger.debug(f"done, out put file {out_path} ")
        return str(out_path)
    except:
        raise common.V2subError(config.get_text('exec.UnableExtractAudio'))



def extract_video(file_path, duration=100000):
    # create an ffmpeg input stream from the video path
    in_path = Path(file_path)
    # out_path = in_path.with_stem(in_path.stem + "_output")
    out_path = in_path.with_stem(in_path.stem + "_output")
    ffmpeg.input(file_path, ss=0, t=duration).output(
        str(out_path)
    ).run(quiet=True, overwrite_output=True)

    logger.debug(f"done, out put file {out_path} ")
    return str(out_path)


def merge_audio_with_video(video_path: str, audio_path: str, srt_path: str, *,  test_run: int = 0) -> str:
    '''
    使用ffmpeg库将音频与视频合并
    '''
    pf = Path(video_path)
    pf = pf.with_stem(f'{pf.stem}_output')
    output_path = str(pf)
    srt_pf = Path(srt_path)
    if test_run > 0:
        logger.debug(f'test run: {test_run}')
    # subprocess.run(["ffmpeg", "-i", video_path, "-i", audio_path, "-i", srt_path, "-map", "0:v", "-map", "1:a",
    #                 "-map", "2:s", "-c:v", "copy", "-c:a", "copy", "-c:s", "mov_text", "-y", "-shortest", output_path])
    args = ["ffmpeg", "-i", video_path, "-i", audio_path, "-vf", f"subtitles=\\'{srt_pf.as_posix()}\\':force_style=\\'FontName=Microsoft YaHei UI\\,FontSize=12\\,PrimaryColour=&H000000FF\\'",
            "-vcodec", "libx264", "-acodec", "aac", '-map', '0:v', '-map', '1:a', '-f', 'mp4', "-y", output_path]
    print(args)
    subprocess.call(args=args,
                    shell=True)
    return output_path

# ffmpeg -i input_video.mp4 -i input_audio.mp3 -vf "subtitles=input_subtitle.srt:force_style='FontName=Arial,FontSize=24'" -c:v libx264 -c:a aac -strict experimental -b:a 192k -shortest output_video.mp4


def reader(pipe, queue):
    try:
        with pipe:
            for line in iter(pipe.readline, b''):
                queue.put((pipe, line))
    finally:
        queue.put(None)


def call_back(status_message, progress_into):
    progress = 100 * (progress_into[0] / progress_into[1])
    print(f'{status_message} , progress: {progress:.2f}%')


def int_to_hex(num: int) -> str:
    num = 100 - num
    num = num * 255 // 100
    return f'{num:02X}'


def font_positon_style(font_position: int) -> tuple:
    '''
    根据位置返回相应样式 (alignment, marginv)
    将font_position(0-100), 转换为0-260的值
    '''
    alignment = '2'
    marginv = int((font_position / 100.0) * 265)
    return alignment, marginv


def path_converter(path):
    # 需要转义字幕路径里的空格,引号, 逗号等
    return (path
            .replace("\\", "/")
            .replace("'", "\\'\\\\\\'\\'")
            .replace(",", "\\,")
            .replace("[", "\\[")
            .replace("]", "\\]")

            )


def make_font_style(srt_path: str, font_profile: dict | None) -> str:
    '''
    根据字体配置， 组合生成相应style
    '''
    srt_pf = Path(srt_path)

    path_str = path_converter(str(srt_pf))
    # path_str = r"D:\\test_files\\test-merge\\33\'\\\'\'ta_en.srt"

    if font_profile:
        font_name = font_profile['font_name']
        font_size = font_profile['font_size']
        font_color = font_profile['font_color']
        font_color = f'{font_color[5:]}{font_color[3:5]}{font_color[1:3]}'
        font_transparency = font_profile['font_transparency']
        font_transparency = int_to_hex(font_transparency)
        font_backcolor = font_profile['font_backcolor']
        font_backcolor = f'{font_backcolor[5:]}{font_backcolor[3:5]}{font_backcolor[1:3]}'
        font_back_transparency = font_profile['font_back_transparency']
        font_back_transparency = int_to_hex(font_back_transparency)
        font_position = font_profile['font_position']
        alignment, marginv = font_positon_style(font_position)
        color = f"&H{font_transparency}{font_color}"
        backcolor = f"&H{font_back_transparency}{font_backcolor}"
        # font_name 为单个词比如 Arial 时会出错， 加一个空格可解决
        if not ' ' in font_name:
            font_name = font_name + ' '

        font_style = f"subtitles=\\'{path_str}\\':force_style=\\'FontName={font_name}\\,FontSize={font_size}\\,PrimaryColour={color}\\,BackColour={backcolor}\\,BorderStyle=4\\,WrapStyle=2\\,Alignment={alignment}\\,MarginL=1\\,MarginR=1\\,MarginV={marginv}\\'"
    else:
        font_style = f"subtitles=\\'{path_str}\\'"
    logger.debug(font_style)
    return font_style


def handle_sub_wrap(srt_path: str, font_profile: dict | None) -> str:
    '''
    处理是否格式化字幕， 自动换行
    font_wrap_length = 0 时不处理
    '''
    if not font_profile:
        return srt_path
    # TODO 双语字幕跳过格式化
    pf = Path(srt_path)
    if '_merge' in pf.stem:
        logger.debug('merge subtitlle, skip handle sub wrap')
        return srt_path
    line_wrap_limit = font_profile['line_wrap_limit']
    fill_back_ground = font_profile['fill_back_ground']
    if line_wrap_limit == 0 and not fill_back_ground:
        return srt_path
    # 重新写一个临时字幕文件

    sub = subutils.read_file(srt_path, '')
    # 需要wrap行
    if line_wrap_limit != 0:
        sub.wrap_lines(line_wrap_limit)
    # 需要填充背景
    if fill_back_ground:
        sub.subtitles = subutils.handle_sub_fill_gap(sub.subtitles)
    in_path = Path(srt_path)
    temp_dir = tempfile.gettempdir()
    out_path_pf = Path(temp_dir).joinpath(f"{in_path.name}")
    out_path = str(out_path_pf)
    logger.debug(f'temp subfile path: {out_path}')
    subtitles = sub.compose(sub.subtitles)
    with open(out_path, "w", encoding="utf-8") as file_out:
        file_out.write(subtitles)
    return out_path


def get_vcodec() -> str:
    '''
    感觉平台，系统返回合适的视频编码器
    默认为libx264
    支持cuda的为 h264_nvenc
    '''
    vcodec = 'libx264'
    if check_gpu():
        vcodec = 'h264_nvenc'
    return vcodec


def merge_video(video_path: str, audio_path: str | None, srt_path: str | None, *, test_run: int = 0, update_progress=None) -> str:
    # Create input streams for audio, video, and srt (if available)
    logger.debug(f'merge video: {video_path}, {audio_path=}, {srt_path=}, {test_run=}')
    pf = Path(video_path)
    pf = pf.with_stem(f'{pf.stem}_output')
    pf = pf.with_suffix(".mp4")
    output_path = str(pf)
    total_duration = float(ffmpeg.probe(
        video_path)['format']['duration'])
    logger.debug(f'{total_duration=}')
    error = []
    
    # Check if audio needs trimming to match video length
    if audio_path:
        # Get a temp file with audio trimmed to match video length if needed
        audio_path = trim_audio_to_video_length(audio_path, video_path, update_progress)
    
    audio = ffmpeg.input(audio_path) if audio_path else None
    if test_run > 0:
        video = ffmpeg.input(video_path, ss=0, t=test_run)
        total_duration = min(test_run, total_duration)
    else:
        video = ffmpeg.input(video_path)

    # Merge audio, video, and srt together
    args = [video.video]
    if audio:
        args.append(audio.audio)
    else:
        args.append(video.audio)

    ffmpeg_args = {}
    if srt_path:
        profile = common.get_profile()
        font_profile = profile.get('video_config')
        srt_path = handle_sub_wrap(srt_path, font_profile)
        sub_arg = make_font_style(srt_path, font_profile)

        ffmpeg_args['vf'] = sub_arg
    ffmpeg_args['vcodec'] = get_vcodec()
    ffmpeg_args['acodec'] = 'aac'
    ffmpeg_args['f'] = 'mp4'
    ffmpeg_args['qscale'] = 0
    # ffmpeg_args['shortest'] = None
    ffmpeg_args['y'] = None

    args.append(output_path)
    cmd = (ffmpeg.output(*args, **ffmpeg_args))
    logger.debug(f'ffmpeg args: {cmd.get_args()}')
    # cmd.run(overwrite_output=True)
    try:
        videoproc = (
            cmd
            .global_args('-progress', 'pipe:1')
            .run_async(pipe_stdout=True, pipe_stderr=True)
        )
        q = Queue()
        Thread(target=reader, args=[videoproc.stdout, q]).start()
        Thread(target=reader, args=[videoproc.stderr, q]).start()
        for _ in range(2):
            for source, line in iter(q.get, None):
                line = line.decode('utf-8', errors='ignore')
                if source == videoproc.stderr:
                    error.append(line)
                else:
                    line = line.rstrip()
                    parts = line.split('=')
                    key = parts[0] if len(parts) > 0 else None
                    value = parts[1] if len(parts) > 1 else None
                    if key == 'out_time_ms':
                        time = max(round(float(value) / 1000000., 2), 0)
                        progress_info = (time, total_duration)
                        status_message = config.get_text(
                            'info.StatusGenerateVideo')
                        # logger.debug(
                        #     f'{status_message},  progress: {progress_info}')
                        if update_progress is not None:
                            update_progress(status_message, progress_info)

                    elif key == 'progress' and value == 'end':
                        progress_info = (total_duration, total_duration)
                        status_message = config.get_text(
                            'info.StatusGenerateVideo')
                        if update_progress is not None:
                            update_progress(status_message, progress_info)
                        logger.debug('done')
    except ffmpeg.Error as e:
        logger.error(error)
    logger.debug(f'create video, {output_path}')
    return output_path
