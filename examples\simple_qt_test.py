#!/usr/bin/env python3
"""
简化的Qt集成测试

用于测试基本的Qt集成功能，不依赖复杂的组件。
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
    from PySide6.QtCore import Qt, QTimer
    QT_VERSION = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
        from PyQt6.QtCore import Qt, QTimer
        QT_VERSION = "PyQt6"
    except ImportError:
        try:
            from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
            from PyQt5.QtCore import Qt, QTimer
            QT_VERSION = "PyQt5"
        except ImportError:
            print("请安装PySide6、PyQt6或PyQt5")
            sys.exit(1)

# 测试导入
try:
    from v2subpy.realtime.integrations import AsyncRealtimeTranslator
    print("✓ 成功导入AsyncRealtimeTranslator")
except ImportError as e:
    print(f"✗ 导入AsyncRealtimeTranslator失败: {e}")
    sys.exit(1)

try:
    from integration_helpers import QtRealtimeTranslator
    print("✓ 成功导入QtRealtimeTranslator")
except ImportError as e:
    print(f"✗ 导入QtRealtimeTranslator失败: {e}")
    sys.exit(1)


class SimpleTestWindow(QMainWindow):
    """简单的测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self, max_concurrent_tasks=1)
        self.current_task_id = None
        
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"简单Qt集成测试 ({QT_VERSION})")
        self.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_btn = QPushButton("开始测试翻译")
        self.test_btn.clicked.connect(self.start_test_translation)
        layout.addWidget(self.test_btn)
        
        # 停止按钮
        self.stop_btn = QPushButton("停止翻译")
        self.stop_btn.clicked.connect(self.stop_translation)
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
    
    def connect_signals(self):
        """连接信号"""
        self.translator.subtitle_ready.connect(self.on_subtitle_ready)
        self.translator.progress_updated.connect(self.on_progress_updated)
        self.translator.error_occurred.connect(self.on_error_occurred)
        self.translator.task_finished.connect(self.on_task_finished)
        self.translator.task_started.connect(self.on_task_started)
    
    def start_test_translation(self):
        """开始测试翻译"""
        # 使用一个不存在的视频文件来测试错误处理
        test_video = "test_video.mp4"
        
        config = {
            'whisper_model': 'small',
            'target_language': 'zh',
            'translation_engine': 'default'
        }
        
        try:
            self.current_task_id = self.translator.start_translation(test_video, config)
            
            if self.current_task_id:
                self.status_label.setText(f"启动测试任务: {self.current_task_id}")
                self.test_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.result_text.append(f"启动测试翻译任务: {self.current_task_id}")
            else:
                self.status_label.setText("启动任务失败")
                self.result_text.append("启动任务失败")
                
        except Exception as e:
            self.status_label.setText(f"错误: {e}")
            self.result_text.append(f"启动任务异常: {e}")
    
    def stop_translation(self):
        """停止翻译"""
        if self.current_task_id:
            success = self.translator.stop_translation(self.current_task_id)
            if success:
                self.status_label.setText("任务已停止")
                self.result_text.append("任务已停止")
            else:
                self.status_label.setText("停止任务失败")
                self.result_text.append("停止任务失败")
            
            self.reset_ui()
    
    def reset_ui(self):
        """重置UI状态"""
        self.test_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.current_task_id = None
    
    def on_subtitle_ready(self, task_id: str, subtitle_data: dict):
        """字幕准备就绪"""
        self.result_text.append(f"[{task_id[:8]}] 字幕: {subtitle_data}")
    
    def on_progress_updated(self, task_id: str, progress_data: dict):
        """进度更新"""
        message = progress_data.get('message', '')
        self.status_label.setText(f"[{task_id[:8]}] {message}")
        self.result_text.append(f"[{task_id[:8]}] 进度: {message}")
    
    def on_error_occurred(self, task_id: str, error_data: dict):
        """错误发生"""
        error_message = error_data.get('message', error_data.get('error', 'Unknown error'))
        self.status_label.setText(f"错误: {error_message}")
        self.result_text.append(f"[{task_id[:8]}] 错误: {error_message}")
        self.reset_ui()
    
    def on_task_finished(self, task_id: str, result_data: dict):
        """任务完成"""
        self.status_label.setText("任务完成")
        self.result_text.append(f"[{task_id[:8]}] 任务完成")
        self.reset_ui()
    
    def on_task_started(self, task_id: str):
        """任务启动"""
        self.result_text.append(f"[{task_id[:8]}] 任务已启动")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.current_task_id:
            self.translator.stop_translation(self.current_task_id)
        
        # 停止所有任务
        self.translator.stop_all_tasks()
        event.accept()


def main():
    """主函数"""
    print(f"使用Qt版本: {QT_VERSION}")
    
    app = QApplication(sys.argv)
    app.setApplicationName("简单Qt集成测试")
    
    window = SimpleTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()