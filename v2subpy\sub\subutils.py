'''
subtitles related functions
'''
from datetime import timedelta
from pathlib import Path
import time
import zhconv
import srt
from pysubs2 import SSAFile, SSAStyle
from v2subpy.utils import common
from v2subpy.utils.config import get_key, get_text
from v2subpy.trans.langutil import is_cjk_lang
from v2subpy.utils.version_manage import Fn, VersionManager
from v2subpy.utils.log import logger

def handle_chinese_result(result):
    segments = result.segments

    for seg in segments:
        seg.text = handle_chinese_segment(seg)
    result.segments = segments


def handle_chinese_text(text):
    chinese_style = get_key('sub.chinese_style')
    code = 'zh-cn' if chinese_style else 'zh-tw'
    text = zhconv.convert(text, code)
    return text


def handle_chinese_segment(segment):
    chinese_style = get_key('sub.chinese_style')
    code = 'zh-cn' if chinese_style else 'zh-tw'
    text = zhconv.convert(segment.text, code)
    return text


def handle_chinese_subtitles(subtitles):
    chinese_style = get_key('sub.chinese_style')
    code = 'zh-cn' if chinese_style else 'zh-tw'
    for sub in subtitles:
        sub.content = zhconv.convert(sub.content, code)


def handle_yue_content(content):
    code = 'zh-tw'
    content = zhconv.convert(content, code)
    return content


def get_wrap_line_limit(lang_code) -> int:
    '''
    根据语言代码返回需要的换行字符数
    '''
    wrap_length = 50
    if is_cjk_lang(lang_code):
        wrap_length = 20
    return wrap_length


def read_file(file_path, target_lang):
    from v2subpy.sub.sub_file import SubFile

    pf = Path(file_path)
    ext = pf.suffix[1:]
    reader = SubFile.get_reader(ext)
    sub = reader(file_path, target_lang, print_info=False, need_clean=False)
    return sub


def write_sub_to_txt(file_path: str) -> str:
    sub = read_file(file_path, '')
    pf = Path(file_path)
    txt_file = str(pf.with_suffix('.txt'))
    with open(txt_file, 'w', encoding='utf-8') as f:
        for s in sub.subtitles:
            print(s.content, file=f)
    return txt_file


def make_fill_subtitle(start, end):
    content = f'.{"　" * 50}'
    subtitle = srt.Subtitle(index=0, start=start,
                            end=end, content=content)
    return subtitle


def handle_sub_fill_gap(subtitles):
    '''
    对字幕空白时间填充一个空行
    产生背景始终覆盖效果
    '''
    filled_subtitles = []
    previous_time = timedelta(microseconds=0)
    for subtitle in subtitles:
        if subtitle.start > previous_time:
            new_subtitle = make_fill_subtitle(previous_time, subtitle.start)
            filled_subtitles.append(new_subtitle)
        filled_subtitles.append(subtitle)
        previous_time = subtitle.end
    for index, subtitle in enumerate(filled_subtitles, start=1):
        subtitle.index = index
    return filled_subtitles

def sub_format_txt(subs, out_f):
    '''
    将任意其他格式SSAFile转为txt格式
    '''
    text_lines = [] 
    for sub in subs:
        text_lines.append(sub.plaintext)
    Path(out_f).write_text('\n'.join(text_lines), encoding='utf-8')

def clean_subs_style(subs):
    '''
    清除字幕格式
    '''
    default_style = SSAStyle()
    subs.styles.clear()
    subs.styles['Default'] = default_style
    for sub in subs:
        sub.text = sub.plaintext
        sub.style = 'Default'
        sub.text = sub.text.replace("\n", "\\N")

def sub_transform(in_file: str, out_folder: str, out_format: str, clean_style:bool = False, update_progress=None):
    '''
    字幕格式转换, 支持批量
    '''
    logger.debug(f'{in_file=}, {out_folder=}, {out_format=}, {clean_style=}')
    file_list = []
    sub_formats = ["."+format for format in VersionManager.get_values(Fn.SUB_FORMAT)]
    pf = Path(in_file)
    if not pf.is_dir():
        file_list.append(in_file)
    else:
        file_list = [str(p) for p in pf.rglob(
                '*') if p.suffix.lower() in sub_formats]
    logger.debug(f'{file_list=}')
    out_file_list = []
    out_foler_pf = Path(out_folder) if out_folder else None
    for f in file_list:
        if not out_foler_pf:
            # 保存在同目录
            out_f = str(Path(f).with_suffix(out_format))
        else:
            out_name = Path(f).with_suffix(out_format).name
            out_f = str(out_foler_pf / out_name)
        out_file_list.append(out_f)
    logger.debug(f'{out_file_list=}')
    i = 0
    total_file = len(file_list)
    try:
        for in_f, out_f in zip(file_list, out_file_list):
            i += 1
            subs = SSAFile.load(in_f)
            if out_format == '.txt':
                sub_format_txt(subs, out_f)
            else:
                if clean_subs_style:
                    clean_subs_style(subs)
                subs.save(out_f)
            logger.debug(f'convert {in_f} to {out_f}')
            if update_progress:
                status_message = get_text('info.StatusSubTransform')
                progress_info = (i, total_file)
                update_progress(status_message, progress_info)
            time.sleep(1)
    except Exception as e:
        logger.debug(str(e))
        raise common.V2subError(get_text('error.SubTransformError'))

def make_aligned_merge_sub(aligned_sub_path: str, sub_path: str) -> str:
    '''
    根据对齐字幕调整里一个字幕时间轴
    返回: 生成一个临时字幕路径
    '''
    subs_aligned = SSAFile.load(aligned_sub_path)
    subs = SSAFile.load(sub_path)
    # 如果长度不等, 直接返回原字幕路径
    if len(subs_aligned) != len(subs):
        return sub_path
    for sub_align, sub in zip(subs_aligned, subs):
        sub.start = sub_align.start
        sub.end = sub_align.end
    pf = Path(sub_path)
    out_name = pf.stem + '_aligned'
    logger.debug(f'make aligned sub: {out_name}')
    out_pf = pf.with_stem(out_name)
    out_path = str(out_pf)
    if not out_pf.exists():
        logger.debug(f'save file : {out_path}')
        subs.save(out_path)
    else:
        logger.debug(f'skip file: {out_path}')
    return out_path
    