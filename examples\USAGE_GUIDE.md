# V2subpy Qt集成使用指南

## 概述

本指南介绍如何将v2subpy的实时翻译功能集成到Qt应用程序中。我们提供了多种集成方式，从简单的直接使用到完整的Qt封装。

## 快速开始

### 1. 最简单的集成方式

直接使用`AsyncRealtimeTranslator`：

```python
from v2subpy.realtime.integrations import AsyncRealtimeTranslator

# 创建翻译器
translator = AsyncRealtimeTranslator(max_concurrent_tasks=2)

# 定义回调函数
def on_subtitle_ready(task_id, subtitle_data):
    print(f"字幕: {subtitle_data}")

def on_error(task_id, error_data):
    print(f"错误: {error_data}")

def on_finished(task_id, result_data):
    print(f"完成: {task_id}")

# 启动翻译
callbacks = {
    'on_subtitle_ready': on_subtitle_ready,
    'on_error': on_error,
    'on_finished': on_finished
}

config = {
    'whisper_model': 'small',
    'target_language': 'zh',
    'translation_engine': 'default'
}

task_id = translator.start_translation("video.mp4", config, callbacks)
```

### 2. Qt信号集成方式

使用Qt信号进行线程安全的通信：

```python
from PySide6.QtCore import QObject, Signal
from v2subpy.realtime.integrations import AsyncRealtimeTranslator

class TranslationHandler(QObject):
    subtitle_ready = Signal(str, dict)
    error_occurred = Signal(str, dict)
    
    def __init__(self):
        super().__init__()
        self.translator = AsyncRealtimeTranslator()
    
    def start_translation(self, video_path, config):
        callbacks = {
            'on_subtitle_ready': self._on_subtitle_ready,
            'on_error': self._on_error
        }
        return self.translator.start_translation(video_path, config, callbacks)
    
    def _on_subtitle_ready(self, task_id, subtitle_data):
        self.subtitle_ready.emit(task_id, subtitle_data)
    
    def _on_error(self, task_id, error_data):
        self.error_occurred.emit(task_id, error_data)
```

### 3. 使用Qt翻译器封装

使用我们提供的Qt专用封装：

```python
from examples.integration_helpers import QtRealtimeTranslator

class MyApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self)
        
        # 连接信号
        self.translator.subtitle_ready.connect(self.on_subtitle_ready)
        self.translator.error_occurred.connect(self.on_error_occurred)
    
    def start_translation(self):
        config = {'whisper_model': 'small', 'target_language': 'zh'}
        task_id = self.translator.start_translation("video.mp4", config)
    
    def on_subtitle_ready(self, task_id, subtitle_data):
        # 在主线程中安全地更新UI
        self.update_subtitle_display(subtitle_data)
```

## 配置选项

### 基本配置

```python
config = {
    # Whisper模型配置
    'whisper_model': 'small',  # tiny, base, small, medium, large
    'device': 'auto',          # auto, cpu, cuda
    
    # 翻译配置
    'target_language': 'zh',   # 目标语言代码
    'translation_engine': 'default',  # default, google, baidu
    
    # 音频处理配置
    'audio_segment_length': 10.0,  # 音频段长度（秒）
    'audio_overlap': 1.0,          # 音频段重叠（秒）
    
    # 其他配置
    'max_retries': 3,          # 最大重试次数
    'timeout': 30.0            # 超时时间（秒）
}
```

### 高级配置

```python
config = {
    'whisper_model': 'medium',
    'target_language': 'en',
    'translation_engine': 'default',
    'segment_duration': 15.0,
    'segment_overlap': 2.0,
    'buffer_size': 20,
    'max_concurrent_segments': 4,
    'retry_delay': 2.0
}
```

## 回调函数

### 字幕准备就绪回调

```python
def on_subtitle_ready(task_id: str, subtitle_data: dict):
    # subtitle_data 包含:
    # - segment_id: 段ID
    # - start_time: 开始时间（秒）
    # - end_time: 结束时间（秒）
    # - original_text: 原文
    # - translated_text: 译文
    # - status: 状态
    pass
```

### 进度更新回调

```python
def on_progress_update(task_id: str, progress_data: dict):
    # progress_data 包含:
    # - message: 进度消息
    # - progress_info: (当前进度, 总进度) 元组
    pass
```

### 错误回调

```python
def on_error(task_id: str, error_data: dict):
    # error_data 包含:
    # - error_type: 错误类型
    # - message: 错误消息
    # - details: 错误详情
    pass
```

### 完成回调

```python
def on_finished(task_id: str, result_data: dict):
    # result_data 包含:
    # - task_id: 任务ID
    # - 其他结果信息
    pass
```

## 任务管理

### 启动任务

```python
task_id = translator.start_translation(video_path, config, callbacks)
```

### 停止任务

```python
success = translator.stop_translation(task_id)
```

### 暂停/恢复任务

```python
# 暂停
success = translator.pause_translation(task_id)

# 恢复
success = translator.resume_translation(task_id)
```

### 查询任务状态

```python
status = translator.get_task_status(task_id)
# 返回包含状态信息的字典
```

### 列出所有任务

```python
tasks = translator.list_tasks()
# 返回任务ID到状态的映射
```

## 错误处理

### 常见错误类型

1. **ConfigurationError**: 配置错误
2. **AudioExtractionError**: 音频提取错误
3. **SpeechRecognitionError**: 语音识别错误
4. **TranslationError**: 翻译错误
5. **TaskNotFoundError**: 任务不存在
6. **ResourceError**: 资源错误

### 错误处理示例

```python
def on_error(task_id, error_data):
    error_type = error_data.get('error_type', 'Unknown')
    message = error_data.get('message', 'Unknown error')
    
    if error_type == 'ConfigurationError':
        # 处理配置错误
        print(f"配置错误: {message}")
    elif error_type == 'AudioExtractionError':
        # 处理音频提取错误
        print(f"音频提取失败: {message}")
    else:
        # 处理其他错误
        print(f"未知错误: {message}")
```

## 最佳实践

### 1. 线程安全

- 所有回调函数都在后台线程中执行
- 如果需要更新UI，使用Qt信号或`QMetaObject.invokeMethod`
- 避免在回调中直接操作Qt对象

### 2. 资源管理

- 在应用程序退出时停止所有翻译任务
- 定期清理已完成的任务
- 监控内存使用情况

### 3. 错误处理

- 始终处理错误回调
- 提供用户友好的错误信息
- 实现适当的重试机制

### 4. 性能优化

- 根据硬件能力调整并发任务数
- 选择合适的Whisper模型大小
- 优化音频段长度和重叠设置

## 示例应用

查看以下示例文件：

- `examples/minimal_qt_test.py` - 最简单的集成示例
- `examples/simple_qt_test.py` - 使用Qt封装的示例
- `examples/qt_integration_example.py` - 完整的Qt应用程序示例
- `examples/async_integration_example.py` - 通用异步集成示例

## 故障排除

### 常见问题

1. **Qt定时器线程错误**
   - 确保Qt对象在主线程中创建
   - 使用信号进行跨线程通信

2. **模型加载失败**
   - 检查网络连接
   - 确认模型文件路径正确

3. **翻译服务错误**
   - 检查API密钥配置
   - 确认网络连接正常

4. **音频提取失败**
   - 确认视频文件格式支持
   - 检查FFmpeg安装

### 调试技巧

1. 启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. 使用测试模式：
```python
# 使用模拟组件进行测试
config['use_mock_components'] = True
```

3. 监控任务状态：
```python
# 定期检查任务状态
status = translator.get_task_status(task_id)
print(f"任务状态: {status}")
```

## 支持

如果遇到问题，请：

1. 查看日志输出
2. 检查配置是否正确
3. 尝试使用最简单的示例
4. 查看错误回调中的详细信息

更多信息请参考项目文档和示例代码。