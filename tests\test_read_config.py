from v2subpy.utils import config


def test_get_sys_config():
    # first time
    cfg = config.get_sys_config()
    print(cfg['language'])
    # second time
    cfg = config.get_sys_config()
    print(cfg['source_lang_index'])


def test_get_key():
    key = 'language'
    print(config.get_key(key))

    key = 'sub.need_merge'
    print(config.get_key(key))


def test_get_lang_key():
    key = 'info.EnableProtectedMode'
    config.read_lang_config()
    print(config.get_text(key))


def test_get_voice_names_dict():
    config.read_lang_config()
    voices_list = config.get_voice_names_dict()
    print(voices_list)
