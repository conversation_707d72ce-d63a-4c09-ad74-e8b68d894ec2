from v2subpy.sub.sub_file import SubFile
from v2subpy.sub import file_readers


def test_get_reader_txt():
    ext = 'txt'
    reader = SubFile.get_reader(ext)
    print(reader)
    assert reader is file_readers.TxtFile


def test_get_reader_srt():
    ext = 'srt'
    reader = SubFile.get_reader(ext)
    print(reader)
    assert reader is file_readers.SrtFile


def test_get_reader_vtt():
    ext = 'vtt'
    reader = SubFile.get_reader(ext)
    print(reader)
    assert reader is file_readers.VttFile

def test_get_reader_ass():
    ext = 'ass'
    reader = SubFile.get_reader(ext)
    print(reader)
    assert reader is file_readers.AssFile
    file_path = r"D:\test_files\test_ass\en1.ass"
    assfile = reader(file_path, "zh")
    for sub in assfile.subtitles:
        print(sub.content)
        
    print('************')
    print(assfile.compose(assfile.subtitles))