#!/usr/bin/env python3
"""
实时翻译演示脚本

直接运行此脚本来测试实时翻译功能，无需 pytest。
"""

import sys
import time
import threading
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

# 启用调试日志
os.environ['V2SUB_DEBUG'] = '1'

from v2subpy.realtime import realtime_trans


class RealtimeDemo:
    """实时翻译演示类"""
    
    def __init__(self):
        self.subtitles = []
        self.is_finished = False
        self.error_occurred = False
        self.lock = threading.Lock()
    
    def on_subtitle_ready(self, subtitle_dict):
        """字幕准备就绪回调"""
        with self.lock:
            self.subtitles.append(subtitle_dict)
            
            # 实时打印字幕
            print(f"\n{'='*60}")
            print(f"⏰ 时间: {subtitle_dict['start_time']:.2f}s - {subtitle_dict['end_time']:.2f}s")
            print(f"🎤 原文: {subtitle_dict['original_text']}")
            print(f"🌍 译文: {subtitle_dict['translated_text']}")
            print(f"📊 置信度: {subtitle_dict['confidence']:.2f}")
            print(f"🔤 语言: {subtitle_dict['language']}")
            print(f"📝 状态: {subtitle_dict['status']}")
            print(f"🆔 段落ID: {subtitle_dict['segment_id']}")
    
    def on_progress_update(self, status_message, progress_info):
        """进度更新回调"""
        current, total = progress_info
        percentage = (current / total * 100) if total > 0 else 0
        
        # 使用回车符实现进度条覆盖显示
        print(f"\r🔄 {status_message} - {percentage:.1f}% ({current:.1f}s/{total:.1f}s)", 
              end="", flush=True)
    
    def on_error(self, error_message):
        """错误回调"""
        with self.lock:
            self.error_occurred = True
            print(f"\n❌ 错误发生: {error_message}")
    
    def on_finished(self):
        """完成回调"""
        with self.lock:
            self.is_finished = True
            print(f"\n\n🎉 处理完成！")
            print(f"📈 统计信息:")
            print(f"  - 总字幕数量: {len(self.subtitles)}")
            print(f"  - 成功字幕: {len([s for s in self.subtitles if s['status'] == 'completed'])}")
            print(f"  - 错误字幕: {len([s for s in self.subtitles if s['status'] == 'error'])}")
            
            if self.subtitles:
                total_duration = max(s['end_time'] for s in self.subtitles)
                print(f"  - 处理时长: {total_duration:.1f}秒")
    
    def run_demo(self, video_path, config):
        """运行演示"""
        print("🎬 v2subpy 实时字幕翻译演示")
        print("=" * 60)
        print(f"📁 视频文件: {video_path}")
        print(f"🤖 Whisper模型: {config['whisper_model']}")
        print(f"🌐 翻译器: {config['translator_type']}")
        print(f"🎯 目标语言: {config['target_language']}")
        print("=" * 60)
        
        try:
            print("🚀 启动实时翻译处理...")
            
            # 启动实时翻译
            realtime_trans(
                video_path=video_path,
                config=config,
                on_subtitle_ready=self.on_subtitle_ready,
                on_progress_update=self.on_progress_update,
                on_error=self.on_error,
                on_finished=self.on_finished
            )
            
            # 等待处理完成
            print("⏳ 等待处理完成...")
            timeout = 600  # 10分钟超时
            start_time = time.time()
            
            while not self.is_finished and not self.error_occurred:
                if (time.time() - start_time) > timeout:
                    print(f"\n⏰ 处理超时 ({timeout}秒)")
                    break
                time.sleep(0.5)
            
            if self.error_occurred:
                print("\n❌ 处理过程中发生错误")
                return False
            elif self.is_finished:
                print("\n✅ 处理成功完成")
                return True
            else:
                print("\n⚠️ 处理超时")
                return False
                
        except Exception as e:
            print(f"\n💥 异常发生: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    # 配置参数
    VIDEO_PATH = r"d:\testvideo\en1.mp4"
    
    CONFIG = {
        # 音频处理配置
        'segment_duration': 8.0,      # 音频段长度（秒）
        'overlap_duration': 1.0,      # 重叠时长（秒）
        'audio_sample_rate': 16000,   # 音频采样率
        
        # 语音识别配置
        'whisper_model': 'medium',    # Whisper模型
        'whisper_device': 'auto',     # 设备选择
        'source_language': 'en',      # 源语言
        
        # 翻译配置
        'translator_type': 'default', # 翻译器类型
        'target_language': 'zh',      # 目标语言
        'translation_timeout': 15.0,  # 翻译超时
        
        # 性能配置
        'max_concurrent_segments': 2, # 最大并发段数
        'buffer_size': 8,             # 缓冲区大小
        
        # 错误处理配置
        'max_retries': 3,             # 最大重试次数
        'retry_delay': 1.0,           # 重试延迟
    }
    
    # 检查视频文件
    if not Path(VIDEO_PATH).exists():
        print(f"❌ 视频文件不存在: {VIDEO_PATH}")
        print("请确保视频文件存在后再运行演示")
        print("\n💡 提示: 您可以修改脚本中的 VIDEO_PATH 变量指向您的测试视频文件")
        return False
    
    # 运行演示
    demo = RealtimeDemo()
    success = demo.run_demo(VIDEO_PATH, CONFIG)
    
    if success:
        print("\n🎊 演示成功完成！")
        print("现在您可以开始前端集成工作了。")
    else:
        print("\n😞 演示未能成功完成，请检查错误信息。")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断了演示")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 未处理的异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)