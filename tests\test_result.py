from pathlib import Path
from v2subpy import v2sub
from v2subpy.model.sent_split import SplitArgs, update_split_result
from v2subpy.trans import langutil
from v2subpy.utils import config
from v2subpy.model.whisper import transcribe, handle_result
from v2subpy.model.result import WhisperResult


def test_whisper_result():
    model_name = 'medium'
    v2sub.init_sys()
    config.set_app_version(config.Version.ULTRA)
    model = v2sub.init_model(model_name)
    # video_path = r"D:\test_files\v1.5.0\leijun.mp4"
    video_path = r"D:\test_files\v1.5.0\iphone.mp4"
    source_lang = None
    options = {}
    options['language'] = source_lang
    result = transcribe(model, video_path, options, debug_json=True)
    print(f'segments: {len(result.segments)}')

def test_update_split_result():
    json_file = r"D:\test_files\test_json\iphone.json"
    # json_file = r"D:\test_files\v1.5.0\jp3_output.json"
    lang_code = 'en'
    # lang_code = 'ja'
    result = WhisperResult(json_file)
    print(f'before segments: {len(result.segments)}')
    if langutil.is_cjk_lang(lang_code):
        args = SplitArgs(width=14, lines=1)
    else:
        args = SplitArgs(width=42, lines=1)
    result = update_split_result(result, lang_code, args)
    print(f'after segments: {len(result.segments)}')
    srt_file = str(Path(json_file).with_suffix('.srt'))
    result.to_srt_vtt(srt_file, word_level=False)

def test_result_regroup():
    '''
    测试不同的regroup方案
    '''
    json_file = r"D:\test_files\test_regroup\en1.json"
    result = WhisperResult(json_file)
    group_str = "mg=.15+3"
    result.regroup(group_str)
    srt_file = str(Path(json_file).with_suffix('.srt'))
    result.to_srt_vtt(srt_file, word_level=False)

def test_handle_result():
    '''
    测试已有resut json
    '''
    json_file = r"D:\test_files\test_json\Day 5-1 - Live Trading with Mark Minervini & Mark Ritchie Part 1.json"
    result = WhisperResult(json_file)
    lang_code = 'en'
    handle_result(result, lang_code)
    output_file = str(Path(json_file).with_suffix('.out.srt'))
    result.to_srt_vtt(output_file, word_level=False)
 
def test_load_json_to_result():
    '''
    加载json文件到result
    '''
    # json_file = r"D:\test_files\v1.5.0\en1_old.json"
    json_file = r"D:\test_files\v1.5.0\jp3_output.json"
    result = WhisperResult(json_file)
    print(result.text)

    print(f'segments: {len(result.segments)}')
    for seg in result.segments:
        print(f'{seg.start:6.2f}->{seg.end:6.2f}:  {seg.text}')