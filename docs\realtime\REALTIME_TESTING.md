# v2subpy 实时字幕翻译测试指南

本文档介绍如何测试 v2subpy 的实时字幕翻译功能。

## 测试文件说明

### 1. `quick_test.py` - 快速验证测试
最简单的测试脚本，用于验证基本功能是否正常。

```bash
python quick_test.py
```

**功能：**
- 测试模块导入
- 测试配置创建
- 测试基本的实时翻译功能（如果测试视频存在）

### 2. `test_realtime_demo.py` - 完整演示
完整的实时翻译演示脚本，会实时打印字幕到命令行。

```bash
python test_realtime_demo.py
```

**功能：**
- 完整的实时翻译流程演示
- 实时显示字幕内容（原文和译文）
- 显示处理进度和统计信息
- 错误处理演示

### 3. `test_model_loading.py` - 模型加载测试
专门测试模型加载功能的脚本。

```bash
python test_model_loading.py
```

**功能：**
- 测试 SpeechRecognizer 的模型加载
- 测试 OptimizedSpeechRecognizer 的预加载功能
- 验证模型加载修复是否正确

### 4. `tests/test_realtime_translation.py` - pytest 测试套件
使用 pytest 框架的完整测试套件。

```bash
# 运行所有测试
pytest tests/test_realtime_translation.py -v

# 运行特定测试
pytest tests/test_realtime_translation.py::test_realtime_translation_basic -v

# 直接运行（不使用 pytest）
python tests/test_realtime_translation.py
```

**功能：**
- 基础实时翻译测试
- 不同配置的测试
- 错误处理测试

## 测试前准备

### 1. 准备测试视频文件
确保测试视频文件存在：
```
d:\testvideo\en1.mp4
```

如果您的测试视频在其他位置，请修改脚本中的 `VIDEO_PATH` 变量。

### 2. 安装依赖
确保已安装所有必要的依赖：
```bash
pip install pytest
pip install faster-whisper
# 其他 v2subpy 依赖...
```

### 3. 配置翻译器
确保 `default` 翻译器已正确配置。

### 4. 准备 Whisper 模型
确保 Whisper 模型已正确安装在本地。系统会自动从模型名称获取实际路径并加载模型。

### 5. 测试模型加载（可选）
运行模型加载测试来验证修复：
```bash
python test_model_loading.py
```

## 测试配置说明

默认测试配置：
```python
CONFIG = {
    # 音频处理配置
    'segment_duration': 8.0,      # 音频段长度（秒）
    'overlap_duration': 1.0,      # 重叠时长（秒）
    'audio_sample_rate': 16000,   # 音频采样率
    
    # 语音识别配置
    'whisper_model': 'medium',    # Whisper模型
    'whisper_device': 'auto',     # 设备选择
    'source_language': 'en',      # 源语言
    
    # 翻译配置
    'translator_type': 'default', # 翻译器类型
    'target_language': 'zh',      # 目标语言
    'translation_timeout': 15.0,  # 翻译超时
    
    # 性能配置
    'max_concurrent_segments': 2, # 最大并发段数
    'buffer_size': 8,             # 缓冲区大小
    
    # 错误处理配置
    'max_retries': 3,             # 最大重试次数
    'retry_delay': 1.0,           # 重试延迟
}
```

## 运行建议

### 推荐的测试顺序：

1. **首先运行快速测试**
   ```bash
   python quick_test.py
   ```
   验证基本功能是否正常。

2. **测试模型加载（如果遇到模型问题）**
   ```bash
   python test_model_loading.py
   ```
   验证模型加载修复是否正确。

3. **然后运行完整演示**
   ```bash
   python test_realtime_demo.py
   ```
   观察完整的实时翻译过程。

4. **最后运行 pytest 测试**
   ```bash
   pytest tests/test_realtime_translation.py -v
   ```
   进行全面的功能测试。

### 性能调优建议：

- **如果处理速度慢**：
  - 使用较小的 Whisper 模型（如 `base` 或 `small`）
  - 增加 `segment_duration` 减少处理频率
  - 减少 `max_concurrent_segments`

- **如果内存不足**：
  - 减少 `buffer_size`
  - 使用较小的 Whisper 模型
  - 减少 `max_concurrent_segments`

- **如果翻译超时**：
  - 增加 `translation_timeout`
  - 检查网络连接
  - 尝试其他翻译器

## 预期输出示例

运行 `test_realtime_demo.py` 时，您应该看到类似以下的输出：

```
🎬 v2subpy 实时字幕翻译演示
============================================================
📁 视频文件: d:\testvideo\en1.mp4
🤖 Whisper模型: medium
🌐 翻译器: default
🎯 目标语言: zh
============================================================
🚀 启动实时翻译处理...
⏳ 等待处理完成...
🔄 正在处理音频段 1/25 - 8.5% (8.0s/94.2s)

============================================================
⏰ 时间: 0.00s - 8.00s
🎤 原文: Hello, welcome to our presentation today.
🌍 译文: 你好，欢迎参加我们今天的演示。
📊 置信度: 0.92
🔤 语言: en
📝 状态: completed
🆔 段落ID: seg_0001_a1b2c3d4

============================================================
⏰ 时间: 7.00s - 15.00s
🎤 原文: We will be discussing the latest developments.
🌍 译文: 我们将讨论最新的发展情况。
📊 置信度: 0.88
🔤 语言: en
📝 状态: completed
🆔 段落ID: seg_0002_e5f6g7h8

...

🎉 处理完成！
📈 统计信息:
  - 总字幕数量: 12
  - 成功字幕: 11
  - 错误字幕: 1
  - 处理时长: 94.2秒

✅ 处理成功完成
🎊 演示成功完成！
现在您可以开始前端集成工作了。
```

## 故障排除

### 常见问题：

1. **模块导入错误**
   - 确保在项目根目录运行脚本
   - 检查 Python 路径设置

2. **视频文件不存在**
   - 检查文件路径是否正确
   - 确保文件格式受支持

3. **Whisper 模型加载失败**
   - 检查模型是否已下载
   - 尝试使用较小的模型

4. **翻译器错误**
   - 检查翻译器配置
   - 验证网络连接

5. **内存不足**
   - 减少并发处理数量
   - 使用较小的模型
   - 增加系统内存

### 调试建议：

- 查看详细的错误日志
- 逐步减少配置参数进行测试
- 使用较短的测试视频
- 检查系统资源使用情况

## 集成到前端

测试成功后，您可以在 v2subqt 中这样使用：

```python
from v2subpy.realtime import realtime_trans

# 在 Worker Thread 中调用
def start_realtime_translation(self):
    config = {
        'whisper_model': 'medium',
        'translator_type': 'default',
        'target_language': 'zh',
        # ... 其他配置
    }
    
    realtime_trans(
        video_path=self.video_path,
        config=config,
        on_subtitle_ready=self.on_subtitle_ready,
        on_progress_update=self.on_progress_update,
        on_error=self.on_error,
        on_finished=self.on_finished
    )
```

祝您测试顺利！🎉