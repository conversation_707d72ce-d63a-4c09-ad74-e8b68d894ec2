"""
config related 
"""
from typing import Any, List, Dict
from pathlib import Path
from enum import Enum
from collections import defaultdict
import toml
from v2subpy.utils.loadfile import get_file
from v2subpy.utils.version_manage import Version, VersionManager, Fn
from v2subpy.utils import common
DEFAULT_TRANSLATOR_CONFIG = {
    'retry_times': 2,
    'wait_for_retry': 5,
    'wait_for_request': 2,
    'max_char': 1000,
    'with_srt_format': False,
    'timeout': (10, 20),
}
APP_NAME = 'V2sub'
MODEL_PATH = 'data/model'
LANG_D = None
CFG_D = None
LANG_SUPPORTED_NAMES = ['en', 'zh', 'ja', 'yue', 'fr', 'ko', 'de', 'ru', 'es',
                        'pt', 'it', 'tr', 'cs', 'hi', 'uk', 'th', 'vi',
                        'ms', 'pl', 'nl', 'ar', 'sv', 'id', 'fi', 'el',
                        ]

CONFIG_FILE = './data/config.toml'
FREE_COUNT = 3

APP_VERSION = Version.FREE


def is_lang_code(code: str) -> bool:
    '''
    检查是否为语言代码
    '''
    return code in LANG_SUPPORTED_NAMES


def set_app_version(version: Version) -> None:
    from v2subpy.video import ff
    global APP_VERSION
    APP_VERSION = version
    ff.set_video_formats()


def app_is_free() -> bool:
    return APP_VERSION == Version.FREE


def get_sys_config(reload=False):
    '''
    only read first time
    '''
    global CFG_D
    if not CFG_D or reload:
        # print('read from file')
        CFG_D = read_config()
    return CFG_D


def get_key(longkey, default=None) -> Any:
    '''
    get config specified key
    '''
    try:
        if not '.' in longkey:
            return get_sys_config()[longkey]
        prefix, key = longkey.split('.')
        return get_sys_config()[prefix][key]
    except KeyError:
        return default


def get_text(longkey) -> str:
    '''
    get language specified text
    '''
    prefix, key = longkey.split('.')
    return LANG_D[prefix][key]


def fn_enabled(fn: Fn) -> bool:
    '''
    检查某个功能有权限， 并配置已打开
    '''
    if VersionManager.check_fn(fn):
        if get_key(fn):
            return True

    return False


def read_config() -> dict:
    config_path = get_file(CONFIG_FILE)
    # Read config from file
    with open(config_path, 'r', encoding='utf8') as f:
        cfg = toml.load(f)
    return cfg


def write_config(cfg) -> None:
    config_path = get_file(CONFIG_FILE)
    with open(config_path, 'w', encoding='utf8') as f:
        toml.dump(cfg, f)


def get_lang_file():
    '''
    取得对应语言设置的文件
    lang_chinese.toml
    '''
    lang = get_sys_config()['language']
    lang_file = f'./data/lang_{lang}.toml'
    lang_path = get_file(lang_file)
    print(f"{lang_path}")
    return lang_path


def read_lang_config() -> dict:
    global LANG_D
    lang_path = get_lang_file()
    cfg = toml.load(lang_path)
    LANG_D = cfg


def get_model_list() -> List[str]:
    """
    get current installed models
    """
    model_folder = Path(get_file(MODEL_PATH))
    model_list = [p.stem for p in model_folder.iterdir() if p.is_dir()]
    return model_list


def map_names_to_lang(names, prefix) -> dict:
    '''
    map names to lang matched name, value pair
    '''
    names_d = {}
    for name in names:
        value = LANG_D[prefix][name]
        names_d[name] = value
    return names_d


def get_model_names_dict() -> Dict[str, str]:
    supported_names = VersionManager.get_values(Fn.MODEL_LIST)
    prefix = 'model'
    names = get_model_list()
    names = [name for name in supported_names if name in names]
    names_dict = map_names_to_lang(names, prefix)
    return names_dict

def get_model_status_dict() -> Dict[str, str]:
    # 返回模型状态列表(name, installed_status)
    supported_names = VersionManager.get_values(Fn.MODEL_LIST)
    prefix = 'model'
    names_dict = map_names_to_lang(supported_names, prefix)
    installed_names = get_model_list()
    status_dict = {}
    for name in names_dict:
        if name in installed_names:
            status_dict[(name, names_dict[name])] = True
        else:
            status_dict[(name, names_dict[name])] = False
    return status_dict

def get_lang_names_dict(names: list = None) -> Dict[str, str]:
    '''
    get supported lang list for frontend
    '''
    if not names:
        names = LANG_SUPPORTED_NAMES
    prefix = 'lang'
    names_dict = map_names_to_lang(names, prefix)
    return names_dict


def get_lang_name(lang: str) -> str:
    '''
    取得语言代码对应的长名称
    '''
    try:
        language_name = get_text(f"lang.{lang}")
    except KeyError:
        language_name = get_text(f"lang.xxx")
    return language_name


def get_translator_names_dict() -> dict:
    '''
    返回翻译器字典
    '''
    names = VersionManager.get_values(Fn.TRANSLATOR)
    prefix = 'translator'
    names_dict = map_names_to_lang(names, prefix)
    return names_dict


def get_voice_names_dict() -> tuple:
    '''
    返回TTS声音名称字典
    '''
    prefix = 'voicename'
    names_dict = LANG_D[prefix]
    lang_dict = {}
    voice_dict = defaultdict(dict)
    for voice, name in names_dict.items():
        lang_1, lang_2, voice_value = voice.split('-', 2)
        lang = f'{lang_1}-{lang_2}'
        lang_name, voice_name = name.split('-', 1)
        lang_dict.setdefault(lang, lang_name)
        voice_dict[lang][voice] = voice_name

    return lang_dict, voice_dict


def get_gpt_domain_type_dict() -> dict:
    '''
    返回GPT domain type 字典
    '''

    names = VersionManager.get_values(Fn.GPT_DOMAIN_TYPE)
    prefix = 'gpt_domain_type'
    names_dict = map_names_to_lang(names, prefix)
    return names_dict


def get_gpt_temperature_dict() -> dict:
    '''
    返回GPT tempature 字典
    {
    '高': 0.9,
    '中': 0.7,
    '低': 0.2,
    '零': 0.0
    }
    '''
    prefix = 'gpt_temperature'
    names = [item.name for item in common.GptTemperatureEnum]
    values = {item.name: item.value for item in common.GptTemperatureEnum}
    names_dict = map_names_to_lang(names, prefix)
    items = {}
    for key in names_dict:
        items[names_dict[key]] = values[key]
    return items
