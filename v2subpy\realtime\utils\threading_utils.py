"""
V2subpy线程工具模块

提供线程安全的数据结构和线程池管理工具。
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, Optional, Callable, TypeVar, Generic
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ThreadSafeDict(Generic[T]):
    """线程安全的字典"""
    
    def __init__(self):
        self._dict: Dict[str, T] = {}
        self._lock = threading.RLock()
    
    def get(self, key: str, default: Optional[T] = None) -> Optional[T]:
        """获取值"""
        with self._lock:
            return self._dict.get(key, default)
    
    def set(self, key: str, value: T):
        """设置值"""
        with self._lock:
            self._dict[key] = value
    
    def pop(self, key: str, default: Optional[T] = None) -> Optional[T]:
        """弹出值"""
        with self._lock:
            return self._dict.pop(key, default)
    
    def keys(self):
        """获取所有键"""
        with self._lock:
            return list(self._dict.keys())
    
    def values(self):
        """获取所有值"""
        with self._lock:
            return list(self._dict.values())
    
    def items(self):
        """获取所有键值对"""
        with self._lock:
            return list(self._dict.items())
    
    def clear(self):
        """清空字典"""
        with self._lock:
            self._dict.clear()
    
    def __len__(self):
        """获取长度"""
        with self._lock:
            return len(self._dict)
    
    def __contains__(self, key: str):
        """检查是否包含键"""
        with self._lock:
            return key in self._dict


class ThreadPoolManager:
    """线程池管理器"""
    
    def __init__(self, max_workers: int = 4, thread_name_prefix: str = "V2subpy"):
        self.max_workers = max_workers
        self.thread_name_prefix = thread_name_prefix
        self._executor: Optional[ThreadPoolExecutor] = None
        self._lock = threading.Lock()
        self._shutdown = False
    
    @property
    def executor(self) -> ThreadPoolExecutor:
        """获取线程池执行器"""
        if self._executor is None or self._shutdown:
            with self._lock:
                if self._executor is None or self._shutdown:
                    if self._executor:
                        self._executor.shutdown(wait=False)
                    self._executor = ThreadPoolExecutor(
                        max_workers=self.max_workers,
                        thread_name_prefix=self.thread_name_prefix
                    )
                    self._shutdown = False
        return self._executor
    
    def submit(self, fn: Callable, *args, **kwargs) -> Future:
        """提交任务到线程池"""
        return self.executor.submit(fn, *args, **kwargs)
    
    def shutdown(self, wait: bool = True):
        """关闭线程池"""
        with self._lock:
            if self._executor and not self._shutdown:
                self._executor.shutdown(wait=wait)
                self._shutdown = True
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


class SafeThread(threading.Thread):
    """安全的线程类，带有异常处理"""
    
    def __init__(self, target: Callable, args: tuple = (), kwargs: Dict[str, Any] = None,
                 error_callback: Optional[Callable[[Exception], None]] = None,
                 name: Optional[str] = None):
        super().__init__(target=self._safe_run, args=args, kwargs=kwargs or {}, name=name, daemon=True)
        self._target = target
        self._error_callback = error_callback
        self._exception: Optional[Exception] = None
    
    def _safe_run(self, *args, **kwargs):
        """安全运行目标函数"""
        try:
            self._target(*args, **kwargs)
        except Exception as e:
            self._exception = e
            logger.error(f"Thread {self.name} encountered error: {e}")
            if self._error_callback:
                try:
                    self._error_callback(e)
                except Exception as callback_error:
                    logger.error(f"Error in thread error callback: {callback_error}")
    
    def get_exception(self) -> Optional[Exception]:
        """获取线程中发生的异常"""
        return self._exception


class ThreadSafeCounter:
    """线程安全的计数器"""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """增加计数"""
        with self._lock:
            self._value += amount
            return self._value
    
    def decrement(self, amount: int = 1) -> int:
        """减少计数"""
        with self._lock:
            self._value -= amount
            return self._value
    
    def get(self) -> int:
        """获取当前值"""
        with self._lock:
            return self._value
    
    def set(self, value: int) -> int:
        """设置值"""
        with self._lock:
            self._value = value
            return self._value
    
    def reset(self) -> int:
        """重置为0"""
        return self.set(0)


class ThreadSafeEvent:
    """线程安全的事件，支持超时等待"""
    
    def __init__(self):
        self._event = threading.Event()
        self._lock = threading.Lock()
    
    def set(self):
        """设置事件"""
        with self._lock:
            self._event.set()
    
    def clear(self):
        """清除事件"""
        with self._lock:
            self._event.clear()
    
    def is_set(self) -> bool:
        """检查事件是否已设置"""
        return self._event.is_set()
    
    def wait(self, timeout: Optional[float] = None) -> bool:
        """等待事件"""
        return self._event.wait(timeout)


def run_with_timeout(func: Callable, timeout: float, *args, **kwargs) -> Any:
    """
    在指定超时时间内运行函数
    
    Args:
        func: 要运行的函数
        timeout: 超时时间（秒）
        *args: 函数参数
        **kwargs: 函数关键字参数
    
    Returns:
        函数返回值
    
    Raises:
        TimeoutError: 如果函数执行超时
    """
    from ..utils.error_handling import TimeoutError as V2subpyTimeoutError
    
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func(*args, **kwargs)
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target, daemon=True)
    thread.start()
    thread.join(timeout)
    
    if thread.is_alive():
        raise V2subpyTimeoutError(f"Function execution timed out after {timeout} seconds")
    
    if exception[0]:
        raise exception[0]
    
    return result[0]


# 全局线程池管理器
global_thread_pool = ThreadPoolManager()