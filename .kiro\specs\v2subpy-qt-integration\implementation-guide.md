# V2subpy Qt集成实施指南

## 实施优先级

### 高优先级（立即实施）

#### 1. 移除程序退出调用
**问题**：v2subpy在某些情况下调用`sys.exit()`导致整个应用程序退出

**解决方案**：
```python
# 当前代码（需要修改）
import sys
sys.exit(1)  # 这会导致整个程序退出

# 修改后的代码
import logging
logger = logging.getLogger(__name__)

class V2subpyError(Exception):
    """V2subpy专用异常"""
    pass

# 用异常替代退出调用
raise V2subpyError("处理失败，请检查输入参数")
```

**影响文件**：
- `v2subpy/realtime/core.py`
- `v2subpy/realtime/processing_pipeline.py`
- 所有包含`sys.exit()`或`os._exit()`的文件

#### 2. 重构阻塞等待循环
**问题**：`core.py`中的阻塞等待循环阻止了异步使用

**当前代码**：
```python
# 等待处理完成
try:
    while pipeline.is_running:
        time.sleep(0.5)  # 这里会阻塞调用线程
except KeyboardInterrupt:
    logger.info("用户中断处理")
    pipeline.stop_processing()
    raise
```

**修改方案**：
```python
def realtime_trans_async(
    video_path: str,
    config: Optional[Dict[str, Any]] = None,
    on_subtitle_ready: Optional[Callable[[dict], None]] = None,
    on_progress_update: Optional[Callable[[str, tuple], None]] = None,
    on_error: Optional[Callable[[str], None]] = None,
    on_finished: Optional[Callable[[], None]] = None
) -> str:
    """
    异步版本的实时翻译函数
    
    Returns:
        str: 任务ID，用于后续控制
    """
    # 创建任务ID
    task_id = f"task_{uuid.uuid4().hex[:8]}"
    
    # 创建处理管道
    pipeline = pipeline_manager.create_pipeline(task_id, realtime_config, callbacks)
    
    # 异步启动处理
    def run_async():
        try:
            pipeline.start_processing(video_path)
            # 不再阻塞等待，而是通过回调通知完成
        except Exception as e:
            if on_error:
                on_error(str(e))
    
    # 在后台线程中运行
    thread = threading.Thread(target=run_async, daemon=True)
    thread.start()
    
    return task_id

# 保持向后兼容的同步版本
def realtime_trans(
    video_path: str,
    config: Optional[Dict[str, Any]] = None,
    on_subtitle_ready: Optional[Callable[[dict], None]] = None,
    on_progress_update: Optional[Callable[[str, tuple], None]] = None,
    on_error: Optional[Callable[[str], None]] = None,
    on_finished: Optional[Callable[[], None]] = None
) -> None:
    """
    兼容版本的实时翻译函数（同步）
    """
    finished_event = threading.Event()
    error_result = None
    
    def wrapped_finished():
        finished_event.set()
        if on_finished:
            on_finished()
    
    def wrapped_error(error_msg):
        nonlocal error_result
        error_result = error_msg
        finished_event.set()
        if on_error:
            on_error(error_msg)
    
    # 调用异步版本
    task_id = realtime_trans_async(
        video_path=video_path,
        config=config,
        on_subtitle_ready=on_subtitle_ready,
        on_progress_update=on_progress_update,
        on_error=wrapped_error,
        on_finished=wrapped_finished
    )
    
    # 等待完成
    finished_event.wait()
    
    if error_result:
        raise V2subpyError(error_result)
```

### 中优先级（第二阶段实施）

#### 3. 任务管理系统
**目标**：提供完整的任务生命周期管理

**实现**：
```python
class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.lock = threading.RLock()
    
    def create_task(self, video_path: str, config: Dict[str, Any]) -> str:
        """创建新任务"""
        task_id = f"task_{uuid.uuid4().hex[:8]}"
        task = Task(task_id, video_path, config)
        
        with self.lock:
            self.tasks[task_id] = task
        
        return task_id
    
    def start_task(self, task_id: str, callbacks: Dict[str, Callable]) -> bool:
        """启动任务"""
        with self.lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            return task.start(callbacks)
    
    def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        with self.lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            return task.stop()
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            task = self.tasks.get(task_id)
            return task.get_status() if task else None

# 全局任务管理器实例
task_manager = TaskManager()
```

#### 4. Qt专用集成模块
**目标**：提供Qt专用的集成接口

**实现**：
```python
# v2subpy/integrations/qt_integration.py
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from typing import Dict, Any, Optional, Callable

class QtRealtimeTranslator(QObject):
    """Qt专用的实时翻译器"""
    
    # Qt信号定义
    subtitle_ready = Signal(str, dict)  # task_id, subtitle_data
    progress_updated = Signal(str, str, tuple)  # task_id, message, progress_info
    error_occurred = Signal(str, str)  # task_id, error_message
    task_finished = Signal(str)  # task_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.active_tasks: Dict[str, QTimer] = {}
    
    def start_translation(self, video_path: str, config: Dict[str, Any]) -> str:
        """启动翻译任务"""
        from v2subpy.realtime import task_manager
        
        # 创建任务
        task_id = task_manager.create_task(video_path, config)
        
        # 设置回调
        callbacks = {
            'on_subtitle_ready': lambda data: self.subtitle_ready.emit(task_id, data),
            'on_progress_update': lambda msg, info: self.progress_updated.emit(task_id, msg, info),
            'on_error': lambda error: self.error_occurred.emit(task_id, error),
            'on_finished': lambda: self._on_task_finished(task_id)
        }
        
        # 启动任务
        success = task_manager.start_task(task_id, callbacks)
        
        if success:
            # 创建状态监控定时器
            timer = QTimer()
            timer.timeout.connect(lambda: self._monitor_task(task_id))
            timer.start(100)  # 每100ms检查一次
            self.active_tasks[task_id] = timer
        
        return task_id if success else ""
    
    def stop_translation(self, task_id: str) -> bool:
        """停止翻译任务"""
        from v2subpy.realtime import task_manager
        
        success = task_manager.stop_task(task_id)
        
        if task_id in self.active_tasks:
            self.active_tasks[task_id].stop()
            del self.active_tasks[task_id]
        
        return success
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        from v2subpy.realtime import task_manager
        return task_manager.get_task_status(task_id)
    
    def _monitor_task(self, task_id: str):
        """监控任务状态"""
        status = self.get_task_status(task_id)
        if not status or status.get('finished', False):
            self._on_task_finished(task_id)
    
    def _on_task_finished(self, task_id: str):
        """任务完成处理"""
        if task_id in self.active_tasks:
            self.active_tasks[task_id].stop()
            del self.active_tasks[task_id]
        
        self.task_finished.emit(task_id)
```

### 低优先级（第三阶段实施）

#### 5. 性能优化
- 模型复用机制
- 内存池管理
- 批处理优化

#### 6. 扩展功能
- 任务暂停/恢复
- 优先级调度
- 资源限制

## 具体修改文件清单

### 立即修改的文件

#### 1. `v2subpy/realtime/core.py`
```python
# 需要修改的关键部分

# 原有代码
def realtime_trans(...):
    # ... 初始化代码 ...
    
    # 等待处理完成 - 这部分需要重构
    try:
        while pipeline.is_running:
            time.sleep(0.5)  # 阻塞等待
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        pipeline.stop_processing()
        raise

# 修改后的代码
def realtime_trans(...):
    """保持向后兼容的同步接口"""
    return _realtime_trans_sync(...)

def realtime_trans_async(...):
    """新的异步接口"""
    return _realtime_trans_async(...)

def _realtime_trans_sync(...):
    """同步实现（内部使用异步）"""
    finished_event = threading.Event()
    result = {'error': None}
    
    def on_finished_wrapper():
        finished_event.set()
        if on_finished:
            on_finished()
    
    def on_error_wrapper(error_msg):
        result['error'] = error_msg
        finished_event.set()
        if on_error:
            on_error(error_msg)
    
    # 调用异步版本
    task_id = _realtime_trans_async(
        video_path=video_path,
        config=config,
        on_subtitle_ready=on_subtitle_ready,
        on_progress_update=on_progress_update,
        on_error=on_error_wrapper,
        on_finished=on_finished_wrapper
    )
    
    # 等待完成
    finished_event.wait()
    
    if result['error']:
        raise V2subpyError(result['error'])

def _realtime_trans_async(...):
    """异步实现"""
    # 验证输入
    _validate_video_file(video_path)
    
    # 创建配置
    realtime_config = _create_config(config or {})
    
    # 创建回调字典
    callbacks = {
        'on_subtitle_ready': on_subtitle_ready,
        'on_progress_update': on_progress_update,
        'on_error': on_error,
        'on_finished': on_finished
    }
    
    # 生成任务ID
    task_id = f"realtime_{uuid.uuid4().hex[:8]}"
    
    # 创建并启动处理管道（异步）
    pipeline = pipeline_manager.create_pipeline(task_id, realtime_config, callbacks)
    
    # 在后台线程中启动处理
    def run_pipeline():
        try:
            pipeline.start_processing(video_path)
        except Exception as e:
            logger.error(f"处理管道异常: {e}")
            if on_error:
                on_error(str(e))
    
    thread = threading.Thread(target=run_pipeline, daemon=True)
    thread.start()
    
    return task_id
```

#### 2. `v2subpy/realtime/processing_pipeline.py`
```python
# 需要修改的关键部分

class RealtimeProcessingPipeline:
    def start_processing(self, video_path: str) -> None:
        """启动处理流程（异步）"""
        if self.is_running:
            logger.warning("处理管道已在运行中")
            return

        try:
            logger.info(f"启动实时处理管道: {video_path}")
            
            # 重置状态
            self.is_running = True
            self.is_paused = False
            self.stop_flag = False
            
            # 预加载模型
            self._preload_models()
            
            # 启动音频生成
            self.audio_generator.start_generation(video_path)
            
            # 启动处理线程（异步）
            self.processing_thread = threading.Thread(
                target=self._processing_loop,
                args=(video_path,),
                daemon=True
            )
            self.processing_thread.start()
            
            # 启动结果分发线程（异步）
            self.result_thread = threading.Thread(
                target=self._result_dispatch_loop,
                daemon=True
            )
            self.result_thread.start()
            
            logger.info("实时处理管道启动成功")
            
            # 不再阻塞等待，直接返回
            
        except Exception as e:
            logger.error(f"启动处理管道失败: {e}")
            self.is_running = False
            raise RealtimeProcessingError(f"启动处理管道失败: {e}")
    
    def _processing_loop(self, video_path: str) -> None:
        """主处理循环（在后台线程中运行）"""
        try:
            logger.info("开始处理循环")
            
            # 存储提交的任务
            pending_futures = []
            
            while self.is_running and not self.stop_flag:
                try:
                    # 检查暂停状态
                    if self.is_paused:
                        time.sleep(0.1)
                        continue
                    
                    # 获取下一个音频段
                    audio_segment = self.audio_generator.get_next_segment(timeout=2.0)
                    
                    if audio_segment is None:
                        # 没有更多音频段，处理完成
                        logger.info("所有音频段处理完成")
                        break
                    
                    # 提交处理任务
                    future = self.executor.submit(
                        self._process_audio_segment, audio_segment
                    )
                    pending_futures.append(future)
                    
                    # 更新进度
                    self._update_progress(audio_segment)
                    
                except Empty:
                    # 超时，检查是否应该继续等待
                    if self.audio_generator.is_generating:
                        continue
                    else:
                        logger.info("音频生成完成，退出处理循环")
                        break
                        
                except Exception as e:
                    logger.error(f"处理循环异常: {e}")
                    self._call_callback('on_error', self._format_error(e))
                    break
            
            # 等待所有处理任务完成
            logger.info(f"等待 {len(pending_futures)} 个处理任务完成")
            for future in pending_futures:
                try:
                    future.result(timeout=30.0)
                except Exception as e:
                    logger.error(f"处理任务异常: {e}")
            
            logger.info("处理循环结束")
            
            # 调用完成回调
            self._call_callback('on_finished')
            
        except Exception as e:
            logger.error(f"处理循环致命错误: {e}")
            self._call_callback('on_error', self._format_error(e))
        finally:
            self.is_running = False
```

#### 3. 创建新文件 `v2subpy/realtime/exceptions.py`
```python
"""V2subpy专用异常定义"""

class V2subpyError(Exception):
    """V2subpy基础异常"""
    pass

class RealtimeProcessingError(V2subpyError):
    """实时处理异常"""
    pass

class AudioExtractionError(V2subpyError):
    """音频提取异常"""
    pass

class SpeechRecognitionError(V2subpyError):
    """语音识别异常"""
    pass

class TranslationError(V2subpyError):
    """翻译异常"""
    pass

class ConfigurationError(V2subpyError):
    """配置异常"""
    pass
```

#### 4. 创建新文件 `v2subpy/integrations/qt_integration.py`
```python
"""Qt集成模块"""

try:
    from PySide6.QtCore import QObject, Signal, QTimer
    QT_AVAILABLE = True
except ImportError:
    try:
        from PyQt6.QtCore import QObject, pyqtSignal as Signal, QTimer
        QT_AVAILABLE = True
    except ImportError:
        try:
            from PyQt5.QtCore import QObject, pyqtSignal as Signal, QTimer
            QT_AVAILABLE = True
        except ImportError:
            QT_AVAILABLE = False

if QT_AVAILABLE:
    from typing import Dict, Any, Optional
    import logging
    
    logger = logging.getLogger(__name__)
    
    class QtRealtimeTranslator(QObject):
        """Qt专用的实时翻译器"""
        
        # 信号定义
        subtitle_ready = Signal(str, dict)
        progress_updated = Signal(str, str, tuple)
        error_occurred = Signal(str, str)
        task_finished = Signal(str)
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.active_tasks: Dict[str, QTimer] = {}
        
        def start_translation(self, video_path: str, config: Dict[str, Any]) -> str:
            """启动翻译任务"""
            from ..core import realtime_trans_async
            
            # 创建回调函数
            def on_subtitle_ready(subtitle_data):
                self.subtitle_ready.emit(task_id, subtitle_data)
            
            def on_progress_update(message, progress_info):
                self.progress_updated.emit(task_id, message, progress_info)
            
            def on_error(error_message):
                self.error_occurred.emit(task_id, error_message)
                self._cleanup_task(task_id)
            
            def on_finished():
                self.task_finished.emit(task_id)
                self._cleanup_task(task_id)
            
            # 启动异步翻译
            task_id = realtime_trans_async(
                video_path=video_path,
                config=config,
                on_subtitle_ready=on_subtitle_ready,
                on_progress_update=on_progress_update,
                on_error=on_error,
                on_finished=on_finished
            )
            
            return task_id
        
        def stop_translation(self, task_id: str) -> bool:
            """停止翻译任务"""
            # TODO: 实现任务停止功能
            self._cleanup_task(task_id)
            return True
        
        def _cleanup_task(self, task_id: str):
            """清理任务资源"""
            if task_id in self.active_tasks:
                self.active_tasks[task_id].stop()
                del self.active_tasks[task_id]

else:
    # Qt不可用时的占位符
    class QtRealtimeTranslator:
        def __init__(self, *args, **kwargs):
            raise ImportError("Qt库不可用，无法使用QtRealtimeTranslator")
```

## 测试验证

### 1. 基础功能测试
```python
# test_async_interface.py
import pytest
import threading
import time
from v2subpy.realtime import realtime_trans_async

def test_async_interface():
    """测试异步接口"""
    video_path = "test_video.mp4"
    config = {"whisper_model": "small"}
    
    results = []
    finished_event = threading.Event()
    
    def on_subtitle_ready(subtitle_data):
        results.append(subtitle_data)
    
    def on_finished():
        finished_event.set()
    
    # 启动异步翻译
    task_id = realtime_trans_async(
        video_path=video_path,
        config=config,
        on_subtitle_ready=on_subtitle_ready,
        on_finished=on_finished
    )
    
    # 验证立即返回
    assert task_id is not None
    assert isinstance(task_id, str)
    
    # 等待完成
    assert finished_event.wait(timeout=60)
    
    # 验证结果
    assert len(results) > 0
```

### 2. Qt集成测试
```python
# test_qt_integration.py
import pytest
from PySide6.QtCore import QCoreApplication
from v2subpy.integrations.qt_integration import QtRealtimeTranslator

def test_qt_integration():
    """测试Qt集成"""
    app = QCoreApplication([])
    
    translator = QtRealtimeTranslator()
    
    # 连接信号
    results = []
    translator.subtitle_ready.connect(lambda task_id, data: results.append(data))
    
    # 启动翻译
    task_id = translator.start_translation("test_video.mp4", {"whisper_model": "small"})
    
    assert task_id is not None
    
    # 运行事件循环一段时间
    app.processEvents()
    
    app.quit()
```

## 部署建议

### 1. 分阶段部署
- **第一阶段**：修复退出调用和阻塞等待问题
- **第二阶段**：添加异步接口和任务管理
- **第三阶段**：完善Qt集成和性能优化

### 2. 向后兼容
- 保持原有`realtime_trans`函数接口不变
- 在内部使用新的异步实现
- 提供迁移文档和示例

### 3. 测试覆盖
- 单元测试覆盖所有新功能
- 集成测试验证Qt集成
- 性能测试确保无回归

这个实施指南提供了具体的代码修改方案和实施步骤，可以指导开发团队逐步改进v2subpy的Qt集成能力。