"""
回调函数管理器

提供安全、高效的回调函数管理和调用机制。
"""

import time
import threading
from typing import Callable, Dict, List, Any, Optional
from queue import Queue, Empty
from dataclasses import dataclass
import json

from v2subpy.utils.log import logger


@dataclass
class CallbackEvent:
    """回调事件"""
    callback_name: str
    args: tuple
    kwargs: dict
    timestamp: float
    priority: int = 0  # 优先级，数字越大优先级越高


class CallbackManager:
    """回调函数管理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        """
        初始化回调管理器
        
        Args:
            max_queue_size: 最大队列大小
        """
        self.callbacks: Dict[str, List[Callable]] = {}
        self.event_queue = Queue(maxsize=max_queue_size)
        self.is_running = False
        self.dispatch_thread: Optional[threading.Thread] = None
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_events': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'dropped_events': 0,
            'average_dispatch_time': 0.0
        }
        
        logger.debug(f"回调管理器初始化: max_queue_size={max_queue_size}")
    
    def register_callback(self, event_name: str, callback: Callable) -> None:
        """
        注册回调函数
        
        Args:
            event_name: 事件名称
            callback: 回调函数
        """
        with self.lock:
            if event_name not in self.callbacks:
                self.callbacks[event_name] = []
            
            if callback not in self.callbacks[event_name]:
                self.callbacks[event_name].append(callback)
                logger.debug(f"注册回调函数: {event_name}")
    
    def unregister_callback(self, event_name: str, callback: Callable) -> bool:
        """
        取消注册回调函数
        
        Args:
            event_name: 事件名称
            callback: 回调函数
            
        Returns:
            bool: 是否成功取消注册
        """
        with self.lock:
            if event_name in self.callbacks:
                try:
                    self.callbacks[event_name].remove(callback)
                    logger.debug(f"取消注册回调函数: {event_name}")
                    return True
                except ValueError:
                    pass
            return False
    
    def emit_event(self, event_name: str, *args, priority: int = 0, **kwargs) -> bool:
        """
        发射事件
        
        Args:
            event_name: 事件名称
            *args: 事件参数
            priority: 事件优先级
            **kwargs: 事件关键字参数
            
        Returns:
            bool: 是否成功加入队列
        """
        event = CallbackEvent(
            callback_name=event_name,
            args=args,
            kwargs=kwargs,
            timestamp=time.time(),
            priority=priority
        )
        
        try:
            self.event_queue.put_nowait(event)
            self.stats['total_events'] += 1
            return True
        except:
            # 队列满，丢弃事件
            self.stats['dropped_events'] += 1
            logger.warning(f"事件队列已满，丢弃事件: {event_name}")
            return False
    
    def emit_event_sync(self, event_name: str, *args, **kwargs) -> List[Any]:
        """
        同步发射事件（立即执行）
        
        Args:
            event_name: 事件名称
            *args: 事件参数
            **kwargs: 事件关键字参数
            
        Returns:
            List[Any]: 所有回调函数的返回值列表
        """
        results = []
        
        with self.lock:
            callbacks = self.callbacks.get(event_name, [])
        
        for callback in callbacks:
            try:
                start_time = time.time()
                result = callback(*args, **kwargs)
                results.append(result)
                
                dispatch_time = time.time() - start_time
                self._update_dispatch_time(dispatch_time)
                self.stats['successful_calls'] += 1
                
            except Exception as e:
                logger.error(f"同步回调执行失败: {event_name}, {e}")
                self.stats['failed_calls'] += 1
                results.append(None)
        
        return results
    
    def start_dispatch(self) -> None:
        """启动事件分发"""
        if self.is_running:
            logger.warning("事件分发已在运行中")
            return
        
        self.is_running = True
        self.dispatch_thread = threading.Thread(
            target=self._dispatch_loop,
            daemon=True
        )
        self.dispatch_thread.start()
        
        logger.info("事件分发已启动")
    
    def stop_dispatch(self) -> None:
        """停止事件分发"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待分发线程结束
        if self.dispatch_thread and self.dispatch_thread.is_alive():
            self.dispatch_thread.join(timeout=2.0)
        
        logger.info("事件分发已停止")
    
    def _dispatch_loop(self) -> None:
        """事件分发循环"""
        try:
            while self.is_running:
                try:
                    # 获取事件（带超时）
                    event = self.event_queue.get(timeout=0.5)
                    
                    # 执行回调
                    self._execute_callbacks(event)
                    
                except Empty:
                    continue
                except Exception as e:
                    logger.error(f"事件分发循环异常: {e}")
            
            # 处理剩余事件
            self._process_remaining_events()
            
        except Exception as e:
            logger.error(f"事件分发循环致命错误: {e}")
    
    def _execute_callbacks(self, event: CallbackEvent) -> None:
        """执行回调函数"""
        with self.lock:
            callbacks = self.callbacks.get(event.callback_name, [])
        
        if not callbacks:
            return
        
        for callback in callbacks:
            try:
                start_time = time.time()
                callback(*event.args, **event.kwargs)
                
                dispatch_time = time.time() - start_time
                self._update_dispatch_time(dispatch_time)
                self.stats['successful_calls'] += 1
                
            except Exception as e:
                logger.error(f"回调执行失败: {event.callback_name}, {e}")
                self.stats['failed_calls'] += 1
    
    def _process_remaining_events(self) -> None:
        """处理剩余事件"""
        processed_count = 0
        
        while not self.event_queue.empty():
            try:
                event = self.event_queue.get_nowait()
                self._execute_callbacks(event)
                processed_count += 1
            except Empty:
                break
            except Exception as e:
                logger.error(f"处理剩余事件失败: {e}")
        
        if processed_count > 0:
            logger.info(f"处理剩余事件: {processed_count} 个")
    
    def _update_dispatch_time(self, dispatch_time: float) -> None:
        """更新分发时间统计"""
        total_calls = self.stats['successful_calls'] + self.stats['failed_calls']
        if total_calls > 0:
            self.stats['average_dispatch_time'] = (
                (self.stats['average_dispatch_time'] * (total_calls - 1) + dispatch_time) / total_calls
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                **self.stats,
                'registered_callbacks': {
                    name: len(callbacks) for name, callbacks in self.callbacks.items()
                },
                'queue_size': self.event_queue.qsize(),
                'is_running': self.is_running
            }
    
    def clear_callbacks(self, event_name: Optional[str] = None) -> None:
        """
        清理回调函数
        
        Args:
            event_name: 事件名称，如果为 None 则清理所有回调
        """
        with self.lock:
            if event_name:
                if event_name in self.callbacks:
                    del self.callbacks[event_name]
                    logger.debug(f"清理回调函数: {event_name}")
            else:
                self.callbacks.clear()
                logger.debug("清理所有回调函数")


class SafeCallbackWrapper:
    """安全回调包装器"""
    
    def __init__(self, callback: Callable, timeout: float = 5.0):
        """
        初始化安全回调包装器
        
        Args:
            callback: 原始回调函数
            timeout: 超时时间（秒）
        """
        self.callback = callback
        self.timeout = timeout
        self.call_count = 0
        self.error_count = 0
        self.last_error: Optional[Exception] = None
        
    def __call__(self, *args, **kwargs) -> Any:
        """调用回调函数"""
        self.call_count += 1
        
        try:
            # 使用线程执行回调，支持超时
            import concurrent.futures
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self.callback, *args, **kwargs)
                return future.result(timeout=self.timeout)
                
        except concurrent.futures.TimeoutError:
            self.error_count += 1
            self.last_error = Exception(f"回调函数超时: {self.timeout}s")
            logger.error(f"回调函数超时: {self.callback}, {self.timeout}s")
            raise self.last_error
            
        except Exception as e:
            self.error_count += 1
            self.last_error = e
            logger.error(f"回调函数执行失败: {self.callback}, {e}")
            raise e
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'call_count': self.call_count,
            'error_count': self.error_count,
            'success_rate': (
                ((self.call_count - self.error_count) / self.call_count * 100)
                if self.call_count > 0 else 0
            ),
            'last_error': str(self.last_error) if self.last_error else None
        }


class RealtimeCallbackManager:
    """实时回调管理器（专门用于实时字幕处理）"""
    
    def __init__(self):
        """初始化实时回调管理器"""
        self.callback_manager = CallbackManager(max_queue_size=2000)
        self.safe_callbacks: Dict[str, SafeCallbackWrapper] = {}
        
    def setup_callbacks(self, callbacks: Dict[str, Callable]) -> None:
        """
        设置回调函数
        
        Args:
            callbacks: 回调函数字典
        """
        # 清理现有回调
        self.callback_manager.clear_callbacks()
        self.safe_callbacks.clear()
        
        # 注册新回调
        for name, callback in callbacks.items():
            if callback:
                # 包装为安全回调
                safe_callback = SafeCallbackWrapper(callback, timeout=10.0)
                self.safe_callbacks[name] = safe_callback
                
                # 注册到回调管理器
                self.callback_manager.register_callback(name, safe_callback)
        
        # 启动事件分发
        self.callback_manager.start_dispatch()
        
        logger.info(f"设置实时回调: {list(callbacks.keys())}")
    
    def emit_subtitle_ready(self, subtitle_dict: dict) -> None:
        """发射字幕准备就绪事件"""
        self.callback_manager.emit_event('on_subtitle_ready', subtitle_dict, priority=1)
    
    def emit_progress_update(self, status_message: str, progress_info: tuple) -> None:
        """发射进度更新事件"""
        self.callback_manager.emit_event('on_progress_update', status_message, progress_info)
    
    def emit_error(self, error_message: str) -> None:
        """发射错误事件"""
        self.callback_manager.emit_event('on_error', error_message, priority=2)
    
    def emit_finished(self) -> None:
        """发射完成事件"""
        self.callback_manager.emit_event('on_finished', priority=3)
    
    def stop(self) -> None:
        """停止回调管理器"""
        self.callback_manager.stop_dispatch()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.callback_manager.get_stats()
        
        # 添加安全回调统计
        callback_stats = {}
        for name, safe_callback in self.safe_callbacks.items():
            callback_stats[name] = safe_callback.get_stats()
        
        stats['callback_stats'] = callback_stats
        return stats