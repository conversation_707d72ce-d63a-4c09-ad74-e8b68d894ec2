# 模型工具模块， 提供下载模型等功能

import os
from pathlib import Path
import shutil
import time
from threading import Thread, Event
from tqdm.std import tqdm
from v2subpy.utils import config
from v2subpy.utils.loadfile import get_file, user_data_dir
from v2subpy.utils.log import logger
from v2subpy.utils.common import V2subError

def init_setup():
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    from huggingface_hub.utils import disable_progress_bars
    disable_progress_bars()

def get_model_path(model):
    # 获取模型本地目录
    model_folder = get_file(config.MODEL_PATH)
    model_path = str(Path(model_folder).joinpath(model).resolve())
    return model_path

def get_dir_size(dir_path):
    total_size = 0
    for dirpath, dirs, files in os.walk(dir_path):
        for filename in files:
            file_path = os.path.abspath(os.path.join(dirpath, filename))
            file_size = os.stat(file_path).st_size
            total_size += file_size
    # logger.debug(f'{total_size=}')
    return total_size

def get_repo_id(model: str) -> str:
    if model == 'large':
        model = model + '-v3'
    if '-en' in model:
        model = model.replace('-', '.')
    if 'turbo' in model:
        repo_id = "mobiuslabsgmbh/faster-whisper-large-v3-turbo"
    else:
        repo_id=f"Systran/faster-whisper-{model}"
    return repo_id

def get_model_size(model: str) -> int:
    # 返回模型目录大小
    model_size_dict = {
        'small' : 486_212_372,
        'medium': 1_530_571_735,
        'large': 3_090_835_702,
        'small-en': 486_098_798,
        'medium-en': 1_530_457_748,
        'large-turbo': 1_621_440_965
    }
    return model_size_dict[model]

def get_cache_dir(model: str) -> str:
    # 获得模型下载缓存目录
    from huggingface_hub.constants import HUGGINGFACE_HUB_CACHE
    repo_id = get_repo_id(model)
    repo_dir = "models--" + repo_id.replace('/', '--')
    cache_dir= os.path.join(HUGGINGFACE_HUB_CACHE, repo_dir)
    logger.debug(f'{cache_dir=}')
    return cache_dir

class DownloadModelThread(Thread):
    def __init__(self, model, progress_event, exception_event):
        super().__init__()
        self.model = model
        self.progress_event = progress_event
        self.exception_event = exception_event

    def run(self):
        from huggingface_hub import snapshot_download
        # 先放在用户v2sub/model_cache目录， 完成下载后复制到目标目录并检测大小
        model_dir = f'model_cache/{self.model}'
        output_dir = user_data_dir(model_dir)
        repo_id = get_repo_id(self.model)
        logger.debug(f'{output_dir=}')
        logger.debug(f'start down {repo_id}')
        try:
            snapshot_download(repo_id, local_dir=output_dir,
                            local_dir_use_symlinks=False, resume_download=True, etag_timeout=30)
        except Exception as e:
            logger.debug(str(e))
            self.exception_event.set()
        else:
            # check model_dir size
            target_size = get_model_size(self.model)
            dir_size = get_dir_size(output_dir)
            if dir_size/target_size < 0.99:
                self.exception_event.set()
            else:
                # 复制目录
                target_dir = get_model_path(self.model)
                shutil.move(output_dir, target_dir)
                self.progress_event.set()

def down_model(model, update_progress):
    # 创建进度事件
    progress_event = Event()
    exception_event = Event()
    # 启动下载线程
    download_thread = DownloadModelThread(model, progress_event, exception_event)
    download_thread.start()

    # 在主线程中监控下载进度
    cache_dir = get_cache_dir(model)
    target_size = get_model_size(model)
    while True:
        if exception_event.is_set():
            err = '下载错误'
            logger.debug(err)
            raise V2subError(err)
        if not progress_event.is_set():
            dir_size = get_dir_size(cache_dir)
            status_message = 'downloading'
            progress_info = (dir_size, target_size)
            update_progress(status_message, progress_info)
            time.sleep(1)
        else:
            logger.debug('下载完成!')
            status_message = 'finished'
            progress_info = (target_size, target_size)
            update_progress(status_message, progress_info)
            break
    
    download_thread.join()

init_setup()