### V2subpy 开发任务

## 转录相关
- [x] whisper model 加载路径自定义
- [x] 重构转录代码， 先通过ffmgeg提取音频
- [x] 提取音频可以控制提取时间， 比如只提取前10分钟
- [x] 将视频处理的后缀定义为常数， 方便前台提取， 并区分收费免费版

## 翻译相关
- [x] 重构翻译相关代码
- [x] 研究playwright 使用代理
- [x] copy deepl_cli 代码， 修改增加代理功能， 改为同步执行
- [x] 改进错误重试机制， 每个文件重试多次r，每个段落请求重试多次， 每次等待x秒
- [x] 改进错误重试机制， 每次使用不同translator， 每次等待x秒
- [x] 只留下deepl_cli翻译器， 暂时移除其他， 并将此改名为default

## 字幕相关
- [x] copy srtwriter， 开发单独字幕文件包sub

## 版本相关
- [x] 增加控制免费版和收费版的方式(使用free branch开发免费版本)
- [x] 免费版前3条字幕增加 v2sub标识

## 注册码相关(转移至前端控制)
- [x] 调研注册码验证， 生成相关

## 配置文件相关(config.py)
- [x] 增加get_sys_config方法， 仅第一次加载时读取文件， 用来读取不变的配置项

## 其他
- [x] 增加代理配置
- [x] 代理协议要为 "http" or "socks5"
- [x] 增加toml配置文件功能
- [x] 系统检测功能， 检测是否支持gpu计算， gpu名
- [x] utils增加获取支持语言列表函数， 供前端使用
- [x] 获取已安装模型列表
- [x] 重构调试信息， 使用logging， 错误信息写入文件， 过程信息写入sys.out
- [x] 增加强制使用cpu选项?
- [x] 增加多语言控制， 可外部配置语言文件
- [x] add code to github
- [x] 统一将文字信息转移至语言配置文件， 统一管理
- [x] 统一错误处理， 异常处理方式， 信息
- [x] 调整log， log-level， 以及关闭不必要log， 在正式版本之前


## 版本打包相关
- [x] 进行必要修改， 打包成为whl格式， 并且文件编译成为pyd格式（trans\deepl_cli.py除外）
- [x] 更新setup文件， 完成相关改动
- [x] 生成自动打包bat脚本， 可以打包pro和free版本
- [x] 合并free和pro版本

```
打包脚本

python setup.py build_ext --inplace
rmdir /s /q v2subpy.egg-info dist build
python setup.py bdist_wheel
```

##  后续版本
- [ ] 增加其他如google， microsoft翻译
- [ ] utils增加获取翻译器列表函数供前端使用
- [ ] 增加openai 翻译
- [ ] 分为reader， writer， 方便后续增加其他字幕或文件类型， 比如txt，ass等  