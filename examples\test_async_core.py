#!/usr/bin/env python3
"""
测试异步核心功能

不依赖Qt，直接测试异步翻译器的基本功能。
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_async_translator():
    """测试异步翻译器"""
    print("=== 测试异步翻译器 ===")
    
    try:
        from v2subpy.realtime.integrations import AsyncRealtimeTranslator
        print("✓ 成功导入AsyncRealtimeTranslator")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建翻译器
    translator = AsyncRealtimeTranslator(max_concurrent_tasks=1)
    print("✓ 创建翻译器成功")
    
    # 测试回调
    results = []
    finished_event = threading.Event()
    
    def on_subtitle_ready(task_id, subtitle_data):
        print(f"字幕: {subtitle_data}")
        results.append(subtitle_data)
    
    def on_progress_update(task_id, progress_data):
        print(f"进度: {progress_data}")
    
    def on_error(task_id, error_data):
        print(f"错误: {error_data}")
        finished_event.set()
    
    def on_finished(task_id, result_data):
        print(f"完成: {result_data}")
        finished_event.set()
    
    callbacks = {
        'on_subtitle_ready': on_subtitle_ready,
        'on_progress_update': on_progress_update,
        'on_error': on_error,
        'on_finished': on_finished
    }
    
    # 启动测试任务
    config = {
        'whisper_model': 'small',
        'target_language': 'zh',
        'translation_engine': 'openai'
    }
    
    try:
        task_id = translator.start_translation("test_video.mp4", config, callbacks)
        print(f"✓ 启动任务成功: {task_id}")
        
        # 等待完成
        print("等待任务完成...")
        finished_event.wait(timeout=30)
        
        if results:
            print(f"✓ 收到 {len(results)} 个字幕结果")
        else:
            print("! 没有收到字幕结果")
        
        return True
        
    except Exception as e:
        print(f"✗ 启动任务失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_manager():
    """测试任务管理器"""
    print("\n=== 测试任务管理器 ===")
    
    try:
        from v2subpy.realtime.async_core.task_manager import global_task_manager
        print("✓ 成功导入任务管理器")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建任务
    try:
        task_id = global_task_manager.create_task("test_video.mp4", {})
        print(f"✓ 创建任务成功: {task_id}")
        
        # 获取任务状态
        status = global_task_manager.get_task_status(task_id)
        print(f"✓ 任务状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_processing_pipeline():
    """测试处理管道"""
    print("\n=== 测试处理管道 ===")
    
    try:
        from v2subpy.realtime.processing_pipeline import RealtimeProcessingPipeline
        print("✓ 成功导入处理管道")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建处理管道
    try:
        config = {
            'whisper_model': 'small',
            'target_language': 'zh',
            'translation_engine': 'openai'
        }
        
        pipeline = RealtimeProcessingPipeline(config, task_id="test_task")
        print("✓ 创建处理管道成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理管道测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("V2subpy异步核心功能测试")
    print("=" * 40)
    
    tests = [
        test_task_manager,
        test_processing_pipeline,
        test_async_translator
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1


if __name__ == '__main__':
    sys.exit(main())