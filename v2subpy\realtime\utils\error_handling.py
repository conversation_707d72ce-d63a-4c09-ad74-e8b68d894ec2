"""
V2subpy错误处理模块

定义专用异常类和错误处理工具，替代sys.exit()调用。
"""

import traceback
from typing import Dict, Any, Optional


class V2subpyError(Exception):
    """V2subpy基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "traceback": traceback.format_exc()
        }


class RealtimeProcessingError(V2subpyError):
    """实时处理异常"""
    pass


class AudioExtractionError(V2subpyError):
    """音频提取异常"""
    pass


class SpeechRecognitionError(V2subpyError):
    """语音识别异常"""
    pass


class TranslationError(V2subpyError):
    """翻译异常"""
    pass


class ConfigurationError(V2subpyError):
    """配置异常"""
    pass


class TaskNotFoundError(V2subpyError):
    """任务未找到异常"""
    pass


class TaskAlreadyRunningError(V2subpyError):
    """任务已在运行异常"""
    pass


class ResourceError(V2subpyError):
    """资源异常"""
    pass


class TimeoutError(V2subpyError):
    """超时异常"""
    pass


def format_error(exception: Exception, task_id: Optional[str] = None) -> Dict[str, Any]:
    """
    格式化异常信息为标准字典格式
    
    Args:
        exception: 异常对象
        task_id: 相关的任务ID
    
    Returns:
        格式化的错误信息字典
    """
    if isinstance(exception, V2subpyError):
        error_dict = exception.to_dict()
    else:
        error_dict = {
            "error_type": exception.__class__.__name__,
            "error_code": exception.__class__.__name__,
            "message": str(exception),
            "details": {},
            "traceback": traceback.format_exc()
        }
    
    if task_id:
        error_dict["task_id"] = task_id
    
    return error_dict


def handle_critical_error(exception: Exception, task_id: Optional[str] = None) -> Dict[str, Any]:
    """
    处理关键错误，替代sys.exit()调用
    
    Args:
        exception: 异常对象
        task_id: 相关的任务ID
    
    Returns:
        格式化的错误信息字典
    """
    import logging
    logger = logging.getLogger(__name__)
    
    error_dict = format_error(exception, task_id)
    logger.critical(f"Critical error occurred: {error_dict}")
    
    return error_dict


class ErrorRecovery:
    """错误恢复机制"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.5):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.retry_counts: Dict[str, int] = {}
    
    def should_retry(self, task_id: str, exception: Exception) -> bool:
        """
        判断是否应该重试
        
        Args:
            task_id: 任务ID
            exception: 异常对象
        
        Returns:
            是否应该重试
        """
        # 某些异常不应该重试
        non_retryable_errors = (
            ConfigurationError,
            TaskNotFoundError,
            TaskAlreadyRunningError
        )
        
        if isinstance(exception, non_retryable_errors):
            return False
        
        current_retries = self.retry_counts.get(task_id, 0)
        return current_retries < self.max_retries
    
    def record_retry(self, task_id: str) -> int:
        """
        记录重试次数
        
        Args:
            task_id: 任务ID
        
        Returns:
            当前重试次数
        """
        self.retry_counts[task_id] = self.retry_counts.get(task_id, 0) + 1
        return self.retry_counts[task_id]
    
    def get_retry_delay(self, task_id: str) -> float:
        """
        获取重试延迟时间
        
        Args:
            task_id: 任务ID
        
        Returns:
            延迟时间（秒）
        """
        retry_count = self.retry_counts.get(task_id, 0)
        return min(30.0, self.backoff_factor ** retry_count)
    
    def reset_retries(self, task_id: str):
        """重置重试计数"""
        self.retry_counts.pop(task_id, None)
    
    def cleanup(self):
        """清理重试记录"""
        self.retry_counts.clear()


# 全局错误恢复实例
global_error_recovery = ErrorRecovery()


def create_error_context(task_id: str, operation: str) -> Dict[str, Any]:
    """
    创建错误上下文信息
    
    Args:
        task_id: 任务ID
        operation: 操作名称
    
    Returns:
        错误上下文字典
    """
    import time
    return {
        "task_id": task_id,
        "operation": operation,
        "timestamp": time.time(),
        "thread_id": __import__('threading').current_thread().ident
    }