"""
音频段生成器

提供更高级的音频段生成功能，包括内存管理、缓存和优化策略。
"""

import os
import threading
import time
from pathlib import Path
from typing import Generator, Optional, List, Dict
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor, Future

from v2subpy.utils.log import logger
from .models import AudioSegment, RealtimeConfig
from .audio_segmenter import AudioSegmenter
from .exceptions import AudioExtractionError, BufferUnderrunError


class AudioSegmentGenerator:
    """高级音频段生成器"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化音频段生成器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.segmenter = AudioSegmenter(realtime_config)
        # 使用无限大小的队列来容纳所有音频段
        self.buffer_queue = Queue()  # 移除 maxsize 限制
        self.is_generating = False
        self.generation_thread: Optional[threading.Thread] = None
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.segment_cache: Dict[str, AudioSegment] = {}
        self.total_segments = 0
        self.generated_segments = 0

        # 添加队列锁，确保跳转操作时的原子性
        self._queue_lock = threading.RLock()
        self._is_reordering = False  # 标记是否正在重排序

        logger.debug(f"AudioSegmentGenerator 初始化: 使用无限大小队列")
    
    def start_generation(self, video_path: str) -> None:
        """
        开始异步生成音频段
        
        Args:
            video_path: 视频文件路径
        """
        if self.is_generating:
            logger.warning("音频段生成已在进行中")
            return
        
        self.is_generating = True
        self.generation_thread = threading.Thread(
            target=self._generate_segments_async,
            args=(video_path,),
            daemon=True
        )
        self.generation_thread.start()
        logger.info(f"开始异步生成音频段: {video_path}")
    
    def get_next_segment(self, timeout: float = 10.0) -> Optional[AudioSegment]:
        """
        获取下一个音频段（线程安全）

        Args:
            timeout: 超时时间（秒）

        Returns:
            AudioSegment: 音频段对象，如果没有更多段落则返回 None

        Raises:
            BufferUnderrunError: 缓冲区为空且生成已完成
        """
        # 检查是否正在重排序，如果是则等待
        while self._is_reordering:
            print(f"[DEBUG] Worker等待队列重排序完成...")
            import time
            time.sleep(0.1)  # 短暂等待

        with self._queue_lock:
            try:
                segment = self.buffer_queue.get(timeout=timeout)
                if segment is None:  # 结束标记
                    return None

                self.generated_segments += 1
                logger.debug(f"获取音频段: {segment.segment_id}, "
                            f"时间: {segment.start_time:.1f}s-{segment.end_time:.1f}s, "
                            f"进度: {self.generated_segments}/{self.total_segments}")

                return segment

            except Empty:
                if not self.is_generating:
                    return None

                raise BufferUnderrunError(
                    "音频段缓冲区为空，处理速度可能跟不上",
                    buffer_level=self.get_buffer_level()
                )
    
    def get_segments_batch(self, batch_size: int = 3) -> List[AudioSegment]:
        """
        批量获取音频段
        
        Args:
            batch_size: 批量大小
            
        Returns:
            List[AudioSegment]: 音频段列表
        """
        segments = []
        for _ in range(batch_size):
            segment = self.get_next_segment(timeout=1.0)
            if segment is None:
                break
            segments.append(segment)
        
        logger.debug(f"批量获取音频段: {len(segments)} 个")
        return segments
    
    def get_buffer_level(self) -> int:
        """
        获取缓冲区水平（0-100）
        
        Returns:
            int: 缓冲区水平百分比
        """
        if self.config.buffer_size == 0:
            return 0
        
        current_size = self.buffer_queue.qsize()
        return int((current_size / self.config.buffer_size) * 100)
    
    def get_generation_progress(self) -> Dict[str, any]:
        """
        获取生成进度信息
        
        Returns:
            dict: 进度信息
        """
        return {
            'total_segments': self.total_segments,
            'generated_segments': self.generated_segments,
            'buffer_level': self.get_buffer_level(),
            'is_generating': self.is_generating,
            'progress_percentage': (
                (self.generated_segments / self.total_segments * 100) 
                if self.total_segments > 0 else 0
            )
        }
    
    def stop_generation(self) -> None:
        """停止音频段生成"""
        if not self.is_generating:
            return
        
        logger.info("停止音频段生成")
        self.is_generating = False
        
        # 等待生成线程结束
        if self.generation_thread and self.generation_thread.is_alive():
            self.generation_thread.join(timeout=5.0)
        
        # 清空缓冲区
        while not self.buffer_queue.empty():
            try:
                self.buffer_queue.get_nowait()
            except Empty:
                break
        
        # 清理资源
        self.cleanup()
    
    def adjust_priority(self, target_position: float) -> bool:
        """
        调整音频段处理优先级

        重新排序队列：[目标位置及后续片段] + [之前的片段]
        例如：跳转到300s，队列从[1,2,3,...,60]变为[30,31,32,...,60,1,2,3,...,29]

        Args:
            target_position: 目标播放位置（秒）

        Returns:
            bool: 是否调整成功
        """
        print(f"[DEBUG] AudioSegmentGenerator.adjust_priority 被调用: target_position={target_position}")
        try:
            # 计算目标片段索引
            segment_duration = self.config.segment_duration
            target_segment_index = int(target_position // segment_duration)

            print(f"[DEBUG] 目标位置 {target_position}s → 片段索引 {target_segment_index} (片段时长 {segment_duration}s)")
            logger.info(f"调整优先级: 目标位置 {target_position}s → 片段索引 {target_segment_index}")

            # 检查是否有缓存的所有音频段
            if not self.segment_cache:
                print(f"[DEBUG] 没有缓存的音频段，无法调整优先级")
                logger.warning("没有缓存的音频段，无法调整优先级")
                return False

            print(f"[DEBUG] 缓存中有 {len(self.segment_cache)} 个音频段，开始重排序")

            # 获取所有音频段并按时间排序
            all_segments = list(self.segment_cache.values())
            all_segments.sort(key=lambda s: s.start_time)

            print(f"[DEBUG] 排序后的音频段范围: {all_segments[0].start_time:.1f}s - {all_segments[-1].end_time:.1f}s")

            # 改进的重排序算法：包含目标时间点的片段及后续 + 之前的片段
            target_and_after = []  # 包含目标位置及后续片段
            before_target = []     # 目标位置之前的片段

            for segment in all_segments:
                # 检查片段是否包含目标时间点，或者在目标时间点之后
                if segment.end_time > target_position:
                    target_and_after.append(segment)
                    print(f"[DEBUG] 优先片段: {segment.segment_id}, 时间: {segment.start_time:.1f}s-{segment.end_time:.1f}s (包含或在目标位置{target_position}s之后)")
                else:
                    before_target.append(segment)

            print(f"[DEBUG] 分类结果: {len(target_and_after)} 个目标及后续片段, {len(before_target)} 个之前片段")

            if not target_and_after:
                print(f"[DEBUG] 没有找到包含目标位置 {target_position}s 及后续的片段")
                logger.warning(f"没有找到包含目标位置 {target_position}s 及后续的音频段")
                return False

            # 新的队列顺序：目标及后续 + 之前的
            reordered_segments = target_and_after + before_target

            print(f"[DEBUG] 重排序完成，新顺序前5个片段:")
            for i, seg in enumerate(reordered_segments[:5]):
                seg_idx = int(seg.start_time // segment_duration)
                print(f"[DEBUG]   {i+1}. 片段{seg_idx}: {seg.start_time:.1f}s-{seg.end_time:.1f}s ({seg.segment_id})")

            # 清空并重新填充队列
            self._replace_queue_with_reordered_segments(reordered_segments)

            print(f"[DEBUG] 队列重排序成功: {len(target_and_after)} 个优先片段被提前")
            logger.info(f"队列重排序成功: {len(target_and_after)} 个优先片段被提前")
            return True

        except Exception as e:
            print(f"[DEBUG] 优先级调整异常: {e}")
            logger.error(f"调整音频段优先级时发生异常: {e}")
            return False

    def _replace_queue_with_reordered_segments(self, reordered_segments):
        """
        用重排序的音频段替换队列内容（线程安全）

        Args:
            reordered_segments: 重排序后的音频段列表
        """
        print(f"[DEBUG] 开始替换队列，共 {len(reordered_segments)} 个音频段")

        # 设置重排序标志，阻止worker获取任务
        self._is_reordering = True
        print(f"[DEBUG] 设置重排序标志，阻止worker获取任务")

        try:
            with self._queue_lock:
                print(f"[DEBUG] 获取队列锁，开始原子操作")

                # 清空当前缓冲队列
                cleared_count = 0
                while not self.buffer_queue.empty():
                    try:
                        self.buffer_queue.get_nowait()
                        cleared_count += 1
                    except Empty:
                        break

                print(f"[DEBUG] 清空了队列中的 {cleared_count} 个音频段")

                # 将重排序的音频段放入队列
                for segment in reordered_segments:
                    self.buffer_queue.put(segment)

                # 如果生成已完成，添加结束标记
                if not self.is_generating:
                    self.buffer_queue.put(None)
                    print(f"[DEBUG] 已添加结束标记")

                print(f"[DEBUG] 队列替换完成，新队列包含 {len(reordered_segments)} 个音频段")

        finally:
            # 清除重排序标志，允许worker继续获取任务
            self._is_reordering = False
            print(f"[DEBUG] 清除重排序标志，worker可以继续获取任务")


    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理临时文件
            self.segmenter.cleanup_temp_files()

            # 清理缓存
            self.segment_cache.clear()

            # 关闭线程池
            self.executor.shutdown(wait=False)

            logger.debug("音频段生成器资源清理完成")

        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")
    
    def _generate_segments_async(self, video_path: str) -> None:
        """异步生成音频段的内部方法"""
        try:
            # 获取分段信息
            segment_info = self.segmenter.get_segment_info(video_path)
            self.total_segments = segment_info['estimated_segment_count']

            logger.info(f"开始生成音频段，预计 {self.total_segments} 个段落")
            print(f"[DEBUG] 开始一次性生成所有音频段，总计 {self.total_segments} 个")

            # 一次性生成所有音频段
            all_segments = []
            for segment in self.segmenter.extract_and_segment(video_path):
                if not self.is_generating:
                    break

                # 缓存段落信息
                self.segment_cache[segment.segment_id] = segment
                all_segments.append(segment)

                if len(all_segments) % 10 == 0:  # 每10个打印一次进度
                    print(f"[DEBUG] 已生成 {len(all_segments)} 个音频段")
                logger.debug(f"音频段已生成: {segment.segment_id}")

            print(f"[DEBUG] 所有音频段生成完成，共 {len(all_segments)} 个")
            logger.info(f"所有音频段生成完成，共 {len(all_segments)} 个")

            # 将所有音频段按顺序放入队列
            print(f"[DEBUG] 开始将所有 {len(all_segments)} 个音频段放入队列")
            for i, segment in enumerate(all_segments):
                if not self.is_generating:
                    break
                self.buffer_queue.put(segment)
                if (i + 1) % 10 == 0:  # 每10个打印一次进度
                    print(f"[DEBUG] 已放入队列 {i + 1} 个音频段")

            # 发送结束标记
            self.buffer_queue.put(None)

            print(f"[DEBUG] 所有 {len(all_segments)} 个音频段已放入队列")
            logger.info("音频段生成和入队完成")

        except Exception as e:
            logger.error(f"音频段生成失败: {e}")
            # 发送错误标记
            self.buffer_queue.put(None)
        finally:
            self.is_generating = False
    
    def get_segment_by_time(self, timestamp: float) -> Optional[AudioSegment]:
        """
        根据时间戳获取对应的音频段
        
        Args:
            timestamp: 时间戳（秒）
            
        Returns:
            AudioSegment: 对应的音频段，如果没有找到则返回 None
        """
        for segment in self.segment_cache.values():
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None
    
    def preload_segments(self, video_path: str, start_time: float = 0.0, 
                        duration: float = 30.0) -> List[AudioSegment]:
        """
        预加载指定时间范围的音频段
        
        Args:
            video_path: 视频文件路径
            start_time: 开始时间
            duration: 持续时间
            
        Returns:
            List[AudioSegment]: 预加载的音频段列表
        """
        try:
            segments = []
            end_time = start_time + duration
            
            for segment in self.segmenter.extract_and_segment(video_path):
                # 检查段落是否在指定时间范围内
                if segment.end_time < start_time:
                    continue
                if segment.start_time > end_time:
                    break
                
                segments.append(segment)
                self.segment_cache[segment.segment_id] = segment
            
            logger.info(f"预加载音频段完成: {len(segments)} 个段落 "
                       f"({start_time:.1f}s - {end_time:.1f}s)")
            
            return segments
            
        except Exception as e:
            logger.error(f"预加载音频段失败: {e}")
            return []


class StreamingAudioGenerator:
    """流式音频段生成器（用于实时处理）"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化流式音频段生成器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.segmenter = AudioSegmenter(realtime_config)
        self.current_position = 0.0
        
    def generate_next_segment(self, video_path: str) -> Optional[AudioSegment]:
        """
        生成下一个音频段（流式处理）
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            AudioSegment: 下一个音频段，如果没有更多段落则返回 None
        """
        try:
            # 获取视频总时长
            segment_info = self.segmenter.get_segment_info(video_path)
            total_duration = segment_info['total_duration']
            
            if self.current_position >= total_duration:
                return None
            
            # 计算段落时间范围
            start_time = max(0.0, self.current_position - self.config.overlap_duration)
            end_time = min(total_duration, self.current_position + self.config.segment_duration)
            
            # 创建音频段
            segment = self.segmenter._create_audio_segment(
                self.segmenter._extract_full_audio(video_path),
                start_time, end_time
            )
            
            # 更新位置
            self.current_position += self.config.segment_duration
            
            return segment
            
        except Exception as e:
            logger.error(f"生成下一个音频段失败: {e}")
            return None
    
    def reset_position(self, position: float = 0.0) -> None:
        """
        重置生成位置
        
        Args:
            position: 新的位置（秒）
        """
        self.current_position = position
        logger.debug(f"重置音频段生成位置: {position}s")