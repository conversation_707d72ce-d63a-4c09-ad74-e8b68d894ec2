"""
实时字幕处理的数据模型定义

包含音频段、字幕段、处理状态和配置等数据结构。
"""

from dataclasses import dataclass, asdict
from typing import Optional
import numpy as np


@dataclass
class AudioSegment:
    """音频段数据模型"""
    segment_id: str          # 段落唯一标识
    start_time: float        # 开始时间（秒）
    end_time: float          # 结束时间（秒）
    audio_data: Optional[np.ndarray] = None   # 音频数据
    sample_rate: int = 16000 # 采样率
    channels: int = 1        # 声道数
    file_path: str = ""      # 临时文件路径（如果需要）
    
    def duration(self) -> float:
        """获取音频段时长"""
        return self.end_time - self.start_time
    
    def to_dict(self) -> dict:
        """转换为字典格式（排除音频数据）"""
        data = asdict(self)
        # 移除音频数据，避免序列化问题
        data.pop('audio_data', None)
        return data


@dataclass
class SubtitleSegment:
    """字幕段数据模型"""
    segment_id: str          # 段落标识
    start_time: float        # 开始时间（秒）
    end_time: float          # 结束时间（秒）
    original_text: str       # 原始文本
    translated_text: str = ""     # 翻译文本
    confidence: float = 0.0       # 识别置信度
    language: str = ""            # 识别语言
    status: str = "completed"     # 状态：pending/processing/completed/error
    
    def duration(self) -> float:
        """获取字幕段时长"""
        return self.end_time - self.start_time
    
    def to_dict(self) -> dict:
        """转换为字典格式用于回调传输"""
        return asdict(self)


@dataclass
class ProcessingStatus:
    """处理状态模型"""
    current_segment: int = 0      # 当前处理段落索引
    total_segments: int = 0       # 总段落数（估算）
    processed_duration: float = 0.0 # 已处理时长
    total_duration: float = 0.0   # 总时长
    processing_speed: float = 1.0 # 处理速度倍率
    buffer_level: int = 0         # 缓冲区水平（0-100）
    
    def progress_percentage(self) -> float:
        """计算进度百分比"""
        if self.total_duration <= 0:
            return 0.0
        return min(100.0, (self.processed_duration / self.total_duration) * 100.0)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class RealtimeConfig:
    """实时处理配置参数"""
    # 音频处理配置
    segment_duration: float = 8.0      # 音频段长度（秒）
    overlap_duration: float = 1.0      # 重叠时长（秒）
    audio_sample_rate: int = 16000     # 音频采样率
    
    # 语音识别配置
    whisper_model: str = "base"        # Whisper模型
    whisper_device: str = "auto"       # 设备选择
    source_language: str = "auto"      # 源语言
    
    # 翻译配置
    translator_type: str = "openai"    # 翻译器类型
    target_language: str = "zh"        # 目标语言
    translation_timeout: float = 10.0  # 翻译超时
    
    # 性能配置
    max_concurrent_segments: int = 3   # 最大并发段数
    buffer_size: int = 10             # 缓冲区大小
    
    # 错误处理配置
    max_retries: int = 3              # 最大重试次数
    retry_delay: float = 1.0          # 重试延迟
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'RealtimeConfig':
        """从字典创建配置"""
        # 过滤掉不存在的字段
        valid_fields = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_fields}
        return cls(**filtered_dict)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return asdict(self)
    
    def validate(self) -> None:
        """验证配置参数的有效性"""
        if self.segment_duration <= 0:
            raise ValueError("segment_duration must be positive")
        if self.overlap_duration < 0:
            raise ValueError("overlap_duration must be non-negative")
        if self.overlap_duration >= self.segment_duration:
            raise ValueError("overlap_duration must be less than segment_duration")
        if self.max_concurrent_segments <= 0:
            raise ValueError("max_concurrent_segments must be positive")
        if self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")