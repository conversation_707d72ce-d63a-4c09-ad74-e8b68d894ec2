## v2subpy 开发笔记

### 打包
> 问题: playwright._impl._api_types.Error: Executable doesn't exist at D:\dev\v2subpy\dist\main\playwright\driver\package\.local-browsers\chromium_win64_special-1050\chrome-win\chrome.exe
>
> 方法: copy C:\Users\<USER>\AppData\Local\ms-playwright\chromium_win64_special-1050 to main\playwright\driver\package\.local-browsers\

> 问题: FileNotFoundError: [Errno 2] No such file or directory: './data/config.toml'
> 方法： 修改为取针对basedir拼接路径

> 问题：No such file or directory: 'D:\\dev\\v2subpy\\dist\\main\\whisper\\assets\\mel_filters.npz'
> 方法: copy D:\scoop\apps\miniconda3\current\envs\torchtest\Lib\site-packages\whisper\assets to main\whisper\assets

> 问题: 
> 方法: 
> add it to main.spec
>`
from PyInstaller.utils.hooks import copy_metadata
datas = []
datas += (copy_metadata('tqdm') + copy_metadata('regex')  + copy_metadata('sacremoses') +
        copy_metadata('requests') + copy_metadata('packaging') + copy_metadata('filelock') + 
        copy_metadata('numpy') + copy_metadata('tokenizers'))
block_cipher = None
`


## 关于配置文件(config.toml)
### model 
  - device 
    -  '' 代表自动优先cuda， 其次cpu
    -  'cpu' 强制使用cpu

## 关于多语言配置
> 使用一个单独lang.toml 配置， 在config.toml中配置lang='chinese'
> lang.toml 对应每个语言有一个section

## 支持语言列表整理
```
whisper: 
None: 为自动探测

deepl:
'auto': 为自动探测

deepl_cli:
deeplx

语言列表相同:
目前支持
lang.en =  英语
lang.zh =  中文
lang.ja =  日语
lang.fr =  法语
lang.ko =  韩语
lang.de =  德语
lang.ru =  俄语
lang.es =  西班牙语
lang.pt =  葡萄牙语
lang.it =  意大利语
lang.tr =  土耳其语
lang.cs =  捷克语
```

```
whisper 

    "en": "english",
    "zh": "chinese",
    "de": "german",
    "es": "spanish",
    "ru": "russian",
    "ko": "korean",
    "fr": "french",
    "ja": "japanese",
    "pt": "portuguese",
    "tr": "turkish",
    "pl": "polish",
    "ca": "catalan",
    "nl": "dutch",
    "ar": "arabic",
    "sv": "swedish",
    "it": "italian",
    "id": "indonesian",
    "hi": "hindi",
    "fi": "finnish",
    "vi": "vietnamese",
    "he": "hebrew",
    "uk": "ukrainian",
    "el": "greek",
    "ms": "malay",
    "cs": "czech",
    "ro": "romanian",
    "da": "danish",
    "hu": "hungarian",
    "ta": "tamil",
    "no": "norwegian",
    "th": "thai",
    "ur": "urdu",
    "hr": "croatian",
    "bg": "bulgarian",
    "lt": "lithuanian",
    "la": "latin",
    "mi": "maori",
    "ml": "malayalam",
    "cy": "welsh",
    "sk": "slovak",
    "te": "telugu",
    "fa": "persian",
    "lv": "latvian",
    "bn": "bengali",
    "sr": "serbian",
    "az": "azerbaijani",
    "sl": "slovenian",
    "kn": "kannada",
    "et": "estonian",
    "mk": "macedonian",
    "br": "breton",
    "eu": "basque",
    "is": "icelandic",
    "hy": "armenian",
    "ne": "nepali",
    "mn": "mongolian",
    "bs": "bosnian",
    "kk": "kazakh",
    "sq": "albanian",
    "sw": "swahili",
    "gl": "galician",
    "mr": "marathi",
    "pa": "punjabi",
    "si": "sinhala",
    "km": "khmer",
    "sn": "shona",
    "yo": "yoruba",
    "so": "somali",
    "af": "afrikaans",
    "oc": "occitan",
    "ka": "georgian",
    "be": "belarusian",
    "tg": "tajik",
    "sd": "sindhi",
    "gu": "gujarati",
    "am": "amharic",
    "yi": "yiddish",
    "lo": "lao",
    "uz": "uzbek",
    "fo": "faroese",
    "ht": "haitian creole",
    "ps": "pashto",
    "tk": "turkmen",
    "nn": "nynorsk",
    "mt": "maltese",
    "sa": "sanskrit",
    "lb": "luxembourgish",
    "my": "myanmar",
    "bo": "tibetan",
    "tl": "tagalog",
    "mg": "malagasy",
    "as": "assamese",
    "tt": "tatar",
    "haw": "hawaiian",
    "ln": "lingala",
    "ha": "hausa",
    "ba": "bashkir",
    "jw": "javanese",
    "su": "sundanese",
}
```
```
deepl

BG - Bulgarian      保加利亚语
CS - Czech          捷克语
DA - Danish         丹麦语
DE - German         德语
EL - Greek          希腊语
EN - English        英语
ES - Spanish        西班牙语
ET - Estonian       爱沙尼亚语
FI - Finnish        芬兰语
FR - French         法语
HU - Hungarian      匈牙利语
ID - Indonesian     印尼语
IT - Italian        意大利语
JA - Japanese       日语
KO - Korean         韩语
LT - Lithuanian     立陶宛语
LV - Latvian        拉脱维亚语
NB - Norwegian      挪威语
NL - Dutch          荷兰语
PL - Polish         波兰语
PT - Portuguese     葡萄牙语
RO - Romanian       罗马尼亚语
RU - Russian        俄语
SK - Slovak         斯洛伐克语
SL - Slovenian      斯洛文尼亚语
SV - Swedish        瑞典语
TR - Turkish        土耳其语
UK - Ukrainian      乌克兰语
ZH - Chinese        中文
If this parameter is omitted, the API will attempt to detect the language of the text and translate it.
```
### 异常处理
```
异常处理流程： 后端主函数捕获各种异常， 统一记录log后抛出自定义异常
前端捕获自定义异常， 后在客户端打印提示信息
```
自异常列表
- ModelLoadError （模型加载异常， 可能显存不够）
- TranslateError  (翻译错误， 超时)


### 支持音频格式
.wav
.wma
.mpa
.mp2
.mp3
.ogg
.m4a
.aac
.mka
.ra
.flac
.ape
.mpc
.mod
.ac3
.eac3
.dts
.dtshd
.wv
.tak
.tta
.aiff
.aif
.opus
.amr


### 支持视频格式
.wmv
.avi
.asf
.mpeg
.mpg
.ts
.mp4
.3gp
.mkv
.rm
.rmvb
.webm
.f4v
.divx
.vob
.mov