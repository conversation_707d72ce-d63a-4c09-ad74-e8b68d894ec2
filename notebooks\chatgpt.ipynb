{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from deepl import deepl"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["source_lang = 'en'\n", "target_lang = 'zh'\n", "translator = deepl.DeepLCLI(source_lang, target_lang)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: This event loop is already running\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_8720\\3761858853.py\", line 4, in <module>\n", "    result = translator.translate(text)\n", "  File \"d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\site-packages\\deepl\\serializable.py\", line 14, in wrapper\n", "    return loop.run_until_complete(func(*args, **kwargs))\n", "  File \"d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\asyncio\\base_events.py\", line 625, in run_until_complete\n", "    self._check_running()\n", "  File \"d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\asyncio\\base_events.py\", line 584, in _check_running\n", "    raise RuntimeError('This event loop is already running')\n", "RuntimeError: This event loop is already running\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_8720\\3761858853.py:8: RuntimeWarning: coroutine 'DeepLCLI.translate' was never awaited\n", "  print(traceback.format_exc())\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}], "source": ["import traceback\n", "text = \"\"\"\n", "Hello, in this video we will try to crack open this padlock in a different manner that doesn't\n", "involve going through every single combination, just like we did in the previous video.\n", "This is similar to when you are writing your algorithm to the same problem you might have,\n", "a different way of solving it, and you need to choose between two different implementations\n", "or two different algorithms.\n", "The big O notation gives us a tool to decide which algorithm scales better with the input\n", "But first let me show you how we can open this padlock.\n", "What we need to crack open this padlock in a faster way is some plastic explosive and\n", "very simple circuit.\n", "And with the plastic explosive we just need to stick it on the arm over here, we just\n", "have to be a bit careful with this stuff, stick it all around, and then we just grab\n", "this metal electric rod, we stick it in slowly into the explosive, we stand a little bit back,\n", "and then we just press this button.\n", "And nothing happens.\n", "\"\"\"\n", "print('lens: ', len(text))\n", "try:\n", "    result = translator.translate(text)\n", "    print(result)\n", "except Exception as e:\n", "    print(f\"Error: {e}\")\n", "    print(traceback.format_exc())"]}], "metadata": {"kernelspec": {"display_name": "torchtest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}