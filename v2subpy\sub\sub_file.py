'''
文件处理父类
'''
import copy
import json
from pathlib import Path
import re
import time
import textwrap
import srt

from srt import Subtitle
from typing import List, Generator
from v2subpy.sub.subutils import handle_chinese_subtitles, get_wrap_line_limit

from v2subpy.trans.base import BaseTranslator
from v2subpy.utils.log import logger
from v2subpy.utils.config import get_key, get_text, app_is_free, FREE_COUNT
from v2subpy.utils.loadfile import get_file
from v2subpy.utils.common import is_task_stopped, TaskStoppedError, get_share_value, SubtitleFormatEnum

TRANSLATION_PROGRESS_FILE = 'data/translation_progress.json'


class SubFile:
    '''
    可处理的文件基类， 具体文件类型
    可继承此类
    '''
    READERS = {}

    def __init__(self, filepath: str | None, target_lang: str, print_info: bool = True, need_clean: bool = True) -> None:
        self.filepath = filepath
        self.target_lang = target_lang
        self.subtitles = []
        self.length = 0
        self.print_info = print_info
        self.need_clean = need_clean
        if self.print_info:
            logger.info(get_text("info.ReadSubFile").format(filepath))
        self.load()

    @classmethod
    def get_reader(cls, ext: str):
        return cls.READERS.get(ext)

    def __init_subclass__(cls, **kwargs):
        '''
        自动注册子类
        '''
        SubFile.READERS[cls.EXT] = cls

    def load(self):
        '''
        读取文件内容并处理
        '''
        with open(self.filepath, "r", encoding="utf-8", errors="ignore") as input_file:
            subtitles = self.parse(input_file)
            if self.need_clean:
                subtitles = self._clean_subs_content(subtitles)
            self.subtitles = subtitles

        # copy self.subtitles
        self.copy_subtitles = copy.deepcopy(self.subtitles)
        self.length = sum(len(sub.content) + 1 for sub in self.subtitles)
        self.sub_count = len(self.subtitles)
        translated_dict = self.load_progress()
        if translated_dict is not None:
            self.merge_progress(translated_dict)
        else:
            self.prepare_progress()

    def parse(self, input_file) -> list[Subtitle]:
        '''
        解析文件， 并返回字幕列表对象
        子类应继承此方法

        Args:
            input_file: 文件对象

        Return:
            list of subtitle object (index, start, end, content)
        '''
        pass

    def prepare_progress(self):
        '''
        保存一个空的进度文件， 以便后续使用
        '''
        jsonfile = get_file(TRANSLATION_PROGRESS_FILE)
        translated_dict = self.make_translated_dict()
        with open(jsonfile, "w", encoding="utf-8") as f:
            json.dump(translated_dict, f, ensure_ascii=False, indent=4)

    def make_translated_dict(self, translation: dict = None) -> dict:
        '''
        准备一个dict， 用来保存翻译进度
        '''
        if translation is None:
            translation = {}
        translated_dict = {
            'filepath': self.filepath,
            'target_lang': self.target_lang,
            'translation': translation
        }
        return translated_dict

    def merge_progress(self, translated_dict):
        '''
        合并上次翻译进度到当前处理
        '''
        translation = translated_dict['translation']
        for sub in self.subtitles:
            translation_text = translation.get(str(sub.index))
            if translation_text:
                sub.content = translation_text
                sub.translated = True
            else:
                break

    def load_progress(self):
        '''
        检查是否有上次相同翻译进度
        '''
        try:
            jsonfile = get_file(TRANSLATION_PROGRESS_FILE)
            with open(jsonfile, "r", encoding="utf-8") as f:
                translated_dict = json.load(f)
                if self.filepath == translated_dict['filepath'] and self.target_lang == translated_dict['target_lang']:
                    return translated_dict
                else:
                    return None
        except:
            return None

    def is_empty(self) -> bool:
        '''检查是否为空文件'''
        return len(self.subtitles) == 0

    def _get_next_chunk(self, chunk_size: int = 4500) -> Generator:
        """Get a portion of the subtitles at the time based on the chunk size

        Args:
            chunk_size (int, optional): Maximum number of letter in text chunk. Defaults to 4500.

        Yields:
            Generator: Each chunk at the time
        """
        portion = []

        for subtitle in self.subtitles:
            if getattr(subtitle, 'translated', False):
                continue
            # Calculate new chunk size if subtitle content is added to actual chunk
            n_char = (
                sum(len(sub.content)
                    for sub in portion)  # All subtitles in chunk
                + len(subtitle.content)  # New subtitle
                + len(portion)  # Break lines in chunk
                + 1  # New breakline
            )

            # If chunk goes beyond the limit, yield it
            if n_char >= chunk_size and len(portion) != 0:
                yield portion
                portion = []

            # Put subtitle content in chunk
            portion.append(subtitle)

        # Yield las chunk
        yield portion

    def _clean_subs_content(self, subtitles: List[Subtitle]) -> List[Subtitle]:
        """Cleans subtitles content and delete line breaks

        Args:
            subtitles (List[Subtitle]): List of subtitles

        Returns:
            List[Subtitle]: Same list of subtitles, but cleaned
        """
        cleanr = re.compile("<.*?>")

        for sub in subtitles:
            sub.content = cleanr.sub("", sub.content)
            sub.content = srt.make_legal_content(sub.content)
            sub.content = sub.content.strip()

            if all(sentence.startswith("-") for sentence in sub.content.split("\n")):
                sub.content = sub.content.replace("\n", "_")
                continue

            sub.content = sub.content.replace("\n", " ")

        return subtitles

    def wrap_lines(self, line_wrap_limit: int | None = None) -> None:
        """Wrap lines in all subtitles in file

        Args:
            line_wrap_limit (int): Number of maximum characters in a line before wrap. Defaults to 50.
        """
        if not line_wrap_limit:
            line_wrap_limit = get_wrap_line_limit(self.target_lang)
        logger.debug(f'wrap lines: {line_wrap_limit=}')
        wraper = textwrap.TextWrapper(width=line_wrap_limit)
        split_char = self.get_split_char()
        for sub in self.subtitles:
            # 可能多行， 对每一行处理换行
            filled_lines = []
            for content_line in sub.content.splitlines():
                if len(content_line) > line_wrap_limit:
                    filled_line = wraper.fill(content_line)
                else:
                    # fill in space at both end
                    diff = line_wrap_limit - len(content_line)
                    left_gap = diff // 2
                    right_gap = diff - left_gap
                    filled_line = f'{left_gap * "　"}{content_line}{right_gap * "　"}'
                if split_char != '\n':
                    filled_line = filled_line.replace('\n', split_char)
                filled_lines.append(filled_line)
            content = split_char.join(filled_lines)
            sub.content = content

    def wrap_line(self, text: str, line_wrap_limit: int = 50) -> str:
        """Wraps a line of text without breaking any word in half

        Args:
            text (str): Line text to wrap
            line_wrap_limit (int): Number of maximum characters in a line before wrap. Defaults to 50.

        Returns:
            str: Text line wraped
        """
        wraped_lines = []
        for word in text.split():
            # Check if inserting a word in the last sentence goes beyond the wrap limit
            if (
                len(wraped_lines) != 0
                and len(wraped_lines[-1]) + len(word) < line_wrap_limit
            ):
                # If not, add it to it
                wraped_lines[-1] += f" {word}"
                continue

            # Insert a new sentence
            wraped_lines.append(f"{word}")

        # Join sentences with line break
        return "\n".join(wraped_lines)

    def translate(
        self,
        translator: BaseTranslator,
        source_language: str,
        destination_language: str,
    ) -> None:
        """Translate SRT file using a translator of your choose

        Args:
            translator (Translator): Translator object of choose
            destination_language (str): Destination language (must be coherent with your translator)
            source_language (str): Source language (must be coherent with your translator)
        """
        progress = 0
        # 检查是否有上次进度
        for sub in self.subtitles:
            if getattr(sub, 'translated', False):
                progress = sub.index
            else:
                break

        if progress > 0:
            logger.info(get_text("info.TranslateLoadProgress").format(
                float(100 * progress / self.sub_count)))
        if progress == self.sub_count:
            return
        chunk_max_char = translator.translator_config.get('max_char')
        logger.debug(f'get chunk: {chunk_max_char}')
        # For each chunk of the file (based on the translator capabilities)
        for subs_slice in self._get_next_chunk(chunk_max_char):
            # 检查是否需要停止任务
            if is_task_stopped():
                raise TaskStoppedError()
            # 翻译器是否需要保留srt的时间等格式信息
            if translator.translator_config["with_srt_format"]:
                text = [sub.to_srt() for sub in subs_slice]
                text = "".join(text)
            else:
                # Put chunk in a single text with break lines
                text = [sub.content for sub in subs_slice]
                text = "\n".join(text)

            # Translate
            time.sleep(translator.translator_config['wait_for_request'])
            translation = translator.translate(
                text, source_language, destination_language
            )

            # Break each line back into subtitle content
            translation = translation.splitlines()
            # logger.debug(
            #     f"before {len(subs_slice)} lines, after {len(translation)} lines")
            for sub, trans in zip(subs_slice, translation):
                sub.content = trans
                sub.translated = True

            progress = subs_slice[-1].index
            logger.info(get_text("info.TranslateProgress").format(
                float(100 * progress / self.sub_count)))
            # 查看是否设置共享变量， update_progress
            update_progress = get_share_value('update_progress')
            if update_progress:
                status_message = get_text("info.StatusTranslateProgress")
                progress_info = (progress, self.sub_count)
                update_progress(status_message, progress_info)
            self.save_progress(subs_slice)

    def save_progress(self, subs_slice):
        '''
        保存翻译进度
        '''
        jsonfile = get_file(TRANSLATION_PROGRESS_FILE)
        try:
            with open(jsonfile, "r+", encoding="utf-8") as f:
                translation_dict = json.load(f)
                slice_dict = {sub.index: sub.content for sub in subs_slice}
                translation_dict['translation'].update(slice_dict)
                f.seek(0)
                json.dump(translation_dict, f, ensure_ascii=False, indent=4)
        except json.decoder.JSONDecodeError:
            logger.debug('json decode error')
        except:
            pass

    def save(self, filepath: str) -> None:
        """Saves SRT to file

        Args:
            filepath (str): Path of the new file
        """
        subtitles = self.handle_free()
        if self.target_lang == 'zh':
            handle_chinese_subtitles(subtitles)
        for sub in subtitles:
            if hasattr(sub, 'translated'):
                delattr(sub, 'translated')
        subtitles = self.compose(subtitles)
        with open(filepath, "w", encoding="utf-8") as file_out:
            file_out.write(subtitles)
        if get_key('sub.need_merge'):
            pf_merge = Path(filepath)
            merge_file = pf_merge.with_stem(
                pf_merge.stem.rsplit('_', 1)[0] + '_' + 'merge')
            self.save_merge(str(merge_file))

    def get_split_char(self):
        '''
        根据格式不同用不同字符分隔行
        '''
        pf = Path(self.filepath)
        split_char = '\n'
        if pf.suffix[1:] == SubtitleFormatEnum.ASS.value:
            split_char = '\\N'
        return split_char

    def save_merge(self, filepath: str):
        # true , original is up; false, original is down
        merge_style_up = get_key('sub.merge_style')
        split_char = self.get_split_char()
        for orinal_sub, sub in zip(self.copy_subtitles, self.subtitles):
            if merge_style_up:
                sub.content = f"{orinal_sub.content}{split_char}{sub.content}"
            else:
                sub.content = f"{sub.content}{split_char}{orinal_sub.content}"
        subtitles = self.compose(self.subtitles)
        with open(filepath, "w", encoding="utf-8") as file_out:
            file_out.write(subtitles)
        logger.info(get_text("info.TranslateSaveMergeFile").format(filepath))

    def compose(self, subtitles: list[Subtitle]) -> str:
        '''
        将字幕列表对象转换为需要写入的字符串
        '''
        pass

    def handle_free(self) -> list:
        if not app_is_free():
            return self.subtitles
        subtitles = []
        for sub in self.subtitles:
            if sub.index <= FREE_COUNT:
                free_ad = get_text("info.FreeSubtitleAD")
                sub.content = free_ad + sub.content
            subtitles.append(sub)
        return subtitles

    def clean(self):
        '''
        翻译后清理
        成功翻译后删除进度文件
        '''
        jsonfile = get_file(TRANSLATION_PROGRESS_FILE)
        p = Path(jsonfile)
        p.unlink(missing_ok=True)
