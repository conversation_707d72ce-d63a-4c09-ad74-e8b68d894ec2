import time
from v2subpy import v2sub
from v2subpy.utils import config
from v2subpy.utils.log import logger

def test_audio():
    video_path = r"D:\v2sub_release\20250329\问题文件\Risk Event Trading – Gold – FOMC – 13.12.17.mp4"
    source_lang = 'auto'
    model_name = 'large-turbo'
    v2sub.init_sys()
    config.set_app_version(config.Version.ULTRA)
    v2sub.init_model(model_name)

    start = time.time()
    v2sub.gen_subtitle(video_path, source_lang)
    end = time.time()
    print(f"\n\n total  transcribe time is {end - start} seconds")

def test_log():
    logger.debug('111')
    