'''
anthropic claude translator
'''
import re
import os
import time
from pathlib import Path
from typing import Callable
import anthropic
from v2subpy.trans.base import BaseTranslator, TimeOutException, TranslationError, ChatBotException, BaseBot
from v2subpy.utils import config
from v2subpy.sub.subutils import handle_chinese_text
from v2subpy.utils.config import get_text
from v2subpy.utils.log import logger
from v2subpy.trans import langutil
from v2subpy.trans.gpt_prompt import prompter_map, BaseTranslatePrompter
from v2subpy.utils.version_manage import VersionManager, Fn
from v2subpy.utils.common import GptTemperatureEnum, write_file, DefaultModelEnum, LLMMaxcharEnum

class ClaudeTranslator(BaseTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_claude()


    def setup_translator_config(self):
        if langutil.is_cjk_lang(self.source_lang):
            self.translator_config["max_char"] = LLMMaxcharEnum.LLM_MAX_CJK.value
        else:  
            self.translator_config["max_char"] = LLMMaxcharEnum.LLM_MAX_EN.value

    def setup_claude(self):
        self.api_key = self.translator_config.get('api_key')
        default_model = DefaultModelEnum.CLAUDE_DEFAULT.value
        default_temperature = GptTemperatureEnum.medium.value
        self.model = self.translator_config.get(
            'custom_model') or default_model
        self.temperature = self.translator_config.get(
            'gpt_temperature') or default_temperature
        if self.proxies:
            os.environ['http_proxy'] = self.proxies['http']
            os.environ['https_proxy'] = self.proxies['http']

        logger.debug(f'setup claude: {self.api_key}, {self.model}')
        api_domain = self.translator_config.get('api_domain') or None

        self.translator = ClaudeBot(
            api_key=self.api_key, model=self.model, base_url=api_domain, temperature=self.temperature,
                                    retry=2, translator_config=self.translator_config)

    def finished(self, output_file):
        if VersionManager.check_fn(Fn.GPT_SUMMARY):
            if self.translator_config.get(Fn.GPT_SUMMARY):
                pf = Path(output_file)
                pf = pf.with_stem(f'{pf.stem}_summary')
                pf = pf.with_suffix('.txt')
                self.write_summary(str(pf))
                
    def write_summary(self, filepath):
        summaries = self.remove_duplicates_ordered(self.translator.summaries)
        text_summary = '\n'.join(summaries)
        logger.debug(f'summary: \n {text_summary}')
        if self.target_lang == 'zh':
            text_summary = handle_chinese_text(text_summary)
        write_file(filepath, text_summary)
        logger.info(config.get_text("info.ClaudeSummary").format(filepath))

    def _translate(self, text: str, source_lang: str, target_lang: str):
        source_lang = langutil.get_lang_longname(source_lang)
        target_lang = langutil.get_lang_longname(target_lang)
        txt_list = text.strip().split('\n')
        try:
            trans_list = self.translator.translate(
                txt_list, src_lang=source_lang, target_lang=target_lang)

            logger.debug(
                f'lines , before: {len(txt_list)}, after: {len(trans_list)}')
            translation = '\n'.join(trans_list)
            return translation
        except ChatBotException as e:
            raise TimeOutException(
                config.get_text("error.TranslationError").format(str(e)))


class ClaudeBot(BaseBot):
    def __init__(self, api_key, model, base_url=None, temperature=0.0, retry=1, translator_config=None):
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.temperature = temperature
        self.retry = retry
        self.translator_config = translator_config
        self.prompter = 'base_trans'
        self.chunk_size = 30
        self.summaries = []
        self.scenes = []
        self.token_count = 0
        self.timeout = 120
        self.max_tokens = 4096
        self.make_client()

    def make_client(self):
        client_args = {'api_key': self.api_key, 'timeout': self.timeout}
        if self.base_url:
            client_args.update(base_url=self.base_url)
        logger.debug(f'{client_args=}')
        self.client = anthropic.Anthropic(**client_args)

    def translate(self,  texts, src_lang, target_lang):
        prompter: BaseTranslatePrompter = prompter_map[self.prompter](
            src_lang, target_lang)
        chunks = self.make_chunks(texts, chunk_size=self.chunk_size)
        logger.debug(
            f'Translating : {len(chunks)} chunks, {len(texts)} lines in total.')
        # Start chunk-by-chunk translation
        translations = []
        # should be self.summaries, now it's useless , always empty
        summaries = []
        summary, scene = '', ''
        for i, chunk in enumerate(chunks, start=1):
            user_input = prompter.format_texts(chunk)
            user_instrctions = self.translator_config.get('gpt_instructions', '')
            system_prompt = prompter.system()
            user_prompt = prompter.user(
                i, user_input, user_instrctions, summaries, scene)
            content = self._send_messages(system_prompt, user_prompt,
                                          output_checker=prompter.check_format)
            summary, scene, translated = self.parse_responses(content)
            translations.extend(translated)
            self.summaries.append(summary or scene)
            self.scenes.append(scene)
            logger.debug(f'Translating : {i}/{len(chunks)}')
            logger.debug(f'summary: {summary}')
            logger.debug(f'scene: {scene}')

        return translations

    def _send_messages(self, system_prompt: str, user_prompt: str, output_checker: Callable = lambda *args, **kw: True):
        messages = [{'role': 'user', 'content': user_prompt}]
        for retry in range(self.retry):
            try:
                result = self.client.messages.create(
                    model=self.model,
                    messages=messages,
                    system=system_prompt,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )

                if not result.content:
                    error = get_text('error.ChatGptAPIConnect')

                if result.usage:
                    self.token_count += getattr(
                        result.usage, 'input_tokens')
                    self.token_count += getattr(
                        result.usage, 'output_tokens')
                response_text = ''
                for piece in result.content:
                    if piece.type == 'text':
                        response_text = piece.text
                        break

                if not response_text:
                    error = "response was empty"

                if not output_checker(user_prompt, response_text):
                    error = "error format"
                    logger.debug(
                        f'Invalid response format. Retry num: {retry + 1}.')
                    continue
                else:
                    return response_text

            except anthropic.APITimeoutError:
                error = get_text('error.ChatGptTimeout')
                logger.debug(f'Timeout.')
                time.sleep(15)
            except anthropic.RateLimitError:
                error = get_text('error.ChatGptRateLimit')
                logger.debug(f'Rate limit exceeded.')
                time.sleep(15)
            except anthropic.APIError as e:
                error = get_text('error.ChatGptAPIError')
                logger.debug(f'API connection error: {str(e)}')
                time.sleep(15)
            except Exception as e:
                error = get_text('error.ChatGptAPIConnect')
                logger.debug(f'exception type: {type(e)}')
                time.sleep(15)

        if error:
            raise ChatBotException(get_text(
                'error.ClaudeError').format(error))

    def _get_error_message(self, e: anthropic.APIError):
        return e.body.get('error', {}).get('message', e.message)
