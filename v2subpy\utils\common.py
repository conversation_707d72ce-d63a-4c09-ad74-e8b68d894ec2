'''
一些公用的class
'''
import sys
from enum import Enum
from pathlib import Path
import threading
import json
from v2subpy.utils.loadfile import get_file

# 控制前端停止任务
TASK_STOP_EVENT = threading.Event()

# 线程共享变量
SHARE_DICT = threading.local()

MODELS_SUPPORTED_NAMES = ['small', 'medium', 'large', 'small-en', 'medium-en', 'large-turbo']


def set_share_value(name, value):
    setattr(SHARE_DICT, name, value)


def get_share_value(name):
    return getattr(SHARE_DICT, name, None)


def is_task_stopped():
    return TASK_STOP_EVENT.is_set()


def stop_task():
    TASK_STOP_EVENT.set()


def reset_task():
    TASK_STOP_EVENT.clear()


def write_file(filepath, text):
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(text)


class StrEnum(str, Enum):
    def __str__(self) -> str:
        return str.__str__(self)


class V2subError(Exception):
    def __init__(self, message, **args) -> None:
        super().__init__(message)
        self._args = args


class TaskStoppedError(V2subError):
    '''
    任务取消
    '''

    def __init__(self) -> None:
        from v2subpy.utils.config import get_text
        message = get_text('info.StoppedProcess')
        super().__init__(message)


class TranscribeError(V2subError):
    '''
    转录错误
    '''


class FnNotSupport(V2subError):
    '''
    功能不支持
    '''

    def __init__(self, fn_name, fn_values=None) -> None:
        from v2subpy.utils.config import get_text
        if fn_values is not None:
            info = f"{fn_name} = {fn_values}"
        else:
            info = f"{fn_name}"
        message = get_text('exec.FunctionNotSupport').format(info)
        super().__init__(message)


class GptDomainTypeEnum(StrEnum):
    openai = 'openai'
    v2sub = 'v2sub'
    other = 'other'

class SubtitleFormatEnum(StrEnum):
    SRT = 'srt'
    VTT = 'vtt'
    ASS = 'ass'

class GptTemperatureEnum(Enum):
    high = 0.9
    medium = 0.6
    low = 0.2
    zero = 0.0

class LLMMaxcharEnum(Enum):
    LLM_MAX_CJK = 1000
    LLM_MAX_EN = 2000
    LOCAL_LLM_MAX_CJK = 500
    LOCAL_LLM_MAX_EN = 1000

class GptModelEnum(str, Enum):
    GPT_3__5_TURBO = "gpt-3.5-turbo"
    GPT_4 = 'gpt-4'
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_O1 = 'o1'
    GPT_O1_MINI = 'o1-mini'
    GPT_O3 = 'o3'
    GPT_O3_MINI = 'o3-mini'
    

    def __str__(self):
        return self.name.replace("__", ".")

class DefaultModelEnum(str, Enum):
    GEMINI_DEFAULT = "gemini-2.0-flash"
    CLAUDE_DEFAULT = "claude-3.7-sonnet"

    def __str__(self):
        return self.name.replace("__", ".")

def read_json_file(json_path: str):
    p = Path(get_file(json_path))
    if p.exists():
        content = p.read_text(encoding='utf-8')
        json_data = json.loads(content)
        return json_data

def write_json_file(json_data:dict, file_path: str):
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False)

def get_extra_config() -> dict:
    extra_cfg_file = './data/extra_config.json'
    return read_json_file(extra_cfg_file)


def get_profile() -> dict:
    profile_path = './data/profile.json'
    return read_json_file(profile_path)


def get_proxy_from_cfg():
    '''
    从配置文件读取proxy配置，
    并处理返回
    '''
    from v2subpy.utils.config import get_sys_config

    cfg = get_sys_config()
    proxy_config = cfg.get('proxy', None)
    if proxy_config and proxy_config['enabled']:
        return make_proxy(**proxy_config)
    else:
        return None


def make_proxy(protocol: str, url: str, port: str,
               username: str = '', password: str = '', **kwargs) -> None:
    if username and password:
        proxy = f"{protocol}://{username}:{password}@{url}:{port}"
    else:
        proxy = f"{protocol}://{url}:{port}"
    proxies = {
        'http': proxy,
        'https': proxy
    }
    return proxies


def is_win() -> bool:
    '''
    检查系统是否为windows
    '''
    return sys.platform == "win32"
