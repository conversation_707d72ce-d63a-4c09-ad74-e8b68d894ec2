import logging
import json
from more_itertools import chunked
from itertools import pairwise
from collections.abc import Iterator
from typing import NamedTuple
from spacy.language import Language
from spacy.tokens import Doc, Span, Token
from spacy.matcher import Matcher
from pathlib import Path
from spacy.util import load_model_from_path

from v2subpy.model.result import WhisperR<PERSON>ult
from v2subpy.utils import config
from v2subpy.utils.config import get_text
from v2subpy.utils.loadfile import get_file
from v2subpy.utils.log import logger
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = '.'):
    assert seconds >= 0, "non-negative timestamp expected"
    milliseconds = round(seconds * 1000.0)

    hours = milliseconds // 3_600_000
    milliseconds -= hours * 3_600_000

    minutes = milliseconds // 60_000
    milliseconds -= minutes * 60_000

    seconds = milliseconds // 1_000
    milliseconds -= seconds * 1_000

    hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
    return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"

def get_time_span(span: Span, timing: dict):
    start_token = span[0]
    end_token = span[-1]
    
    # Find start timing
    while start_token.is_punct or not timing.get(start_token.idx, None):
        start_token = start_token.nbor(-1)
    start_index = start_token.idx
    start, _ = timing[start_index]
    
    # For CJK languages, check character by character
    if span.doc.lang_ in ['zh', 'ja', 'ko']:
        end_token_start = end_token.idx
        end_token_text = end_token.text
        end = None
        
        # Try to find the last complete word's timing
        last_char_pos = end_token_start + len(end_token_text) - 1
        while last_char_pos >= end_token_start:
            if timing.get(last_char_pos, (None, None))[1]:
                _, end = timing[last_char_pos]
                break
            last_char_pos -= 1
            
        # If we couldn't find the end time in the last token, try the previous token
        if not end and end_token.i > 0:
            prev_token = end_token.nbor(-1)
            prev_token_end = prev_token.idx + len(prev_token.text) - 1
            _, end = timing.get(prev_token_end, (None, None))
    else:
        # Original behavior for other languages
        while end_token.is_punct or not timing.get(end_token.idx, None):
            end_token = end_token.nbor(-1)
        _, end = timing.get(end_token.idx, (None, None))
    
    if not end:
        logging.debug("Timing alignment error: %s %d", span.text, end_token.idx)
        # Fallback: try looking at previous token
        end_token = end_token.nbor(-1)
        _, end = timing.get(end_token.idx, (None, None))
        
    # If we still don't have an end time, use the start time of the next token
    if not end:
        for next_idx, (next_start, _) in sorted(timing.items()):
            if next_idx > end_token.idx:
                end = next_start
                break
    
    # Last resort: if we still don't have an end time, use start + 1 second
    if not end and start:
        end = start + 1.0
        logging.warning(f"Using fallback timing for span: {span.text}")
    
    return (start, end)

Token.set_extension("can_fragment_after", default=False)
Token.set_extension("fragment_reason", default="")
Span.set_extension("get_time_span", method=get_time_span)

# Language-specific patterns
PATTERNS = {
    'default': {
        'punct': [{'IS_PUNCT': True, 'ORTH': {"IN": [",", ":", ";"]}}],
        'conj': [{"POS": {"IN": ["CCONJ", "SCONJ"]}}],
        'clause': [{"DEP": {"IN": ["advcl", "relcl", "acl", "acl:relcl"]}}],
        'ac_comp': [{"DEP": {"IN": ["acomp", "ccomp"]}}],
        'preposition': [{'POS': 'ADP'}],
        'dobj': [{'DEP': 'dobj'}, {'IS_PUNCT': False}],
        'v_particle': [{'POS': 'VERB'}, {'POS': 'PART'}, {'POS': {"IN": ["VERB", "AUX"]}, 'OP': '!'}],
        'v_adj': [{'POS': "VERB"}, {"POS": "ADJ", "DEP": "amod"}]
    },
    'zh': {
        'punct': [{'IS_PUNCT': True, 'ORTH': {"IN": ["，", "。", "；", "：", "、"]}}],
        'conj': [{"POS": {"IN": ["CCONJ"]}, "ORTH": {"IN": ["和", "或", "而且", "但是"]}}],
        'topic_marker': [{'DEP': 'topic'}],
        'clause_end': [{'DEP': {'IN': ['punct']}, 'ORTH': {'IN': ['。', '！', '？']}}]
    },
    'ja': {
        'punct': [{'IS_PUNCT': True, 'ORTH': {"IN": ["、", "。"]}}],
        'topic_marker': [{'ORTH': {'IN': ['は', 'が', 'を', 'に']}}],
        'clause_end': [{'ORTH': {'IN': ['ます', 'です', 'た', 'ない', 'ました']}}],
        'conj': [{'POS': 'SCONJ'}]
    },
    'ko': {
        'punct': [{'IS_PUNCT': True, 'ORTH': {"IN": [".", "。", "，"]}}],
        'topic_marker': [{'ORTH': {'IN': ['는', '은', '이', '가']}}],
        'clause_end': [{'ORTH': {'REGEX': '^(습니다|니다|세요|어요|에요)$'}}],
        'conj': [{'POS': 'SCONJ'}]
    }
}

@Language.factory("fragmenter", default_config={"verbal_pauses": []})
def create_fragmenter_component(nlp: Language, name: str, verbal_pauses: list[int]):
    return FragmenterComponent(nlp, verbal_pauses)

class FragmenterComponent:
    def __init__(self, nlp: Language, verbal_pauses: list):
        self.pauses = set(verbal_pauses)
        logging.info("Count of pauses: %d", len(self.pauses))

    def __call__(self, doc: Doc) -> Doc:
        return fragmenter(doc, self.pauses)

def _fragment_at(token: Token, reason: str):
    token._.can_fragment_after = True
    token._.fragment_reason = reason

def fragmenter(doc: Doc, pauses: set) -> Doc:
    # Get language-specific patterns
    lang = doc.lang_
    patterns = PATTERNS.get(lang, PATTERNS['default'])
    
    matcher = Matcher(doc.vocab)
    for pattern_name, pattern in patterns.items():
        matcher.add(pattern_name, [pattern])

    matches = matcher(doc)
    
    # Language-specific fragmentation rules
    if lang in ['zh', 'ja', 'ko']:
        _handle_cjk_fragmentation(doc, matches, matcher)
    else:
        _handle_default_fragmentation(doc, matches)

    _scan_entities(doc)
    _scan_noun_phrases(doc)
    _scan_pauses(doc, pauses)
    return doc

def _handle_cjk_fragmentation(doc: Doc, matches: list, matcher: Matcher):
    """Route to language-specific handlers for CJK languages"""
    if doc.lang_ == 'ja':
        _handle_japanese_fragmentation(doc, matches)
    elif doc.lang_ == 'zh':
        _handle_chinese_fragmentation(doc, matches)
    elif doc.lang_ == 'ko':
        _handle_korean_fragmentation(doc, matches)

def _handle_japanese_fragmentation(doc: Doc, matches: list):
    """Specific fragmentation rules for Japanese"""
    for token in doc:
        # Fragment after sentence endings
        if token.text in [",", "。", "！", "？", "．"]:
            _fragment_at(token, reason="ja-punct")
            continue
            
        # Fragment after commas if previous token isn't a particle
        if token.text == "、" and token.i > 0:
            prev_token = token.nbor(-1)
            if not prev_token.text in ["は", "が", "を", "に"]:
                _fragment_at(token, reason="ja-comma")
            continue

        # Fragment after sentence-final expressions
        if token.text in ["ます", "です", "ました", "でした"]:
            _fragment_at(token, reason="ja-final")
            continue
            
        # Fragment after certain particles if previous chunk is long enough
        if token.text in ["は", "が", "を", "に"] and token.i > 0:
            prev_text_length = sum(len(t.text) for t in doc[max(0, token.i-5):token.i])
            if prev_text_length >= 10:
                _fragment_at(token.nbor(-1), reason="ja-particle")
            continue
            
        # Fragment after te-form + certain verbs
        if token.text.endswith("て") and token.i < len(doc) - 1:
            next_token = token.nbor(1)
            if next_token.text in ["いる", "おり", "あり", "います", "おります", "あります"]:
                _fragment_at(token, reason="ja-te-form")

def _handle_chinese_fragmentation(doc: Doc, matches: list):
    """Specific fragmentation rules for Chinese"""
    for token in doc:
        # First priority: Fragment after major sentence endings
        if token.text in [",", "。", "！", "？", "．"]:
            _fragment_at(token, reason="zh-sent")
            continue
            
        # Second priority: Fragment after commas and other pause markers
        if token.text in ["，", "、", "；", "：", "…"]:
            _fragment_at(token, reason="zh-pause")
            continue
            
        # Lower priority: Fragment after topic markers
        if token.dep_ == "topic" and token.i > 0:
            # Only fragment if not immediately following punctuation
            prior = token.nbor(-1)
            if not prior.is_punct:
                _fragment_at(token.nbor(-1), reason="zh-topic")
            continue
            
        # Lowest priority: Fragment after certain conjunctions
        if token.text in ["但是", "而且", "因为", "所以"] and token.i > 0:
            prior = token.nbor(-1)
            if not prior.is_punct:
                _fragment_at(token, reason="zh-conj")

def _handle_korean_fragmentation(doc: Doc, matches: list):
    """Specific fragmentation rules for Korean"""
    for token in doc:
        # Fragment after sentence endings
        if token.text in [",", ".", "。", "!", "?", "．"]:
            _fragment_at(token, reason="ko-punct")
            continue
            
        # Fragment after commas
        if token.text in ["，", "、"]:
            _fragment_at(token, reason="ko-comma")
            continue
            
        # Fragment after topic markers
        if token.text in ["는", "은", "이", "가"] and token.i > 0:
            prev_text_length = sum(len(t.text) for t in doc[max(0, token.i-5):token.i])
            if prev_text_length >= 10:
                _fragment_at(token.nbor(-1), reason="ko-topic")
            continue
            
        # Fragment after certain endings
        if token.text.endswith(("습니다", "니다", "세요", "어요", "에요")):
            _fragment_at(token, reason="ko-final")
            continue
            
        # Fragment after conjunctive endings
        if token.text.endswith(("고", "며", "지만")):
            _fragment_at(token, reason="ko-conj")

def _handle_default_fragmentation(doc: Doc, matches: list):
    """Handle fragmentation for English and other non-CJK languages"""
    conjunction_or_punct = frozenset(["CCONJ", "SCONJ", "PUNCT"])
    for match_id, start, end in matches:
        rule_id = doc.vocab.strings[match_id]
        matched_span = doc[start:end]
        token = doc[start]
        if token.i < 2:
            continue
        if rule_id == "punct":
            _fragment_at(token, reason=rule_id)
        elif rule_id == "conj":
            prior = token.nbor(-1)
            if prior.pos_ not in conjunction_or_punct:
                _fragment_at(prior, reason=rule_id)
        elif rule_id == "clause":
            subtree = [t for t in token.subtree]
            if len(subtree) < 2:
                continue
            clause_rule = f"{rule_id}:{token.text}"
            left = subtree[0]
            if left and left.i > 0 and not left.is_punct and left.text[0] != "'":
                prior = left.nbor(-1)
                if prior.pos_ not in conjunction_or_punct and not prior.nbor(-1).is_punct:
                    _fragment_at(prior, reason=clause_rule)
            right = subtree[-1]
            # Add boundary check
            if right.i + 1 < len(doc):
                try:
                    if right.pos_ not in conjunction_or_punct and not right.nbor(1).is_punct:
                        _fragment_at(right, reason=clause_rule)
                except IndexError:
                    continue
        elif rule_id == "preposition":
            prior = token.nbor(-1)
            if prior.pos_ in conjunction_or_punct or token.ent_iob_ == 'I':
                continue
            # Add boundary check
            if token.i + 1 < len(doc):
                try:
                    next = token.nbor(1)
                    if (token.dep_ == 'prt' or prior.pos_ in ['AUX', 'VERB']) and not next.is_punct:
                        _fragment_at(token, reason=f"{rule_id}-after")
                    else:
                        if token.i > 2 and not (prior.is_punct or prior.nbor(-1).is_punct):
                            _fragment_at(prior, reason=rule_id)
                except IndexError:
                    continue
        elif rule_id == "v_particle":
            particle = matched_span[1]
            # Add boundary check
            if particle.i + 1 < len(doc):
                try:
                    if particle.is_punct or particle.nbor(1).is_punct:
                        continue
                    _fragment_at(particle, reason=rule_id)
                except IndexError:
                    continue
        elif rule_id == "v_adj":
            _fragment_at(token, reason=rule_id)
        elif rule_id == "dobj":
            if token.pos_ not in conjunction_or_punct:
                _fragment_at(token, reason=rule_id)
        elif rule_id == "ac_comp":
            if token.is_punct:
                continue
            subtree = [t for t in token.subtree]
            left = subtree[0]
            if len(subtree) < 2:
                continue
            ac_rule = f"{rule_id}:{token.text}"
            if left and left.i > 0 and left.text[0] != "'" and not (left.is_punct or left.nbor(-1).is_punct):
                _fragment_at(left.nbor(-1), reason=ac_rule)
            right = subtree[-1]
            # Add boundary check
            if right.i + 1 < len(doc):
                try:
                    if not (right.is_punct or right.nbor(1).is_punct):
                        _fragment_at(right, reason=ac_rule)
                except IndexError:
                    continue

def _scan_pauses(doc: Doc, pauses: set):
    for token in doc:
        if token.text[0] == '-':
            continue
        try:
            if token.idx in pauses and not token.nbor(1).is_punct:
                logging.debug("Candidate pause: %d %s %s", token.i, token.text, token.nbor(1).text)
        except IndexError:
            continue

def _scan_entities(doc: Doc):
    for entity in doc.ents:
        if len(entity) < 2 or entity.label_ in ['PERSON', 'ORDINAL', 'PERCENT', 'TIME', 'CARDINAL'] or len(entity.text) < 10:
            continue
        token = entity[0]
        if token.i < 1:
            continue
        prior = token.nbor(-1)
        if (not prior.is_punct and 
            prior.pos_ not in ['DET'] and
            not prior._.can_fragment_after):
            _fragment_at(prior, reason="entity->")
        after = entity[-1].nbor(1)
        if (after.pos_ != "PART" and 
            not after.is_punct and
            not entity[-1]._.can_fragment_after):
            _fragment_at(entity[-1], reason="entity")

def _scan_noun_phrases(doc: Doc):
    if doc.lang_ == 'zh':
        _scan_chinese_phrases(doc)
    elif doc.lang_ == 'ja':
        _scan_japanese_phrases(doc)
    elif doc.lang_ == 'ko':
        _scan_korean_phrases(doc)
    else:
        # Original noun chunk logic for other languages
        for chunk in doc.noun_chunks:
            if len(chunk) < 2:
                continue
            token = chunk[0]
            if token.i > 0 and not token.is_punct:
                prior = token.nbor(-1)
                if (prior.pos_ not in ['ADP', 'SCONJ', 'CCONJ'] and
                    not prior._.can_fragment_after and
                    not prior.is_punct):
                    _fragment_at(prior, reason="NP->")
            try:
                after = chunk[-1].nbor(1)
                if (not after.is_punct and
                    not chunk[-1]._.can_fragment_after):
                    _fragment_at(chunk[-1], reason="NP")
            except IndexError:
                continue

def _scan_chinese_phrases(doc: Doc):
    for token in doc:
        if token.pos_ == 'NOUN' and token.dep_ in ['nsubj', 'dobj']:
            if token.i > 0 and not token.nbor(-1).is_punct:
                _fragment_at(token.nbor(-1), reason="CN-NP")

def _scan_japanese_phrases(doc: Doc):
    for token in doc:
        if token.pos_ == 'NOUN':
            next_token = token.nbor(1) if token.i < len(doc) - 1 else None
            if next_token and next_token.text in ['は', 'が', 'を']:
                _fragment_at(token, reason="JP-NP")

def _scan_korean_phrases(doc: Doc):
    for token in doc:
        if token.pos_ == 'NOUN':
            next_token = token.nbor(1) if token.i < len(doc) - 1 else None
            if next_token and next_token.text in ['는', '은', '이', '가']:
                _fragment_at(token, reason="KR-NP")

def leading_whitespace(s: str) -> int:
    return(len(s) - len(s.lstrip()))

def load_whisper_json(file: str) -> tuple[str, dict]:
    doc_timing = {}
    doc_text = ""
    with open(file) as js:
        jsdata = json.load(js)
        for s in jsdata['segments']:
            if 'words' not in s:
                raise ValueError('JSON input file must contain word timestamps')
            for word_timed in s['words']:
                word = word_timed['word']
                if len(doc_text) == 0:
                    word = word.lstrip()
                    start_index = 0
                doc_text += word
                start_index = len(doc_text) - len(word) + leading_whitespace(word)
                doc_timing[start_index] = (word_timed['start'], word_timed['end'])
        return doc_text.strip(), doc_timing



def scan_for_pauses(doc_text: str, timing: dict) -> list[int]:
    pauses = []
    for (k1, (_, end)), (k2, (start, _)) in pairwise(sorted(timing.items())):
        gap = start - end
        if gap > 0.3:
            pauses.append(k1)
    return pauses

def preferred_division_for(span: Span, max_width: int) -> int:
    def is_grammatically_preferred(token: Token):
        return token._.can_fragment_after and (
            token._.fragment_reason in ['punct', 'conj', 'clause', 'entity', 'entity->', 'v_adj'])
    preferreds = (t for t in reversed(span) if is_grammatically_preferred(t))
    target_width = round(0.7 * max_width)
    for tp in preferreds:
        width = tp.idx + len(tp) - span.start_char
        if width > max_width:
            continue
        remainder_width = span.end_char - tp.idx - len(tp)
        if width <= remainder_width and width >= max_width/3 and remainder_width <= max_width:
            logging.debug("Primary complete %s %s at %d : '%s'", tp.text, tp._.fragment_reason, tp.idx - span.start_char, span.text)
            return tp.i
        if width >= target_width and width <= remainder_width * 1.2:
            logging.debug("Primary selected %s %s at %d : '%s'", tp.text, tp._.fragment_reason, tp.idx - span.start_char, span.text)
            return tp.i
    logging.debug("No primary for '%s'", span.text)
    return 0

def secondary_division_for(span: Span, max_width: int) -> int:
    token_divider = 0
    start_index = span.start_char
    for token in span:
        if token.i == span[0].i:
            continue
        token_start = token.idx - start_index
        if token_divider and token_start > max_width:
            break
        token_end = token_start + len(token)
        if token._.can_fragment_after and token_end <= max_width and token.i + 2 < span[-1].i:
            token_divider = token.i
            if span.end_char - token.idx - len(token) <= max_width:
               break
        if not token_divider and token_end > max_width:
            token_divider = token.i - 1 if token.pos_ != 'PUNCT' else token.i - 2
            logging.info("Forced division after word '%s' : '%s'", span.doc[token_divider].text, span.text)
    return token_divider 

def divide_span(span: Span, args) -> Iterator[Span]:
    max_width = args.width
    if span.end_char - span.start_char <= max_width:
        yield span
        return
    divider = preferred_division_for(span, max_width) or secondary_division_for(span, max_width)
    after_divider = divider + 1
    yield span.doc[span.start:after_divider]
    if after_divider < span.end:
        yield from divide_span(span.doc[after_divider:span.end], args)

def iterate_document(doc: Doc, timing: dict, args):
    max_lines = args.lines
    for sentence in doc.sents:
        for chunk in chunked(divide_span(sentence, args), max_lines):
            subtitle = '\n'.join(line.text for line in chunk)
            sub_start, _ = chunk[0]._.get_time_span(timing)
            _, sub_end = chunk[-1]._.get_time_span(timing)
            yield sub_start, sub_end, subtitle

def write_srt(doc, timing, args, output_file, print_output=True):
    comma: str = ','
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, (start, end, text) in enumerate(iterate_document(doc, timing, args), start=1):
            ts1 = format_timestamp(start, always_include_hours=True, decimal_marker=comma)
            ts2 = format_timestamp(end, always_include_hours=True, decimal_marker=comma)
            output = f"{i}\n{ts1} --> {ts2}\n{text}\n"
            f.write(output)
            if print_output:
                print(output)

def configure_spaCy(model: str, entities: str, pauses: list = []):
    nlp = load_spacy_model(model)
    if model.startswith('xx'):
        raise NotImplementedError("spaCy multilanguage models are not currently supported")
    nlp.add_pipe("fragmenter", config={"verbal_pauses": pauses}, last=True, validate=False)
    if len(entities) > 0:
        nlp.add_pipe("entity_ruler", config={"overwrite_ents": True}).from_disk(entities)
    return nlp

def load_spacy_model(model_path):
    """Load a spaCy model from a custom path."""
    try:
        model_path = Path(model_path)
        nlp = load_model_from_path(model_path)
        return nlp
    except Exception as e:
        raise Exception(f"Error loading spaCy model from {model_path}: {str(e)}")

def update_whisper_json(doc: Doc, timing: dict, args, input_json_path: str, output_json_path: str):
    """Update the original JSON with new segmentation while maintaining the format."""
    
    def print_progress(total_time: float, progress_time: float):
        '''
        根据时间打印处理的进度
        每隔20分钟打印一次
        '''
        time_gap = 20 * 60
        if total_time <= time_gap:
            return
        # Use function attribute to store last print time
        if not hasattr(print_progress, 'last_print_time'):
            print_progress.last_print_time = 0.0
        
        progress_percent = (progress_time / total_time) * 100
        # Only print if 10 minutes (600 seconds) have passed since last print
        if progress_time - print_progress.last_print_time >= time_gap:
            logger.info(get_text('info.GeneralProgress').format(progress_percent)) 
            print_progress.last_print_time = progress_time
        elif progress_percent == 100:
            logger.info(get_text('info.GeneralProgress').format(progress_percent)) 
 
        
    # Load original JSON
    with open(input_json_path) as f:
        json_data = json.load(f)
    
    total_time = json_data['segments'][-1]['end']
    # Create new segments based on our SRT segmentation
    new_segments = []
    
    for start, end, text in iterate_document(doc, timing, args):
        # Create a new segment
        segment = {
            "id": len(new_segments),
            "start": start,
            "end": end,
            "text": text,
            "words": []
        }
        
        # Find all words that belong to this time range
        for word_idx, (word_start, word_end) in timing.items():
            if word_start is None or word_end is None:
                continue
                
            if word_start >= start and word_end <= end:
                # Find the next word boundary
                next_word_start = None
                for next_idx, _ in sorted(timing.items()):
                    if next_idx > word_idx:
                        next_word_start = next_idx
                        break
                
                # Extract word text safely
                word_end_idx = next_word_start if next_word_start else len(doc.text)
                word_text = doc.text[word_idx:word_end_idx]
                
                segment["words"].append({
                    "word": word_text,
                    "start": word_start,
                    "end": word_end
                })
        # compare segment.text and words
        all_words = "".join(item["word"] for item in segment["words"])
        if segment['text'].strip() != all_words.strip():
            logger.debug(f'mismatch found: text={segment["text"]}\n{all_words=}\n')
        # add progress info
        progress_time = segment['end']
        print_progress(total_time, progress_time)
        new_segments.append(segment)
    if total_time != progress_time: 
        print_progress(total_time, total_time)    # Update the JSON with new segments
    json_data["segments"] = new_segments
    
    # Write the updated JSON
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)


    

class SplitArgs(NamedTuple):
    width: int
    lines: int


def update_split_result(result: WhisperResult, lang_code: str, args: SplitArgs) -> WhisperResult:
    '''
    使用spacy进行句子分割，并更新result
    Args:
        result: WhisperResult
        lang_code: 语言代码
        args: 参数
    Returns:
        WhisperResult
    '''
    # Save result to temporary json file
    import tempfile
    import os
    logger.debug(f'{lang_code=}, {args=}')
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as tmp_file:
        result.save_as_json(tmp_file.name)
        input_json = tmp_file.name

    # Create temporary output json file
    output_json = input_json.rsplit('.', 1)[0] + '_updated.json'

    # Get text and timing from input json
    wtext, word_timing = load_whisper_json(input_json)

    # Configure spaCy
    model_folder = get_file(config.MODEL_PATH)
    model = str(Path(model_folder).joinpath(f'sentence/{lang_code}').resolve())
    logger.debug(f"use spacy model: {model}")
    verbal_pauses = scan_for_pauses(wtext, word_timing)
    nlp = configure_spaCy(model, "", verbal_pauses)
    doc = nlp(wtext)

    # Update json with new segments
    update_whisper_json(doc, word_timing, args, input_json, output_json)

    # Load updated json into new WhisperResult
    with open(output_json, 'r', encoding='utf-8') as f:
        updated_result = WhisperResult(json.load(f))

    # Cleanup temporary files
    os.unlink(input_json)
    os.unlink(output_json)

    return updated_result

def is_supported_lang(lang_code: str) -> bool:
    '''
    是否支持该语言
    '''
    supported_langs = ['zh', 'en', 'ja', 'ko']
    return lang_code in supported_langs