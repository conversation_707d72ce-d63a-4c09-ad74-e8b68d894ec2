'''
google gemini translator
'''
import re
import os
import time
from pathlib import Path
from typing import Callable
import google.generativeai as genai
import google.api_core as gapicore
from google.api_core.exceptions import DeadlineExceeded
from google.generativeai.types import GenerateContentResponse
from v2subpy.trans.base import BaseTranslator, BaseBot, TimeOutException, TranslationError, ChatBotException
from v2subpy.utils import config
from v2subpy.sub.subutils import handle_chinese_text
from v2subpy.utils.config import get_text
from v2subpy.utils.log import logger
from v2subpy.trans import langutil
from v2subpy.trans.gpt_prompt import prompter_map, BaseTranslatePrompter
from v2subpy.utils.version_manage import VersionManager, Fn
from v2subpy.utils.common import GptTemperatureEnum, write_file, DefaultModelEnum, LLMMaxcharEnum


class GeminiTranslator(BaseTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_gemini()

    def setup_translator_config(self):
        if langutil.is_cjk_lang(self.source_lang):
            self.translator_config["max_char"] = LLMMaxcharEnum.LLM_MAX_CJK.value
        else:  
            self.translator_config["max_char"] = LLMMaxcharEnum.LLM_MAX_EN.value

    def setup_gemini(self):
        self.api_key = self.translator_config.get('api_key')
        default_model = DefaultModelEnum.GEMINI_DEFAULT.value
        default_temperature = GptTemperatureEnum.medium.value
        self.model = self.translator_config.get(
            'custom_model') or default_model
        self.temperature = self.translator_config.get(
            'gpt_temperature') or default_temperature
        if self.proxies:
            os.environ['ALL_PROXY'] = self.proxies['http']

        logger.debug(f'setup gemini: {self.api_key}, {self.model}')
        self.translator = GeminiBot(api_key=self.api_key, model=self.model, temperature=self.temperature,
                                    retry=2, translator_config=self.translator_config)

    def finished(self, output_file):
        if VersionManager.check_fn(Fn.GPT_SUMMARY):
            if self.translator_config.get(Fn.GPT_SUMMARY):
                pf = Path(output_file)
                pf = pf.with_stem(f'{pf.stem}_summary')
                pf = pf.with_suffix('.txt')
                self.write_summary(str(pf))

    def write_summary(self, filepath):
        summaries = self.remove_duplicates_ordered(self.translator.summaries)
        text_summary = '\n'.join(summaries)
        logger.debug(f'summary: \n {text_summary}')
        if self.target_lang == 'zh':
            text_summary = handle_chinese_text(text_summary)
        write_file(filepath, text_summary)
        logger.info(config.get_text("info.GeminiSummary").format(filepath))

    def _translate(self, text: str, source_lang: str, target_lang: str):
        source_lang = langutil.get_lang_longname(source_lang)
        target_lang = langutil.get_lang_longname(target_lang)
        txt_list = text.strip().split('\n')
        try:
            trans_list = self.translator.translate(
                txt_list, src_lang=source_lang, target_lang=target_lang)

            logger.debug(
                f'lines , before: {len(txt_list)}, after: {len(trans_list)}')
            translation = '\n'.join(trans_list)
            return translation
        except ChatBotException as e:
            raise TimeOutException(
                config.get_text("error.TranslationError").format(str(e)))


class GeminiBot(BaseBot):
    def __init__(self, api_key, model='gemini-1.0-pro', temperature=0.0, retry=1, translator_config=None):
        self.model = model
        self.temperature = temperature
        self.retry = retry
        self.prompter = 'base_trans'
        self.chunk_size = 30
        self.translator_config = translator_config
        genai.configure(api_key=api_key, transport='rest')
        self.summaries = []
        self.scenes = []
        self.token_count = 0
        self.safety_settings = {
            "HARM_CATEGORY_HARASSMENT": "BLOCK_NONE",
            "HARM_CATEGORY_HATE_SPEECH": "BLOCK_NONE",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_NONE",
            "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_NONE",
        }
        self.request_options = {'timeout': 120, 'retry': None}

    def translate(self,  texts, src_lang, target_lang):
        prompter: BaseTranslatePrompter = prompter_map[self.prompter](
            src_lang, target_lang)
        chunks = self.make_chunks(texts, chunk_size=self.chunk_size)
        logger.debug(
            f'Translating : {len(chunks)} chunks, {len(texts)} lines in total.')
        # Start chunk-by-chunk translation
        translations = []
        # should be self.summaries, now it's useless , always empty
        summaries = []
        summary, scene = '', ''
        for i, chunk in enumerate(chunks, start=1):
            user_input = prompter.format_texts(chunk)
            user_instrctions = self.translator_config.get('gpt_instructions', '')
            system_prompt = prompter.system()
            user_prompt = prompter.user(
                i, user_input, user_instrctions, summaries, scene)

            content = self._send_messages(system_prompt, user_prompt,
                                          output_checker=prompter.check_format)
            summary, scene, translated = self.parse_responses(content)
            translations.extend(translated)
            self.summaries.append(summary or scene)
            self.scenes.append(scene)
            logger.debug(f'Translating : {i}/{len(chunks)}')
            logger.debug(f'summary: {summary}')
            logger.debug(f'scene: {scene}')

        return translations

    def _send_messages(self, system_prompt: str, user_prompt: str, output_checker: Callable = lambda *args, **kw: True):
        """
        Make a request to the Gemini API to provide a translation
        """
        gcr: GenerateContentResponse = None
        error = ''
        completion = f'{system_prompt}\n\n{user_prompt}'
        # logger.debug(complete_prompt)
        for retry in range(self.retry):
            try:
                gemini_model = genai.GenerativeModel(self.model)
                config = genai.GenerationConfig(
                    candidate_count=1, temperature=self.temperature)
                # logger.debug(f'gemini config: {config}')
                gcr = gemini_model.generate_content(completion,
                                                    generation_config=config,
                                                    safety_settings=self.safety_settings,
                                                    request_options=self.request_options)
            except DeadlineExceeded:
                error = get_text('error.ChatGptTimeout')
                logger.debug(f'Timeout.')
                time.sleep(15)
            except Exception as e:
                error = get_text('error.ChatGptAPIConnect')
                logger.debug(f'exception type: {type(e)}')
                time.sleep(15)
            else:
                if not gcr:
                    error = get_text('error.ChatGptAPIConnect')
                else:
                    if gcr.prompt_feedback.block_reason:
                        error = get_text('error.ChatGptAPIConnect')

                    if not gcr.candidates:
                        error = get_text('error.ChatGptAPIConnect')

                    # Try to find a validate candidate
                    candidates = [
                        candidate for candidate in gcr.candidates if candidate.finish_reason == "STOP"] or gcr.candidates

                    candidate = candidates[0]
                    self.token_count += candidate.token_count

                    finish_reason = candidate.finish_reason
                    if finish_reason.name == "MAX_TOKENS":
                        error = "Gemini response exceeded token limit"
                    elif finish_reason.name == "SAFETY":
                        error = "Gemini response was blocked for safety reason"
                    elif finish_reason.name == "RECITATION":
                        error = "Gemini response was blocked for recitation"
                    elif finish_reason.name == "FINISH_REASON_UNSPECIFIED":
                        error = "Gemini response was incomplete"

                    response_text = "\n".join(
                        part.text for part in candidate.content.parts)

                    if not response_text:
                        error = "Gemini response was empty"

                    if not output_checker(user_prompt, response_text):
                        error = "error format"
                        logger.debug(
                            f'Invalid response format. Retry num: {retry + 1}.')
                        continue
                    else:
                        return response_text

        if error:
            raise ChatBotException(get_text(
                'error.GeminiError').format(error))
