'''
Deepl翻译器
'''
import traceback

import deepl
from deepl import DeepLException, QuotaExceededException
from v2subpy.trans.base import BaseTranslator, TimeOutException, TranslationError
from v2subpy.utils import config
from v2subpy.utils.log import logger
from v2subpy.sub.subutils import handle_yue_content

DEEPL_SUPPORTED_LANGS = ["BG", "CS", "DA", "DE", "EL", "EN", "EN", "EN", "ES", "ET", "FI", "FR", "HU", "ID", "IT",
                         "JA", "KO", "LT", "LV", "NB", "NL", "PL", "PT", "PT", "PT", "RO", "RU", "SK", "SL", "SV", "TR", "UK", "ZH"]


class DeeplTranslator(BaseTranslator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setup_deepl()

    def setup_deepl(self):
        api_key = self.translator_config.get('api_key')
        api_domain = self.translator_config.get('api_domain')
        if not api_domain:
            api_domain = None
        logger.debug(f'deel api_domain: {api_domain}')
        self.translator = deepl.Translator(api_key,
                                           server_url=api_domain)

    def is_lang_supported(self, lang_code: str) -> bool:
        '''
        检查是否支持该语言
        '''
        return lang_code.upper() in DEEPL_SUPPORTED_LANGS

    def convert_lang(self, lang_code: str) -> str:
        '''
        yue -> zh
        '''
        lang_code_d = {
            'yue': 'zh',
        }
        if lang_code in lang_code_d:
            return lang_code_d[lang_code]
        else:
            return lang_code

    def handle_lang_code(self, lang_code: str) -> str:
        '''
        某些语言代码需要变化
        '''
        lang_code_d = {
            'EN': 'EN-US',
            'PT': 'PT-PT',
        }
        if lang_code in lang_code_d:
            return lang_code_d[lang_code]
        else:
            return lang_code

    def handle_result(self, text) -> str:
        '''
        yue 处理将文字专为繁体
        '''
        if self.target_lang == 'yue':
            text = handle_yue_content(text)
        return text

    def _translate(self, text: str, source_language: str, destination_language: str):
        try:
            if not self.is_lang_supported(destination_language):
                raise TranslationError(config.get_text(
                    "error.LangNotSupported"))
            destination_language = self.handle_lang_code(
                destination_language.upper())
            if source_language == 'auto':
                source_language = None
            result = self.translator.translate_text(
                text, source_lang=source_language, target_lang=destination_language)
            text = handle_yue_content(result.text)
            # logger.debug(text)
            return text
        except QuotaExceededException as e:
            raise TranslationError(
                config.get_text('error.DeelQuotaExceededError'), need_retry=False)
        except DeepLException as e:
            if e.should_retry:
                raise TimeOutException(config.get_text(
                    "error.TranslationTimeoutError"))
            else:
                raise TranslationError(str(e))
