# 实时翻译优先级调整功能

## 概述

优先级调整功能允许用户在实时翻译过程中跳转到视频的任意时间点，系统会自动重新安排音频段的处理顺序，优先翻译目标时间点及其后续内容。这个功能特别适用于用户需要快速查看视频后半部分翻译结果的场景。

## 功能特性

### 核心功能
- **智能队列重排序**：根据目标时间点重新安排音频段处理顺序
- **线程安全**：使用锁机制确保队列操作的原子性
- **智能调度**：防止频繁跳转操作，提供流畅的用户体验
- **进度准确性**：跳转后进度计算仍然准确反映实际处理状态

### 调度策略
- **首次跳转**：立即执行
- **连续跳转**：记录最新请求，延迟执行（默认3秒间隔）
- **进行中跳转**：记录请求，当前操作完成后自动执行

## API 接口

### 基础接口

#### `adjust_translation_priority(task_id: str, target_position: float) -> bool`

调整指定任务的翻译优先级。

**参数：**
- `task_id` (str): 翻译任务ID
- `target_position` (float): 目标时间位置（秒）

**返回值：**
- `bool`: 调整是否成功

**示例：**
```python
from v2subpy.realtime.integrations import AsyncRealtimeTranslator

# 创建翻译器实例
translator = AsyncRealtimeTranslator()

# 启动翻译任务
task_id = translator.start_translation("video.mp4", config)

# 跳转到300秒位置
success = translator.adjust_translation_priority(task_id, 300.0)
if success:
    print("优先级调整成功")
else:
    print("优先级调整失败")
```

### Qt集成接口

#### `QtRealtimeTranslator.adjust_translation_priority(task_id: str, target_position: float) -> bool`

Qt版本的优先级调整接口，提供信号通知机制。

**示例：**
```python
from integration_helpers import QtRealtimeTranslator

class MyWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self)
        
    def jump_to_position(self, position):
        task_ids = self.translator.get_running_tasks()
        if task_ids:
            success = self.translator.adjust_translation_priority(task_ids[0], position)
            return success
        return False
```

## 实现原理

### 1. 音频段重排序算法

```python
# 算法逻辑
for segment in all_segments:
    if segment.end_time > target_position:
        target_and_after.append(segment)  # 优先处理
    else:
        before_target.append(segment)     # 后续处理

# 新的处理顺序
reordered_segments = target_and_after + before_target
```

**示例：**
- 原始顺序：[0-10s, 10-20s, 20-30s, ..., 290-300s, 300-310s, 310-320s, ...]
- 跳转到300s后：[300-310s, 310-320s, ..., 590-600s, 0-10s, 10-20s, ..., 290-300s]

### 2. 线程安全机制

```python
# 队列锁定流程
self._is_reordering = True          # 阻止worker获取任务
try:
    with self._queue_lock:          # 获取队列锁
        # 清空队列
        # 重新填充队列
finally:
    self._is_reordering = False     # 允许worker继续
```

### 3. 智能调度机制

```python
# 调度逻辑
current_time = time.time()
time_since_last = current_time - self.last_adjustment_time

if time_since_last >= self.adjustment_delay:
    # 立即执行
    self._execute_priority_adjustment(task_id, target_position)
else:
    # 延迟执行
    remaining_delay = self.adjustment_delay - time_since_last
    self.delay_timer.start(int(remaining_delay * 1000))
```

## 使用指南

### 基本使用

#### 1. 启动翻译任务
```python
from v2subpy.realtime.integrations import AsyncRealtimeTranslator

translator = AsyncRealtimeTranslator()
task_id = translator.start_translation("video.mp4", config)
```

#### 2. 执行优先级调整
```python
# 跳转到5分钟位置
success = translator.adjust_translation_priority(task_id, 300.0)
```

#### 3. 监听进度更新
```python
def on_progress_update(task_id, message, progress_info):
    current, total = progress_info
    percentage = (current / total) * 100 if total > 0 else 0
    print(f"进度: {current}/{total} ({percentage:.1f}%)")

translator.set_progress_callback(on_progress_update)
```

### Qt应用集成

#### 1. 创建Qt翻译器
```python
from integration_helpers import QtRealtimeTranslator

class TranslationWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self)
        self.translator.progress_updated.connect(self.on_progress_update)
        self.translator.translation_completed.connect(self.on_translation_complete)
```

#### 2. 实现跳转功能
```python
def setup_jump_controls(self):
    # 时间输入框
    self.time_input = QLineEdit()
    self.time_input.setPlaceholderText("输入跳转时间（秒）")
    
    # 跳转按钮
    self.jump_btn = QPushButton("调整优先级并跳转")
    self.jump_btn.clicked.connect(self.adjust_priority_and_jump)

def adjust_priority_and_jump(self):
    try:
        target_position = float(self.time_input.text())
        running_tasks = self.translator.get_running_tasks()
        
        if running_tasks:
            task_id = running_tasks[0]
            success = self.translator.adjust_translation_priority(task_id, target_position)
            
            if success:
                self.status_label.setText(f"已跳转到 {target_position}s")
            else:
                self.status_label.setText("跳转失败")
                
    except ValueError:
        QMessageBox.warning(self, "错误", "请输入有效的数字")
```

#### 3. 智能调度实现
```python
def __init__(self):
    super().__init__()
    # 调度管理
    self.last_adjustment_time = 0
    self.pending_adjustment = None
    self.adjustment_delay = 3.0  # 3秒间隔
    
    # 延迟执行定时器
    self.delay_timer = QTimer()
    self.delay_timer.setSingleShot(True)
    self.delay_timer.timeout.connect(self._execute_pending_adjustment)

def _schedule_priority_adjustment(self, task_id, target_position):
    current_time = time.time()
    
    # 检查是否有正在进行的调整
    if self.priority_thread and self.priority_thread.isRunning():
        self.pending_adjustment = (task_id, target_position, current_time)
        return
    
    # 检查时间间隔
    time_since_last = current_time - self.last_adjustment_time
    
    if time_since_last >= self.adjustment_delay:
        # 立即执行
        self._execute_priority_adjustment(task_id, target_position)
    else:
        # 延迟执行
        remaining_delay = self.adjustment_delay - time_since_last
        self.pending_adjustment = (task_id, target_position, current_time)
        self.delay_timer.start(int(remaining_delay * 1000))
```

## 配置选项

### 调度参数
```python
# 在Qt应用中配置
self.adjustment_delay = 3.0  # 调整间隔（秒）

# 可根据需要调整：
# - 1.0: 响应更快，但可能频繁操作
# - 5.0: 更稳定，但响应较慢
# - 3.0: 推荐的平衡值
```

### 性能参数
```python
# 音频段配置
config = {
    'segment_duration': 10.0,      # 音频段时长（秒）
    'max_concurrent_segments': 1,  # 并发处理段数
    'buffer_size': 100            # 缓冲区大小（已移除限制）
}
```

## 注意事项

### 使用限制
1. **任务状态**：只能对正在运行的任务执行优先级调整
2. **时间范围**：目标位置应在视频时长范围内
3. **并发限制**：同一时间只能有一个优先级调整操作

### 性能考虑
1. **队列锁定**：跳转时会短暂阻塞worker（通常<50ms）
2. **内存使用**：所有音频段会被缓存在内存中
3. **调度开销**：正常情况下每次获取任务增加<10μs开销

### 最佳实践
1. **合理设置间隔**：根据应用场景调整`adjustment_delay`
2. **错误处理**：始终检查调整操作的返回值
3. **用户反馈**：提供清晰的状态提示和进度显示
4. **资源清理**：应用退出时正确清理翻译器资源

## 故障排除

### 常见问题

#### 1. 调整失败
```python
# 检查任务状态
running_tasks = translator.get_running_tasks()
if not running_tasks:
    print("没有正在运行的翻译任务")

# 检查目标位置
if target_position < 0:
    print("目标位置不能为负数")
```

#### 2. 进度显示异常
- 确保使用最新的进度更新逻辑
- 检查是否正确处理跳转后的进度计算

#### 3. UI卡顿
- 确保使用QThread进行后台处理
- 检查是否正确连接信号和槽

### 调试技巧
```python
# 启用详细日志
import logging
logging.getLogger('v2subpy.realtime').setLevel(logging.DEBUG)

# 监控性能
import time
start_time = time.perf_counter()
success = translator.adjust_translation_priority(task_id, position)
end_time = time.perf_counter()
print(f"调整耗时: {(end_time - start_time)*1000:.2f}ms")
```

## 完整示例

参考 `examples/qt_integration_example.py` 获取完整的实现示例，包括：
- 智能调度机制
- 线程安全的UI更新
- 错误处理和用户反馈
- 进度监控和状态管理

该示例展示了如何在实际Qt应用中集成优先级调整功能，提供了生产就绪的代码实现。
