from abc import ABC, abstractmethod
import re
import time
import traceback
from enum import Enum
from v2subpy.utils import config
from v2subpy.utils.network import test_proxy
from v2subpy.utils.log import logger
from v2subpy.utils.common import StrEnum, V2subError, make_proxy

all_translators = {}


class TranslatorEnum(StrEnum):
    DEFAULT = "default"
    GOOGLE = "google"
    CHATGPT = "chatgpt"
    DEEPL = 'deepl'
    GEMINI = 'gemini'
    CLAUDE = 'claude'
    DEEPSEEK = 'deepseek'
    MODELBAO = 'modelbao'
    CUSTOMLLM = 'customllm'


class BaseTranslator(ABC):
    def __init__(self, source_lang: str, target_lang: str, proxy_config: dict = None, translator_config: dict = None):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.translator_config = translator_config
        self.setup_translator_config()
        if proxy_config and proxy_config['enabled']:
            self.proxies = make_proxy(**proxy_config)
        else:
            self.proxies = None
        if self.proxies:
            logger.info(config.get_text(
                "info.ProxyEnabled").format(self.proxies['http']))
            # test_proxy(self.proxies)
        # logger.debug(f'transator_config: {self.translator_config}')

    def setup_translator_config(self):
        '''
        一些翻译器自定义配置修改
        '''
        pass

    @abstractmethod
    def _translate(self, text: str, source_language: str, destination_language: str):
        ...

    def remove_duplicates_ordered(self, input_list):
        unique_list = []
        for item in input_list:
            if item not in unique_list:
                unique_list.append(item)
        return unique_list

    def convert_lang(self, lang_code: str) -> str:
        '''
        转换语言代码， 翻译器如果需要转换，应覆盖此方法
        默认直接原样返回
        '''
        return lang_code

    def translate(self, text: str, source_language: str, destination_language: str):
        retry_times = self.translator_config['retry_times']
        wait_for_request = self.translator_config['wait_for_request']
        wait_for_retry = self.translator_config['wait_for_retry']
        source_language = self.convert_lang(source_language)
        destination_language = self.convert_lang(destination_language)
        for i in range(retry_times):
            try:
                result = self._translate(
                    text, source_language, destination_language)
                return result
            except TimeOutException as e:
                logger.debug(
                    f"translate error, try {i+1} time, wait {self.translator_config['wait_for_retry']} seconds and try again")
                time.sleep(wait_for_retry)
                if i == retry_times - 1:
                    logger.debug(f'exception: {e}')
                    raise TranslationError(
                        config.get_text("error.TranslationError").format(str(e)))

        # wait between each request
        time.sleep(wait_for_request)

    def finished(self, output_file):
        '''
        回调函数， 翻译完成后调用
        子类可以用来做些翻译结束后的事
        '''
        pass


class TranslationError(V2subError):
    """Translation error"""

    def __init__(self, message, need_retry=True) -> None:
        super().__init__(message,  need_retry=need_retry)


class ChatBotException(TranslationError):
    """
    Raised when chatbot fails to generate response.
    """
    pass


class TimeOutException(TranslationError):
    """Translation timed out"""


class BaseBot(ABC):

    @abstractmethod
    def translate(self,  texts, src_lang, target_lang):
        ...

    def parse_responses(self, content):
        """
        Parse response from OpenAI API.
        :return: summary, scene, translations
        """
        # logger.debug(f'response content\n\n: {content}')
        try:
            # Extract summary tag
            summary = re.search(r'<summary>(.*)</summary>', content)
            scene = re.search(r'<scene>(.*)</scene>', content)

            summary = summary.group(1) if summary else ''
            scene = scene.group(1) if scene else ''

            translation = re.findall(
                r'Translation>\n*(.*?)(?:#\d+|<summary>|\n*$)', content, re.DOTALL)

            # Remove "</summary>\nxxx</scene>" tags (or some wierd tags like </p> ) from translation
            if any([re.search(r'(<.*?>|</.*?>)', t) for t in translation]):
                logger.debug(
                    f'The extracted translation from response contains tags , tags removed')
                translation = [
                    re.sub(
                        r'(</summary>|<summary>|</scene>|<scene>|</translation>|<translation>|</p>|</div>).*', '', t, flags=re.DOTALL)
                    for t in translation]

            return summary.strip(), scene.strip(), [t.strip() for t in translation]

        except Exception as e:
            logger.debug(
                f'Failed to extract contents from response: {content}')
            raise e

    @staticmethod
    def make_chunks(texts, chunk_size=30):
        """
        Split the subtitle into chunks, each chunk has a line number of chunk_size.
        :return: List of chunks, each chunk is a list of (line_number, text) tuples
        """

        chunks = []
        start = 1
        for i in range(0, len(texts), chunk_size):
            chunk = [(start + j, text)
                     for j, text in enumerate(texts[i:i + chunk_size])]
            start += len(chunk)
            chunks.append(chunk)

        # if the last chunk is too small, merge it to the previous chunk
        if len(chunks) >= 2 and len(chunks[-1]) < chunk_size / 2:
            chunks[-2].extend(chunks[-1])
            chunks.pop()

        return chunks


def init_all_translators():
    from .default import GoogleTranslator
    from .default import MicrosoftTranslator
    from .chatgpt import ChatgptTranslator
    from .deepl import DeeplTranslator
    from .gemini import GeminiTranslator
    from .claude import ClaudeTranslator
    from .llm import DeepseekTranslator, ModelbaoTranslator, CustomLLMTranslator

    all_translators[TranslatorEnum.DEFAULT.value] = MicrosoftTranslator
    all_translators[TranslatorEnum.GOOGLE.value] = GoogleTranslator
    all_translators[TranslatorEnum.CHATGPT.value] = ChatgptTranslator
    all_translators[TranslatorEnum.DEEPL.value] = DeeplTranslator
    all_translators[TranslatorEnum.GEMINI.value] = GeminiTranslator
    all_translators[TranslatorEnum.CLAUDE.value] = ClaudeTranslator
    all_translators[TranslatorEnum.DEEPSEEK.value] = DeepseekTranslator
    all_translators[TranslatorEnum.MODELBAO.value] = ModelbaoTranslator
    all_translators[TranslatorEnum.CUSTOMLLM.value] = CustomLLMTranslator