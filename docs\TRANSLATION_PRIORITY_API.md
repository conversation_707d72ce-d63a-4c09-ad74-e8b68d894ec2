# 翻译队列优先级调整API接口文档

## 概述

当用户在播放器中跳转视频位置时，前端需要通知后端调整翻译任务队列的优先级，以确保目标位置及其后续片段能够优先翻译，从而减少用户等待时间。

## 接口定义

### 方法名称
```python
def adjust_translation_priority(self, task_id: str, target_position: float) -> None:
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| task_id | str | 是 | 翻译任务的唯一标识符 |
| target_position | float | 是 | 目标播放位置（秒），用户跳转到的时间点 |

### 功能描述

1. **接收跳转通知**：当用户通过进度条跳转到视频的某个位置时，前端会调用此接口
2. **队列重排序**：后端应根据目标位置重新安排翻译任务队列的优先级
3. **优先处理**：将目标位置及其后续的音频片段提前到队列前端进行翻译

### 实现逻辑建议

#### 1. 时间片段映射
```python
# 根据target_position计算对应的音频片段
segment_duration = 8.0  # 音频片段长度（秒）
target_segment_index = int(target_position // segment_duration)
```

#### 2. 队列优先级调整
```python
# 伪代码示例
def adjust_translation_priority(self, task_id: str, target_position: float):
    # 1. 获取当前任务的翻译队列
    task_queue = self.get_task_queue(task_id)
    
    # 2. 计算目标片段索引
    target_segment_index = int(target_position // self.segment_duration)
    
    # 3. 找出需要优先处理的片段（目标位置及其后续片段）
    priority_segments = []
    for segment in task_queue:
        if segment.index >= target_segment_index:
            priority_segments.append(segment)
    
    # 4. 重新排序队列：优先片段 + 其他片段
    remaining_segments = [s for s in task_queue if s.index < target_segment_index]
    new_queue = priority_segments + remaining_segments
    
    # 5. 更新任务队列
    self.update_task_queue(task_id, new_queue)
    
    # 6. 如果有正在处理的任务不在优先列表中，考虑中断并重新调度
    self.reschedule_current_tasks(task_id, priority_segments)
```

#### 3. 状态管理
- 记录当前的优先级调整状态
- 避免频繁调整导致的性能问题
- 提供调整结果的反馈机制

### 调用时机

#### 1. 用户主动跳转
- 用户通过进度条点击或拖拽跳转视频位置
- 用户使用快捷键跳转（如方向键）
- 用户点击特定时间点

#### 2. 自动跳转场景
- 播放过程中遇到字幕未准备好的情况
- 系统自动跳转到有字幕的位置

### 前端集成示例

```python
# 在播放器窗口的seek方法中
def seek(self, position: float):
    """跳转到指定位置"""
    print(f"[DEBUG] 请求跳转到位置: {position:.2f}s")
    
    # 检查目标位置的字幕是否准备好
    if self._check_subtitle_ready_for_position(position):
        # 字幕已准备好，直接跳转
        self.mpv_widget.seek(position)
        self.is_waiting_for_subtitle = False
        self.subtitle_renderer.set_waiting_mode(False)
    else:
        # 字幕未准备好，暂停播放并等待
        self.pending_seek_position = position
        self.is_waiting_for_subtitle = True
        self.mpv_widget.pause()
        self.subtitle_renderer.set_waiting_mode(True, "字幕准备中...")
    
    # 通知后端调整翻译队列优先级
    self._notify_backend_seek_position(position)

def _notify_backend_seek_position(self, position: float):
    """通知后端调整翻译队列优先级"""
    try:
        if self.current_task_id and hasattr(self.translation_worker, 'adjust_translation_priority'):
            self.translation_worker.adjust_translation_priority(self.current_task_id, position)
    except Exception as e:
        print(f"[DEBUG] 通知后端调整优先级失败: {e}")
```

### 错误处理

#### 1. 参数验证
- 验证task_id是否存在且有效
- 验证target_position是否在合理范围内（0 <= position <= video_duration）

#### 2. 异常情况
- 任务不存在或已完成
- 目标位置超出视频范围
- 队列调整过程中的并发问题

#### 3. 降级策略
- 如果优先级调整失败，不应影响正常的翻译流程
- 提供日志记录以便调试

### 性能考虑

#### 1. 防抖机制
```python
# 避免用户快速连续跳转导致的频繁调整
last_adjust_time = 0
min_adjust_interval = 1.0  # 最小调整间隔（秒）

def adjust_translation_priority(self, task_id: str, target_position: float):
    current_time = time.time()
    if current_time - self.last_adjust_time < self.min_adjust_interval:
        return  # 跳过过于频繁的调整
    
    self.last_adjust_time = current_time
    # 执行实际的优先级调整逻辑
```

#### 2. 批量处理
- 如果短时间内有多个调整请求，可以批量处理
- 只保留最新的目标位置进行调整

### 测试建议

#### 1. 功能测试
- 测试不同位置的跳转是否能正确调整队列
- 测试连续跳转的处理情况
- 测试边界条件（视频开头、结尾）

#### 2. 性能测试
- 测试大量片段情况下的调整性能
- 测试并发调整的稳定性
- 测试内存使用情况

#### 3. 集成测试
- 测试与前端播放器的完整交互流程
- 测试异常情况下的降级处理
- 测试用户体验的改善效果

## 总结

这个API接口的核心目标是提升用户体验，通过智能的队列调整减少用户等待字幕的时间。实现时需要平衡功能完整性、性能效率和系统稳定性。
