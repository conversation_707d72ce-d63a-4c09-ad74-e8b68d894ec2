"""
V2subpy异步任务管理器

提供线程安全的任务创建、管理和控制功能。
"""

import threading
import time
from concurrent.futures import Future
from typing import Dict, Optional, Callable, Any
import logging

from .models import TranslationTask, TranslationConfig, TaskStatus, generate_task_id
from .callbacks import TranslationCallbacks, FunctionCallbacks, CallbackManager
from ..utils.threading_utils import ThreadSafeDict, global_thread_pool
from ..utils.error_handling import (
    TaskNotFoundError, 
    TaskAlreadyRunningError, 
    RealtimeProcessingError,
    format_error,
    global_error_recovery
)

logger = logging.getLogger(__name__)


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 4):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: ThreadSafeDict[TranslationTask] = ThreadSafeDict()
        self.callback_manager = CallbackManager()
        self._lock = threading.RLock()
        self._running_count = 0
    
    def create_task(self, video_path: str, config: Optional[Dict[str, Any]] = None) -> str:
        """
        创建翻译任务
        
        Args:
            video_path: 视频文件路径
            config: 翻译配置字典
        
        Returns:
            任务ID
        
        Raises:
            ConfigurationError: 配置错误
        """
        try:
            # 生成唯一任务ID
            task_id = generate_task_id()
            
            # 创建配置对象
            if config is None:
                config = {}
            
            translation_config = TranslationConfig.from_dict(config)
            
            # 创建任务对象
            task = TranslationTask(task_id, video_path, translation_config)
            
            # 存储任务
            self.tasks.set(task_id, task)
            
            logger.info(f"Created task {task_id} for video: {video_path}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            raise RealtimeProcessingError(f"Failed to create task: {e}")
    
    def start_task(self, task_id: str, callbacks: Optional[Dict[str, Callable]] = None) -> bool:
        """
        启动翻译任务
        
        Args:
            task_id: 任务ID
            callbacks: 回调函数字典
        
        Returns:
            是否启动成功
        
        Raises:
            TaskNotFoundError: 任务不存在
            TaskAlreadyRunningError: 任务已在运行
        """
        with self._lock:
            # 检查并发任务数量限制
            if self._running_count >= self.max_concurrent_tasks:
                raise RealtimeProcessingError(
                    f"Maximum concurrent tasks ({self.max_concurrent_tasks}) reached"
                )
            
            # 获取任务
            task = self.tasks.get(task_id)
            if not task:
                raise TaskNotFoundError(f"Task {task_id} not found")
            
            # 检查任务状态
            if task.is_running():
                raise TaskAlreadyRunningError(f"Task {task_id} is already running")
            
            if task.is_finished():
                raise RealtimeProcessingError(f"Task {task_id} has already finished")
            
            try:
                # 创建回调对象
                if callbacks:
                    callback_obj = FunctionCallbacks(**callbacks)
                else:
                    callback_obj = FunctionCallbacks()
                
                # 注册回调
                self.callback_manager.register_callbacks(task_id, callback_obj)
                task.set_callbacks(callbacks or {})
                
                # 在线程池中异步执行任务
                future = global_thread_pool.submit(self._run_task, task)
                task.set_future(future)
                
                # 更新任务状态
                task.update_status(TaskStatus.RUNNING)
                self._running_count += 1
                
                logger.info(f"Started task {task_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to start task {task_id}: {e}")
                task.update_status(TaskStatus.ERROR, str(e))
                self.callback_manager.call_error(task_id, format_error(e, task_id))
                return False
    
    def stop_task(self, task_id: str) -> bool:
        """
        停止翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否停止成功
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task {task_id} not found for stopping")
            return False
        
        try:
            # 请求停止任务
            task.request_stop()
            task.update_status(TaskStatus.CANCELLED)
            
            # 取消Future（如果可能）
            if task.future and not task.future.done():
                task.future.cancel()
            
            logger.info(f"Stopped task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop task {task_id}: {e}")
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """
        暂停翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否暂停成功
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task {task_id} not found for pausing")
            return False
        
        if not task.is_running():
            logger.warning(f"Task {task_id} is not running, cannot pause")
            return False
        
        try:
            task.request_pause()
            task.update_status(TaskStatus.PAUSED)
            logger.info(f"Paused task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause task {task_id}: {e}")
            return False
    
    def resume_task(self, task_id: str) -> bool:
        """
        恢复翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否恢复成功
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task {task_id} not found for resuming")
            return False
        
        if not task.is_paused():
            logger.warning(f"Task {task_id} is not paused, cannot resume")
            return False
        
        try:
            task.request_resume()
            task.update_status(TaskStatus.RUNNING)
            logger.info(f"Resumed task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume task {task_id}: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            任务状态字典，如果任务不存在则返回None
        """
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        return task.get_status()
    
    def list_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有任务
        
        Returns:
            任务ID到状态字典的映射
        """
        result = {}
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task:
                result[task_id] = task.get_status()
        return result
    
    def cleanup_finished_tasks(self):
        """清理已完成的任务"""
        finished_task_ids = []
        
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task and task.is_finished():
                finished_task_ids.append(task_id)
        
        for task_id in finished_task_ids:
            self._cleanup_task_resources(task_id)
            logger.debug(f"Cleaned up finished task {task_id}")
    
    def _cleanup_task_resources(self, task_id: str):
        """
        清理单个任务的资源
        
        Args:
            task_id: 任务ID
        """
        task = self.tasks.pop(task_id)
        if task:
            # 清理处理管道
            if task.pipeline:
                try:
                    task.pipeline.cleanup()
                except Exception as e:
                    logger.warning(f"Error cleaning up pipeline for task {task_id}: {e}")
            
            # 取消Future
            if task.future and not task.future.done():
                task.future.cancel()
            
            # 清理回调
            self.callback_manager.unregister_callbacks(task_id)
            
            # 重置错误恢复状态
            global_error_recovery.reset_retries(task_id)
    
    def get_running_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有运行中的任务
        
        Returns:
            运行中任务的状态字典
        """
        result = {}
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task and task.is_running():
                result[task_id] = task.get_status()
        return result
    
    def get_task_count_by_status(self) -> Dict[str, int]:
        """
        按状态统计任务数量
        
        Returns:
            状态到数量的映射
        """
        status_counts = {}
        
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task:
                status = task.info.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
        
        return status_counts
    
    def cancel_all_tasks(self):
        """取消所有任务"""
        task_ids = list(self.tasks.keys())
        for task_id in task_ids:
            try:
                self.stop_task(task_id)
            except Exception as e:
                logger.error(f"Error cancelling task {task_id}: {e}")
    
    def wait_for_task_completion(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒），None表示无限等待
        
        Returns:
            任务是否在超时前完成
        """
        task = self.tasks.get(task_id)
        if not task:
            return True  # 任务不存在，认为已完成
        
        start_time = time.time()
        
        while not task.is_finished():
            if timeout and (time.time() - start_time) > timeout:
                return False
            time.sleep(0.1)
        
        return True
    
    def shutdown(self, wait: bool = True):
        """
        关闭任务管理器
        
        Args:
            wait: 是否等待所有任务完成
        """
        logger.info("Shutting down task manager")
        
        # 停止所有运行中的任务
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task and task.is_running():
                self.stop_task(task_id)
        
        # 等待任务完成（如果需要）
        if wait:
            max_wait_time = 30.0  # 最多等待30秒
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                running_tasks = [
                    task_id for task_id in self.tasks.keys()
                    if self.tasks.get(task_id) and self.tasks.get(task_id).is_running()
                ]
                
                if not running_tasks:
                    break
                
                time.sleep(0.1)
        
        # 清理所有任务
        self.tasks.clear()
        global_error_recovery.cleanup()
        
        logger.info("Task manager shutdown complete")
    
    def _run_task(self, task: TranslationTask):
        """
        在后台线程中运行任务
        
        Args:
            task: 翻译任务对象
        """
        try:
            # 导入处理管道（延迟导入避免循环依赖）
            from ..processing_pipeline import RealtimeProcessingPipeline
            
            # 创建处理管道
            pipeline = RealtimeProcessingPipeline(
                task.config,  # 直接传递配置对象
                task_id=task.task_id,
                callback_manager=self.callback_manager
            )
            
            task.set_pipeline(pipeline)
            
            # 开始处理
            pipeline.start_processing(task.video_path)
            
            # 监控处理状态
            self._monitor_task_processing(task)
            
        except Exception as e:
            logger.error(f"Task {task.task_id} failed: {e}")
            task.update_status(TaskStatus.ERROR, str(e))
            self.callback_manager.call_error(task.task_id, format_error(e, task.task_id))
        finally:
            with self._lock:
                if self._running_count > 0:
                    self._running_count -= 1
    
    def _monitor_task_processing(self, task: TranslationTask):
        """
        监控任务处理状态（非阻塞）
        
        Args:
            task: 翻译任务对象
        """
        while not task.stop_requested.is_set():
            # 检查暂停状态
            if task.pause_requested.is_set():
                if task.pipeline:
                    task.pipeline.pause_processing()
                task.update_status(TaskStatus.PAUSED)
                
                # 等待恢复信号
                task.resume_requested.wait()
                
                if task.pipeline:
                    task.pipeline.resume_processing()
                task.update_status(TaskStatus.RUNNING)
                task.resume_requested.clear()
            
            # 检查处理是否完成
            if task.pipeline and task.pipeline.is_finished():
                task.update_status(TaskStatus.COMPLETED)
                self.callback_manager.call_finished(task.task_id, {"task_id": task.task_id})
                break
            
            # 短暂休眠，避免占用过多CPU
            time.sleep(0.1)

    def adjust_translation_priority(self, task_id: str, target_position: float) -> bool:
        """
        调整翻译队列优先级

        Args:
            task_id: 任务ID
            target_position: 目标播放位置（秒）

        Returns:
            bool: 是否调整成功
        """
        print(f"[DEBUG] AsyncTaskManager.adjust_translation_priority 被调用: task_id={task_id}, target_position={target_position}")
        task = self.tasks.get(task_id)
        if not task:
            print(f"[DEBUG] 任务 {task_id} 在 tasks 中未找到")
            print(f"[DEBUG] 当前 tasks 中的任务: {list(self.tasks.keys())}")
            logger.warning(f"Task {task_id} not found for priority adjustment")
            return False

        print(f"[DEBUG] 任务 {task_id} 找到，检查运行状态")
        if not task.is_running():
            print(f"[DEBUG] 任务 {task_id} 未在运行状态，当前状态: {task.info.status}")
            logger.warning(f"Task {task_id} is not running, cannot adjust priority")
            return False

        try:
            # 获取处理管道
            if not task.pipeline:
                print(f"[DEBUG] 任务 {task_id} 没有处理管道")
                logger.warning(f"Task {task_id} has no processing pipeline")
                return False

            print(f"[DEBUG] 调用处理管道的优先级调整方法")
            # 调用处理管道的优先级调整方法
            success = task.pipeline.adjust_translation_priority(target_position)
            print(f"[DEBUG] 处理管道调用结果: {success}")

            if success:
                logger.info(f"Successfully adjusted priority for task {task_id} to position {target_position}s")
            else:
                logger.warning(f"Failed to adjust priority for task {task_id}")

            return success

        except Exception as e:
            print(f"[DEBUG] AsyncTaskManager 调用异常: {e}")
            logger.error(f"Failed to adjust priority for task {task_id}: {e}")
            return False

    def get_task_count_by_status(self) -> Dict[str, int]:
        """
        获取各状态任务数量统计

        Returns:
            Dict[str, int]: 状态到数量的映射
        """
        status_counts = {}
        for task_id in self.tasks.keys():
            task = self.tasks.get(task_id)
            if task:
                status = task.info.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
        return status_counts


# 全局任务管理器实例
global_task_manager = AsyncTaskManager()