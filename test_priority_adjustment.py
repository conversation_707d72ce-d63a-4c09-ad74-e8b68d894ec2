#!/usr/bin/env python3
"""
测试优先级调整功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from v2subpy.utils.log import logger
from v2subpy.realtime.integrations.generic_integration import AsyncRealtimeTranslator

def test_priority_adjustment():
    """测试优先级调整功能"""
    
    # 创建翻译器
    translator = AsyncRealtimeTranslator(max_concurrent_tasks=1)
    
    # 配置
    config = {
        'whisper_model': 'small',
        'target_language': 'zh',
        'translation_engine': 'default',
        'segment_duration': 10.0,
        'max_concurrent_segments': 1
    }
    
    # 回调函数
    def on_subtitle_ready(task_id, subtitle_data):
        start_time = subtitle_data.get('start_time', 0)
        end_time = subtitle_data.get('end_time', 0)
        original_text = subtitle_data.get('original_text', '')
        translated_text = subtitle_data.get('translated_text', '')
        
        print(f"[{task_id[:8]}] {start_time:.1f}s - {end_time:.1f}s")
        print(f"原文: {original_text}")
        print(f"译文: {translated_text}")
        print("-" * 50)
    
    def on_progress_update(task_id, progress_data):
        message = progress_data.get('message', '')
        print(f"[进度] {message}")
    
    def on_error(task_id, error_data):
        print(f"[错误] {error_data}")
    
    def on_finished(task_id, result_data):
        print(f"[完成] 任务 {task_id} 已完成")
    
    callbacks = {
        'on_subtitle_ready': on_subtitle_ready,
        'on_progress_update': on_progress_update,
        'on_error': on_error,
        'on_finished': on_finished
    }
    
    # 启动翻译任务（使用一个测试视频文件）
    video_path = "test_video.mp4"  # 请替换为实际的视频文件路径
    
    try:
        task_id = translator.start_translation(video_path, config, callbacks)
        print(f"启动翻译任务: {task_id}")
        
        # 等待几秒让任务开始
        time.sleep(5)
        
        # 测试优先级调整
        target_position = 300.0  # 跳转到300秒
        print(f"\n{'='*50}")
        print(f"调整优先级: 跳转到 {target_position} 秒")
        print(f"{'='*50}")
        
        success = translator.adjust_translation_priority(task_id, target_position)
        print(f"优先级调整结果: {success}")
        
        # 继续等待看结果
        time.sleep(30)
        
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        # 清理
        translator.stop_all_tasks()

if __name__ == '__main__':
    test_priority_adjustment()