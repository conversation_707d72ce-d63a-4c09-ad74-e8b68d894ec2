from v2subpy.video.ff import extract_video, extract_audio
from v2subpy.utils.common import V2subError
import pytest

def test_no_sound_audio():
    file_path = r'D:\test_files\fail_files\38. ICT Live Session - June 12, 2022.mp4'
    duration = 100000
    with pytest.raises(V2subError):
        out_path = extract_audio(file_path, duration)


def test_extract_audio():
    # file_path = "d:/test_1/en1.mp4"
    file_path = r"D:\testvideo/Bunker.mp4"

    duration = 100000
    out_path = extract_audio(file_path, duration)
    print(out_path)
