from v2subpy.model.model_utils import get_dir_size,get_model_path, get_cache_dir, down_model

def test_get_dir_size():
    model = 'small'
    model_path = get_model_path(model)
    print(model_path)
    size = get_dir_size(model_path)
    print(size)

def test_get_cache_dir():
    model = 'large'
    cache_dir = get_cache_dir(model)
    print(cache_dir)

def test_down_model():
    def update_progress(status, progress_info):
        print(f'{status}')
        print(f'{progress_info}')
    model = 'small'
    down_model(model, update_progress)