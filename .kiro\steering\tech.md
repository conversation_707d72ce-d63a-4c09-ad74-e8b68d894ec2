# Technology Stack

## Core Technologies

- **Python 3.9+**: Primary development language
- **faster-whisper**: Speech recognition engine (Whisper model implementation)
- **PyQt5**: Desktop GUI framework
- **Cython**: Performance optimization and compilation
- **FFmpeg**: Video/audio processing backend
- **setuptools**: Package management and distribution

## Key Dependencies

### AI/ML Libraries
- `faster-whisper`: Speech-to-text processing
- `torch`: PyTorch for model inference
- `transformers`: Hugging Face transformers
- `openai-whisper`: Alternative Whisper implementation

### Translation Services
- `openai`: OpenAI API integration
- `deepl-translate`: DeepL translation service
- `googletrans`: Google Translate (unofficial)
- `translatepy`: Multi-service translation wrapper

### Media Processing
- `ffmpeg-python`: FFmpeg Python bindings
- Audio format conversion and extraction
- Video processing and merging

### GUI and Threading
- `PyQt5`: Desktop application framework
- `concurrent.futures`: Thread pool management
- `asyncio`: Asynchronous processing support

## Build System

### Development Build
```bash
# Install in development mode
pip install -e .

# Build Cython extensions
python setup.py build_ext --inplace
```

### Production Build
```bash
# Clean previous builds
python clear_build.bat  # or rmdir /s /q build dist v2subpy.egg-info

# Build wheel distribution
python build.bat
# Equivalent to:
# python setup.py build_ext --inplace
# python setup.py bdist_wheel
```

### Testing
```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/test_realtime_translation.py
pytest tests/test_ff.py  # FFmpeg tests
pytest tests/test_translate.py  # Translation tests
```

## Architecture Patterns

### Modular Design
- `v2subpy.model`: Whisper model management and transcription
- `v2subpy.trans`: Translation engine abstractions
- `v2subpy.video`: FFmpeg integration and media processing
- `v2subpy.sub`: Subtitle file handling and formats
- `v2subpy.realtime`: Real-time processing pipeline
- `v2subpy.utils`: Configuration, logging, and utilities

### Configuration Management
- TOML-based configuration files
- Environment-specific settings
- Runtime configuration validation
- Version-based feature management

### Error Handling
- Custom exception hierarchy
- Retry mechanisms with exponential backoff
- Graceful degradation for network failures
- Comprehensive logging system

### Threading Model
- Worker thread pattern for long-running tasks
- Qt signal/slot for UI communication
- Thread-safe resource management
- Concurrent processing for performance

## Performance Considerations

### Model Loading
- Lazy loading of Whisper models
- Model caching and reuse
- Device detection (CPU/CUDA)
- Memory-efficient processing

### Audio Processing
- Streaming audio extraction
- Segmented processing for large files
- Overlap handling for continuity
- Format optimization

### Translation Optimization
- Batch translation requests
- Connection pooling
- Request deduplication
- Local caching of results