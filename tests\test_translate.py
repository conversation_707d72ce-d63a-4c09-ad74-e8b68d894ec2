from v2subpy import v2sub
from v2subpy.utils import config
from v2subpy.utils.common import get_extra_config
from v2subpy.utils.version_manage import VersionManager, Version, Fn


def test_translator():
    ver = Version.MAX
    config.set_app_version(ver)
    v2sub.init_sys()
    # translator_name = 'deepl'
    translator_name = 'chatgpt'
    # translator_name = 'default'
    assert v2sub.test_translator(translator_name)


def test_v2sub_auto_domain():
    ver = Version.MAX
    config.set_app_version(ver)
    v2sub.init_sys()
    extra_cfg = get_extra_config()
    api_domain_list = extra_cfg['api_domain_list']
    result = []
    for api_domain in api_domain_list:
        translator_name = 'chatgpt'
        source_lang = 'zh'
        target_lang = 'en'
        test_text = config.get_text('translator.ApiTestText')
        translator = v2sub.get_translator(
            source_lang, target_lang, translator_name)
        translator.replace_openai_domain(api_domain)
        print(f"use {api_domain}")
        try:
            translated_text = translator.translate(
                test_text, source_lang, target_lang)
            translated_text = translated_text.lower()
            if 'hello' in translated_text or 'world' in translated_text:
                result.append(f"Success: {api_domain}")
        except:
            result.append(f"failure: {api_domain}")

    print(result)\


def test_translate():
    ver = Version.MAX
    config.set_app_version(ver)
    source_lang = 'zh'
    target_lang = 'en'
    # translator = 'deepl'
    translator = 'deepseek'
    # translator = 'default'
    file_path = r"D:\testvideo\test-gemini\leijun.srt"
    # file_path = "d:/test_files/testsrt/jp3.srt"
    v2sub.init_sys()
    # config.set_app_version(config.Version.PRO)
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)


def test_translate_txt():
    source_lang = 'auto'
    target_lang = 'zh'
    translator = 'chatgpt'
    file_path = "d:/test_files/testsrt/en1.srt"
    v2sub.init_sys()
    config.set_app_version(config.Version.MAX)
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)


def test_translate_vtt():
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'default'
    file_path = "d:/test_files/testvtt/en1.vtt"
    v2sub.init_sys()
    config.set_app_version(config.Version.PRO)
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)

def test_translate_ass():
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'default'
    file_path = r"D:\test_files\test_ass\en1.ass"
    v2sub.init_sys()
    config.set_app_version(config.Version.MAX)
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)


def test_empty_translate():
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'default'
    file_path = "d:/ff_video/empty.srt"
    v2sub.init_sys()
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)


def test_gpt_translate():
    ver = Version.MAX
    config.set_app_version(ver)
    v2sub.init_sys()
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'chatgpt'
    file_path = r"D:\test_files\testsrt\en1.srt"
    # file_path = r"D:\test_files\testsrt\jp3.srt"
    # file_path = r"D:\test_files\testsrt\leijun_output.srt"
    # file_path = r"D:\test_files\testsrt\iphone.srt"
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)

def test_deepseek_translate():
    ver = Version.MAX
    config.set_app_version(ver)
    v2sub.init_sys()
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'deepseek'
    file_path = r"D:\test_files\testsrt\en1.srt"
    # file_path = r"D:\test_files\testsrt\jp3.srt"
    # file_path = r"D:\test_files\testsrt\leijun_output.srt"
    # file_path = r"D:\test_files\testsrt\iphone.srt"
    v2sub.translate_file(file_path, source_lang=source_lang,
                         target_lang=target_lang, translator_name=translator)

def test_batch_translate():
    ver = Version.MAX
    config.set_app_version(ver)
    v2sub.init_sys()
    dir_path = r"D:\test_files\test_batch_srt"
    source_lang = 'en'
    target_lang = 'zh'
    translator = 'default'
    v2sub.gen_subtitles(dir_path, source_lang, target_lang, translator, 'small',
                        batch_subtitle=True)
