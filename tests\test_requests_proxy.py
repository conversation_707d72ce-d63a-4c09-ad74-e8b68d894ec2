
import requests
import os

http_proxy_url = 'http://*************:7890'
socks_proxy_url = 'socks5://*************:7891'

proxies = {
    'http': http_proxy_url,
    # 'http': socks_proxy_url,
    # 'https': socks_proxy_url,
    # 'all':  http_proxy_url,
    # 'all': socks_proxy_url
}


def test_validate():
    https_url = 'https://ifconfig.me/ip'
    http_url = 'http://api.ipify.org/?format=json'
    headers = {'User-Agent': 'curl/7.29.0'}
    https_r = requests.get(https_url, headers=headers,
                           proxies=proxies, timeout=10)
    http_r = requests.get(http_url, headers=headers,
                          proxies=proxies, timeout=10)
    print(f"当前使用代理：{proxies}")
    print(f"访问https网站使用代理：{str(https_r.content.decode())}")
    print(f"访问http网站使用代理：{http_r.json()['ip']}")


def test_request_no_system_proxy():
    proxies = {
        'http': None,
        'https': None
    }
    env = os.environ
    # print(env)
    https_url = 'https://ifconfig.me/ip'
    http_url = 'http://api.ipify.org/?format=json'
    headers = {'User-Agent': 'curl/7.29.0'}
    try:
        https_r = requests.get(https_url, headers=headers,
                               proxies=proxies, timeout=10)
        print(f"访问https网站使用代理：{str(https_r.content.decode())}")
    except Exception as e:
        print(e)

    try:
        http_r = requests.get(http_url, headers=headers, timeout=10)
        print(f"访问http网站使用代理：{http_r.json()['ip']}")
    except Exception as e:
        print(e)
