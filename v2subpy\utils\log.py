import logging
from logging.handlers import RotatingFileHandler
import sys
from os import makedirs, environ
from os.path import dirname, exists
from v2subpy.utils.loadfile import get_file
logger = None

LOG_ENABLED = True  # 是否开启日志
LOG_TO_SYSOUT = True  # 是否输出到控制台
LOG_TO_FILE = True  # 是否输出到文件
LOG_PATH = 'logs/runtime.log'  # 日志文件路径
LOG_LEVEL_SYSOUT = logging.INFO  # 日志级别
LOG_LEVEL_FILE = logging.INFO  # 日志级别
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

V2SUB_DEBUG = environ.get('V2SUB_DEBUG') or True
APP_ENVIRONMENT_DEV = V2SUB_DEBUG  # 运行环境，如测试环境还是生产环境

if APP_ENVIRONMENT_DEV:
    LOG_LEVEL_SYSOUT = logging.DEBUG
    # stdout日志输出格式
    LOG_FORMAT_SYSOUT = '%(levelname)s - %(asctime)s - %(filename)s - %(lineno)d - %(module)s \n %(message)s'
    # file日志输出格式
    LOG_FORMAT_FILE = '%(levelname)s - %(asctime)s - process: %(process)d - %(filename)s - %(lineno)d - %(module)s - %(message)s'
else:
    # stdout日志输出格式
    LOG_FORMAT_SYSOUT = '%(asctime)s - %(message)s'
    # file日志输出格式
    LOG_FORMAT_FILE = '%(levelname)s - %(asctime)s - %(message)s'


def init_logger():
    from v2subpy.utils.config import APP_NAME
    global logger
    logger = logging.getLogger(APP_NAME)
    logger.setLevel(LOG_LEVEL_SYSOUT)

    # 输出到控制台
    if LOG_ENABLED and LOG_TO_SYSOUT:
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(level=LOG_LEVEL_SYSOUT)
        formatter = logging.Formatter(LOG_FORMAT_SYSOUT, datefmt=DATE_FORMAT)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

    # 输出到文件
    if LOG_ENABLED and LOG_TO_FILE:
        # 如果路径不存在，创建日志文件文件夹
        log_dir = dirname(get_file(LOG_PATH))
        if not exists(log_dir):
            makedirs(log_dir)
        # 添加 FileHandler
        file_handler = RotatingFileHandler(
            get_file(LOG_PATH), encoding='utf-8', maxBytes=10240000)
        file_handler.setLevel(level=LOG_LEVEL_FILE)
        formatter = logging.Formatter(LOG_FORMAT_FILE, datefmt=DATE_FORMAT)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    logger.debug('logger created')


init_logger()
