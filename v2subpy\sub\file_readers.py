from datetime import timedelta
from srt import Subtitle
import srt
from pysubs2 import SSAFile
from v2subpy.sub.sub_file import SubFile


class TxtFile(SubFile):
    '''
    txt 文件处理
    '''
    EXT = 'txt'

    def parse(self, input_file) -> list[Subtitle]:
        '''
        解析txt文件并返回
        '''
        subtitles = []
        index = 0
        for line in input_file:
            content = line.strip()
            start = timedelta(0, 0, 0)
            end = start
            if content:
                index += 1
                sub = Subtitle(index, start, end, content)
                subtitles.append(sub)

        return subtitles

    def compose(self, subtitles: list[Subtitle]) -> str:
        '''
        将字幕对象转换为txt格式
        '''
        text_list = []
        for sub in subtitles:
            text_list.append(sub.content)

        text = '\n\n'.join(text_list)
        return text


class SrtFile(SubFile):
    '''
    srt 文件处理
    '''
    EXT = 'srt'

    def parse(self, input_file) -> list[Subtitle]:
        '''
        解析srt文件并返回
        '''
        srt_file = srt.parse(input_file)
        subtitles = list(srt_file)
        subtitles = list(srt.sort_and_reindex(subtitles))
        return subtitles

    def compose(self, subtitles: list[Subtitle]) -> str:
        '''
        将字幕对象转换为srt格式
        方便写入文件
        '''
        text = srt.compose(subtitles)
        return text


class VttFile(SubFile):
    '''
    vtt 文件处理
    '''
    EXT = 'vtt'

    def parse(self, input_file) -> list[Subtitle]:
        '''
        解析srt文件并返回
        '''
        vtt_file = SSAFile.from_file(input_file)
        self.vtt_file = vtt_file
        subs = list(vtt_file)
        for index, sub in enumerate(subs, start=1):
            sub.content = sub.plaintext
            sub.index = index
        return subs

    def compose(self, subtitles: list[Subtitle]) -> str:
        '''
        将字幕对象转换为srt格式
        方便写入文件
        '''
        for sub, vtt_sub in zip(subtitles, self.vtt_file):
            vtt_sub.text = sub.content

        text = self.vtt_file.to_string(format_=self.EXT)
        return text

class AssFile(SubFile):
    '''
    ass 文件处理
    '''
    EXT = 'ass'
    
    def parse(self, input_file) -> list[Subtitle]:
        '''
        解析ass文件并返回
        '''
        ass_file = SSAFile.from_file(input_file)
        self.ass_file = ass_file
        subs = list(ass_file)
        for index, sub in enumerate(subs, start=1):
            sub.content = sub.plaintext
            sub.index = index
        return subs
    
    def compose(self, subtitles: list[Subtitle]) -> str:
        '''
        将字幕对象转换为ass格式
        方便写入文件
        '''
        for sub, ass_sub in zip(subtitles, self.ass_file):
            ass_sub.text = sub.content

        text = self.ass_file.to_string(format_=self.EXT)
        return text