"""
实时字幕处理的异常类型定义

定义了各种处理过程中可能出现的异常类型，便于错误处理和调试。
"""

import json
from typing import Optional


class RealtimeProcessingError(Exception):
    """实时处理基础异常"""
    
    def __init__(self, message: str, segment_id: Optional[str] = None, 
                 can_retry: bool = True, error_code: Optional[str] = None):
        super().__init__(message)
        self.segment_id = segment_id
        self.can_retry = can_retry
        self.error_code = error_code
        
    def to_dict(self) -> dict:
        """转换为字典格式用于回调传输"""
        return {
            'error_type': self.__class__.__name__,
            'error_message': str(self),
            'segment_id': self.segment_id,
            'can_retry': self.can_retry,
            'error_code': self.error_code
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)


class AudioExtractionError(RealtimeProcessingError):
    """音频提取异常"""
    
    def __init__(self, message: str, video_path: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.video_path = video_path
        
    def to_dict(self) -> dict:
        data = super().to_dict()
        data['video_path'] = self.video_path
        return data


class SpeechRecognitionError(RealtimeProcessingError):
    """语音识别异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.model_name = model_name
        
    def to_dict(self) -> dict:
        data = super().to_dict()
        data['model_name'] = self.model_name
        return data


class TranslationError(RealtimeProcessingError):
    """翻译异常"""
    
    def __init__(self, message: str, translator_type: Optional[str] = None, 
                 source_text: Optional[str] = None, retry_count: int = 0, **kwargs):
        super().__init__(message, **kwargs)
        self.translator_type = translator_type
        self.source_text = source_text
        self.retry_count = retry_count
        
    def to_dict(self) -> dict:
        data = super().to_dict()
        data.update({
            'translator_type': self.translator_type,
            'source_text': self.source_text,
            'retry_count': self.retry_count
        })
        return data


class BufferUnderrunError(RealtimeProcessingError):
    """缓冲区不足异常"""
    
    def __init__(self, message: str, buffer_level: int = 0, **kwargs):
        super().__init__(message, **kwargs)
        self.buffer_level = buffer_level
        
    def to_dict(self) -> dict:
        data = super().to_dict()
        data['buffer_level'] = self.buffer_level
        return data


class ConfigurationError(RealtimeProcessingError):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, can_retry=False, **kwargs)
        self.config_key = config_key
        
    def to_dict(self) -> dict:
        data = super().to_dict()
        data['config_key'] = self.config_key
        return data