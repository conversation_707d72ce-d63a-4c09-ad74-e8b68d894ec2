#!/usr/bin/env python3
"""
最小化Qt集成测试

测试基本的Qt集成功能，不使用复杂的定时器机制。
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
    from PySide6.QtCore import Qt
    QT_VERSION = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
        from PyQt6.QtCore import Qt
        QT_VERSION = "PyQt6"
    except ImportError:
        try:
            from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
            from PyQt5.QtCore import Qt
            QT_VERSION = "PyQt5"
        except ImportError:
            print("请安装PySide6、PyQt6或PyQt5")
            sys.exit(1)

# 直接使用异步翻译器，不使用Qt包装器
try:
    from v2subpy.realtime.integrations import AsyncRealtimeTranslator
    print("✓ 成功导入AsyncRealtimeTranslator")
except ImportError as e:
    print(f"✗ 导入AsyncRealtimeTranslator失败: {e}")
    sys.exit(1)


class MinimalTestWindow(QMainWindow):
    """最小化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.translator = AsyncRealtimeTranslator(max_concurrent_tasks=1)
        self.current_task_id = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"最小化Qt集成测试 ({QT_VERSION})")
        self.setGeometry(100, 100, 500, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_btn = QPushButton("开始测试")
        self.test_btn.clicked.connect(self.start_test)
        layout.addWidget(self.test_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
    
    def start_test(self):
        """开始测试"""
        self.status_label.setText("启动测试...")
        self.result_text.append("开始测试异步翻译器")
        
        # 创建回调函数
        def on_subtitle_ready(task_id, subtitle_data):
            self.result_text.append(f"[{task_id[:8]}] 字幕: {subtitle_data}")
        
        def on_progress_update(task_id, progress_data):
            message = progress_data.get('message', '')
            self.status_label.setText(f"[{task_id[:8]}] {message}")
        
        def on_error(task_id, error_data):
            error_msg = error_data.get('message', error_data.get('error', 'Unknown error'))
            self.status_label.setText(f"错误: {error_msg}")
            self.result_text.append(f"[{task_id[:8]}] 错误: {error_msg}")
            self.test_btn.setEnabled(True)
        
        def on_finished(task_id, result_data):
            self.status_label.setText("测试完成")
            self.result_text.append(f"[{task_id[:8]}] 测试完成")
            self.test_btn.setEnabled(True)
        
        callbacks = {
            'on_subtitle_ready': on_subtitle_ready,
            'on_progress_update': on_progress_update,
            'on_error': on_error,
            'on_finished': on_finished
        }
        
        # 启动测试任务
        config = {
            'whisper_model': 'small',
            'target_language': 'zh',
            'translation_engine': 'default'
        }
        
        try:
            self.current_task_id = self.translator.start_translation(
                "d:/testvideo/en1.mp4", config, callbacks
            )
            
            if self.current_task_id:
                self.status_label.setText(f"启动任务: {self.current_task_id}")
                self.result_text.append(f"启动任务: {self.current_task_id}")
                self.test_btn.setEnabled(False)
            else:
                self.status_label.setText("启动任务失败")
                self.result_text.append("启动任务失败")
                
        except Exception as e:
            self.status_label.setText(f"异常: {e}")
            self.result_text.append(f"启动异常: {e}")
            import traceback
            self.result_text.append(traceback.format_exc())
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.current_task_id:
            self.translator.stop_translation(self.current_task_id)
        
        self.translator.stop_all_tasks()
        event.accept()


def main():
    """主函数"""
    print(f"使用Qt版本: {QT_VERSION}")
    
    app = QApplication(sys.argv)
    app.setApplicationName("最小化Qt集成测试")
    
    window = MinimalTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()