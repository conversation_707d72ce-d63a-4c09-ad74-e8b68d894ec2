from v2subpy.video import tts
from v2subpy.video import ff
from v2subpy.utils import config
from v2subpy.utils import common


def test_make_audio():
    config.read_lang_config()
    # srt_path = r"D:\test_files\test_whole_process\en1.srt"
    srt_path = r"D:\test_files\test_250328\tts\iphone_zh.srt"
    audio_path = tts.make_audio(srt_path, audio_lang='zh')
    print(audio_path)

def test_env():
    import os
    print(os.environ)

def test_make_audio_txt_testrun():
    config.read_lang_config()
    srt_path = r"D:\test_files\test_tts\en1\ko.txt"
    test_run = 0
    audio_path = tts.make_audio(srt_path, test_run=test_run)
    print(audio_path)


def test_make_audio_testrun():
    config.read_lang_config()
    srt_path = r"D:\test_files\test_tts\en1\en1_zh.srt"
    voice_name = "zh-CN-YunxiNeural"
    test_run = 5
    audio_path = tts.make_audio(srt_path, test_run=test_run)
    print(audio_path)


def test_merge_video():
    config.read_lang_config()
    video_path = r"D:\test_files\test_tts\en1\en1.mp4"
    srt_path = r"D:\test_files\test_tts\en1\en1_zh.srt"
    audio_path = r"D:\test_files\test_tts\en1\en1_zh_output.mp3"
    out_path = r"D:\test_files\test_tts\en1\en1_output.mp4"
    ff.merge_audio_with_video(video_path, audio_path, srt_path)
    print(f'output path: {out_path}')


def test_merge_video2():
    config.read_lang_config()
    video_path = r"D:\test_files\test_tts\en1\en1.mp4"
    srt_path = r"D:\test_files\test_tts\en1\en1_zh.srt"
    audio_path = r"D:\test_files\test_tts\en1\en1_zh.mp3"
    test_run = 0
    out_path = ff.merge_video(video_path, audio_path, srt_path,
                              test_run=test_run, update_status=None)
    print(f'output path: {out_path}')


def test_get_voice_names_dict():
    config.read_lang_config()
    voicenames = config.get_voice_names_dict()
    print(voicenames)


def test_make_font_style():
    srt_path = r"D:\test_files\test_tts\en1\en1_zh.srt"
    profile = common.get_profile()
    font_profile = profile.get('video_config')
    style = ff.make_font_style(srt_path, font_profile)
    print(style)

def test_read_ass_as_srt():
    srt_path = r"D:\test_files\test_tts_fix\gen\en1.ass"
    srt_data = tts.get_srt_subtitles(srt_path)
    print(srt_data)

    
def test_gen_silence():
    out_file = r'D:\test_files\test_250328\silence0.wav'
    duration = 0.2
    tts.silence_gen(out_file, duration)
    
def test_trim_audio_file():
    input_file =  r'D:\test_files\test_250328\tts\silence_74.mp3'
    before_duration = tts.get_duration(input_file)
    tts.remove_silence(input_file)
    after_duration = tts.get_duration(input_file)
    print('before_duration', before_duration, 'trimed duration: ', after_duration)

def test_silence_gen():
    out_file = r'D:\test_files\test_250328\silence.mp3'
    duration = 0.15
    tts.silence_gen(out_file, duration)
