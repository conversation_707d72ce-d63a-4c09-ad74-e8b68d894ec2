{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import toml"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["s = \"\"\"\n", "info.TranscribeComplete = \"\\nTranscribe done, time is {:.2f} seconds\"\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["t = toml.loads(s)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\\\nTranscribe done, time is {:.2f} seconds\n", "\\\\nTranscribe done, time is 123.12 seconds\n"]}], "source": ["print(t['info']['TranscribeComplete'])\n", "print(t['info']['TranscribeComplete'].format(123.123))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["with open('1.toml', 'w') as f:\n", "    toml.dump({'A': '\\nabc'}, f)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["with open('1.toml') as f:\n", "    t = toml.load(f)\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'A': '\\nabc {:.2f}', 'info': {'ProcessComplete': '\\n\\nProcess done, total time is {:.2f} seconds', 'TranscribeComplete': '\\nTranscribe done, time is {:.2f} seconds', 'TranslateComplete': '\\nTranslation done, time is {:.2f} seconds'}}\n"]}], "source": ["print(t)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "abc {:.2f}\n"]}], "source": ["print(t['A'])"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Translation done, time is 123.12 seconds\n"]}], "source": ["print(t['info']['TranslateComplete'].format(123.123))"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Process done, total time is 123.12 seconds\n"]}], "source": ["print(t['info']['ProcessComplete'].format(123.123))"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\site-packages\\tqdm\\auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\site-packages\\requests\\__init__.py:109: RequestsDependencyWarning: urllib3 (1.26.13) or chardet (None)/charset_normalizer (3.1.0) doesn't match a supported version!\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["DEBUG - 2023-03-23 10:42:05,284 - log.py - 51 - log \n", " logger created\n"]}], "source": ["from v2subpy import v2sub"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["v2sub.init_sys()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["from v2subpy.utils import config"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["key = 'info.ProcessComplete'\n", "fmt = config.get_text(key)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\\\n\\\\n全部处理完成, 共花费时间 {:.2f} 秒\n"]}], "source": ["print(fmt)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["with open('data/lang.toml') as f:\n", "    t1 = toml.load(f)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'title': 'language config file for v2sub', 'chinese': {'model': {'small': '小型', 'medium': '中型', 'large': '大型'}, 'lang': {'en': '英语', 'zh': '中文', 'ja': '日语', 'fr': '法语', 'ko': '韩语', 'de': '德语', 'es': '西班牙语', 'ru': '俄语', 'pt': '葡萄牙语', 'it': '意大利语', 'tr': '土耳其语', 'ar': '阿拉伯语', 'cs': '捷克语', 'th': '泰语'}, 'error': {'TranslationError': '翻译超时错误', 'TranslationErrorRetry': '翻译错误, 重试第 {} 次, 等待 {} 秒后重试', 'ModelLoadError': '模型加载错误, 请确认有足够显存'}, 'info': {'LoadModel': '模型加载中: {}', 'SaveSubFile': '转录字幕文件已保存: {}', 'ReadSubFile': '读取字幕文件: {}', 'TranslateProgress': '... 翻译进度 {} %', 'TranslateSaveSubFile': '翻译字幕文件已保存: {}', 'ProxyEnabled': '已启用代理: {}', 'ProcessFile': '开始处理文件: {}', 'ProcessComplete': '\\n全部处理完成, 共花费时间 {:.2f} 秒', 'TranscribeComplete': '\\n转录字幕完成, 共花费时间 {:.2f} 秒', 'TranslateComplete': '\\n翻译字幕完成, 共花费时间 {:.2f} 秒'}}, 'english': {'model': {'small': 'small', 'medium': 'medium', 'large': 'large'}, 'error': {'TranslationError': 'translate error', 'TranslationErrorRetry': 'translate error, try {} time, wait {} seconds and try again', 'ModelLoadError': 'model load error, please confirm you have enough video memory'}, 'info': {'LoadModel': 'loading model: {}', 'SaveSubFile': 'Transcribed subtitle file saved: {}', 'ReadSubFile': 'read subtitle: {}', 'TranslateProgress': '... Translating progress {} %', 'TranslateSaveSubFile': 'Translated subtitle file saved: {}', 'ProxyEnabled': 'Proxy Enabled: {}', 'ProcessFile': 'Start Process File: {}', 'ProcessComplete': '\\n\\nProcess done, total time is {:.2f} seconds', 'TranscribeComplete': '\\nTranscribe done, time is {:.2f} seconds', 'TranslateComplete': '\\nTranslation done, time is {:.2f} seconds'}}}\n"]}], "source": ["print(t1)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "全部处理完成, 共花费时间 {:.2f} 秒\n"]}], "source": ["lang = t1['chinese']\n", "print(lang['info']['ProcessComplete'])"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["from v2subpy import version"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.1.0'"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["version.__version__"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["import ffmpeg"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["video_path = \"d:/testvideo/jp2.mp4\"\n", "probe = ffmpeg.probe(video_path)\n", "video_info = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)\n", "video_length = video_info['duration']"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/plain": ["32.15722221666667"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["float(video_length)/60"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/plain": ["1926"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["32 * 60 + 6"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1929.433333'"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["video_length"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["## test ffmpeg support extension\n", "\n", "ext_file = 'ffmpeg_extensions.txt'\n", "ext_support = \"\"\"\n", ".wav\n", ".wma\n", ".mpa\n", ".mp2\n", ".m1a\n", ".m2a\n", ".mp3\n", ".ogg\n", ".m4a\n", ".aac\n", ".mka\n", ".ra\n", ".flac\n", ".ape\n", ".mpc\n", ".mod\n", ".ac3\n", ".eac3\n", ".dts\n", ".dtshd\n", ".wv\n", ".tak\n", ".cda\n", ".dsf\n", ".tta\n", ".aiff\n", ".aif\n", ".opus\n", ".amr\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(ext_file) as f:\n", "    all_ext = [f\".{ext.strip()}\" for ext in f.readlines()]\n", "    for ext in ext_support.strip().splitlines():\n", "        print(ext)\n", "        if ext not in all_ext:\n", "            print(f\"{ext} not support\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["ext_support = \"\"\"\n", ".wmv\n", ".avi\n", ".asf\n", ".mpeg\n", ".mpg\n", ".ts\n", ".mp4\n", ".3gp\n", ".mkv\n", ".rm\n", ".rmvb\n", ".webm\n", ".f4v\n", ".divx\n", ".vob\n", ".mov\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(ext_file) as f:\n", "    all_ext = [f\".{ext.strip()}\" for ext in f.readlines()]\n", "    for ext in ext_support.strip().splitlines():\n", "        print(ext)\n", "        if ext not in all_ext:\n", "            print(f\"{ext} not support\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['.wav', '.wma', '.mpa', '.mp2', '.mp3', '.ogg', '.m4a', '.aac', '.mka', '.ra', '.flac', '.ape', '.mpc', '.mod', '.ac3', '.eac3', '.dts', '.dtshd', '.wv', '.tak', '.tta', '.aiff', '.aif', '.opus', '.amr']\n"]}], "source": ["audio_formats = \"\"\"\n", ".wav\n", ".wma\n", ".mpa\n", ".mp2\n", ".mp3\n", ".ogg\n", ".m4a\n", ".aac\n", ".mka\n", ".ra\n", ".flac\n", ".ape\n", ".mpc\n", ".mod\n", ".ac3\n", ".eac3\n", ".dts\n", ".dtshd\n", ".wv\n", ".tak\n", ".tta\n", ".aiff\n", ".aif\n", ".opus\n", ".amr\n", "\"\"\"\n", "\n", "audio_list = []\n", "for ext in audio_formats.splitlines():\n", "    if ext.strip():\n", "        audio_list.append(ext.strip())\n", "print(audio_list)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('aa', 'bb')\n"]}], "source": ["t = ('aa',)\n", "\n", "print(t)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["我干什么不干你事。\n", "人體內存在很多微生物\n"]}], "source": ["import zhconv\n", "print(zhconv.convert(u'我幹什麼不干你事。', 'zh-cn'))\n", "print(zhconv.convert(u'人体内存在很多微生物', 'zh-tw'))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["'以下是繁體中文句子'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["zhconv.convert('以下是繁体中文句子', 'zh-tw')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\scoop\\apps\\miniconda3\\current\\envs\\torchtest\\lib\\site-packages\\tqdm\\auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["尽管受到这些指责马英九在一次演讲中处长与中国大陆建立更加紧密的关系并轻松获胜\n"]}], "source": ["import whisper\n", "\n", "model = whisper.load_model(\"small\")\n", "file = 'd:/testaudio/audio.wav'\n", "result = model.transcribe(file)\n", "print(result[\"text\"])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'zh'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["result['language']"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[00:00.000 --> 00:04.800] 尽管受到这些指责 马英九在一次演讲中处长与中国大陆\n", "[00:04.800 --> 00:07.400] 建立更加紧密的关系并轻松获胜\n"]}], "source": ["prompt='以下是普通话的句子'\n", "result = model.transcribe(file, task='transcribe',language='zh',verbose=True,initial_prompt=prompt)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[00:00.000 --> 00:04.800] 儘管受到這些指責馬英九在一次演講中處長與中國大陸\n", "[00:04.800 --> 00:07.400] 獻利更加緊密的關係並趨順獲勝\n"]}], "source": ["prompt='以下是繁體中文句子'\n", "result = model.transcribe(file, task='transcribe',language='zh',verbose=True,initial_prompt=prompt)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "filepath = 'en1_output_zh.srt'\n", "pf_merge = Path(filepath)\n", "merge_file = pf_merge.with_stem(\n", "pf_merge.stem.rsplit('_', 1)[0] + '_' + 'merge')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['en1_output', 'zh']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pf_merge.stem.rsplit('_', 1)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'en1_output_merge.srt'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["str(merge_file)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["toml_string = \"\"\"\n", "... # This is a TOML document.\n", "...\n", "... title = \"TOML Example\"\n", "...\n", "... [owner]\n", "... name = \"<PERSON>\"\n", "... dob = 1979-05-27T07:32:00-08:00 # First class dates\n", "...\n", "... [database]\n", "... server = \"***********\"\n", "... ports = [ 8001, 8001, 8002 ]\n", "... connection_max = 5000\n", "... enabled = true\n", "...\n", "... [servers]\n", "...\n", "...   # Indentation (tabs and/or spaces) is allowed but not required\n", "...   [servers.alpha]\n", "...   ip = \"********\"\n", "...   dc = \"eqdc10\"\n", "...\n", "...   [servers.beta]\n", "...   ip = \"********\"\n", "...   dc = \"eqdc10\"\n", "...\n", "... [clients]\n", "... data = [ [\"gamma\", \"delta\"], [1, 2] ]\n", "...\n", "... # Line breaks are OK when inside arrays\n", "... hosts = [\n", "...   \"alpha\",\n", "...   \"omega\"\n", "... ]\n", "... \"\"\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["parsed_toml = toml.loads(toml_string)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'title': 'TOML Example',\n", " 'owner': {'name': '<PERSON>',\n", "  'dob': datetime.datetime(1979, 5, 27, 7, 32, tzinfo=<toml.tz.TomlTz object at 0x0000022C57BD0370>)},\n", " 'database': {'server': '***********',\n", "  'ports': [8001, 8001, 8002],\n", "  'connection_max': 5000,\n", "  'enabled': True},\n", " 'servers': {'alpha': {'ip': '********', 'dc': 'eqdc10'},\n", "  'beta': {'ip': '********', 'dc': 'eqdc10'}},\n", " 'clients': {'data': [['gamma', 'delta'], [1, 2]],\n", "  'hosts': ['alpha', 'omega']}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_toml"]}], "metadata": {"kernelspec": {"display_name": "torchtest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}