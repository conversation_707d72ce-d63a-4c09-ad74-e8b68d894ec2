"""
重试管理器

提供指数退避重试机制，用于处理临时性错误。
"""

import time
import random
from typing import Callable, Any, Optional
from v2subpy.utils.log import logger


class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        初始化重试管理器
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
        logger.debug(f"RetryManager 初始化: max_retries={max_retries}, "
                    f"base_delay={base_delay}s, backoff_factor={backoff_factor}")
    
    def retry_with_backoff(self, func: Callable, *args, **kwargs) -> Any:
        """
        使用指数退避策略重试函数
        
        Args:
            func: 要重试的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
            
        Raises:
            Exception: 重试次数用尽后抛出最后一次的异常
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = func(*args, **kwargs)
                
                # 如果不是第一次尝试，记录成功信息
                if attempt > 0:
                    logger.info(f"重试成功: 第 {attempt + 1} 次尝试")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # 如果是最后一次尝试，不再重试
                if attempt == self.max_retries:
                    logger.error(f"重试失败，已达到最大重试次数 {self.max_retries}: {e}")
                    break
                
                # 计算延迟时间
                delay = self._calculate_delay(attempt)
                
                logger.warning(f"第 {attempt + 1} 次尝试失败: {e}, "
                             f"{delay:.2f}s 后重试")
                
                # 等待后重试
                time.sleep(delay)
        
        # 抛出最后一次的异常
        raise last_exception
    
    def retry_with_condition(self, func: Callable, condition_func: Callable[[Exception], bool],
                           *args, **kwargs) -> Any:
        """
        根据条件重试函数
        
        Args:
            func: 要重试的函数
            condition_func: 判断是否应该重试的函数，接收异常作为参数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                last_exception = e
                
                # 检查是否应该重试
                if not condition_func(e):
                    logger.debug(f"异常不满足重试条件: {e}")
                    break
                
                # 如果是最后一次尝试，不再重试
                if attempt == self.max_retries:
                    break
                
                delay = self._calculate_delay(attempt)
                logger.warning(f"条件重试: 第 {attempt + 1} 次尝试失败: {e}, "
                             f"{delay:.2f}s 后重试")
                time.sleep(delay)
        
        raise last_exception
    
    def _calculate_delay(self, attempt: int) -> float:
        """
        计算延迟时间
        
        Args:
            attempt: 当前尝试次数（从0开始）
            
        Returns:
            float: 延迟时间（秒）
        """
        # 指数退避 + 随机抖动
        delay = self.base_delay * (self.backoff_factor ** attempt)
        
        # 添加随机抖动（±25%）
        jitter = delay * 0.25 * (2 * random.random() - 1)
        delay += jitter
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        return max(0.1, delay)  # 最小延迟0.1秒
    
    def should_retry_network_error(self, exception: Exception) -> bool:
        """
        判断网络错误是否应该重试
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否应该重试
        """
        # 网络相关的异常通常可以重试
        network_errors = [
            'ConnectionError',
            'TimeoutError', 
            'HTTPError',
            'RequestException',
            'URLError'
        ]
        
        exception_name = type(exception).__name__
        return any(error in exception_name for error in network_errors)
    
    def should_retry_translation_error(self, exception: Exception) -> bool:
        """
        判断翻译错误是否应该重试
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否应该重试
        """
        # 某些翻译错误可以重试
        retryable_errors = [
            'rate limit',
            'quota exceeded',
            'service unavailable',
            'timeout',
            'temporary'
        ]
        
        error_message = str(exception).lower()
        return any(error in error_message for error in retryable_errors)
    
    def should_retry_speech_recognition_error(self, exception: Exception) -> bool:
        """
        判断语音识别错误是否应该重试
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否应该重试
        """
        # 某些语音识别错误可以重试
        retryable_errors = [
            'model not loaded',
            'cuda out of memory',
            'temporary failure'
        ]
        
        error_message = str(exception).lower()
        return any(error in error_message for error in retryable_errors)


class CircuitBreaker:
    """熔断器，防止连续失败时继续重试"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间（秒）
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
        logger.debug(f"CircuitBreaker 初始化: threshold={failure_threshold}, "
                    f"timeout={recovery_timeout}s")
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
            
        Raises:
            Exception: 熔断器开启时抛出异常，或函数执行异常
        """
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
                logger.info("熔断器进入半开状态")
            else:
                raise Exception("熔断器开启，拒绝请求")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
            
        except Exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置熔断器"""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _on_success(self) -> None:
        """成功时的处理"""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            logger.info("熔断器重置为关闭状态")
        
        self.failure_count = 0
    
    def _on_failure(self) -> None:
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"熔断器开启，连续失败 {self.failure_count} 次")
    
    def get_state(self) -> dict:
        """获取熔断器状态"""
        return {
            'state': self.state,
            'failure_count': self.failure_count,
            'last_failure_time': self.last_failure_time
        }