"""
v2subpy 实时字幕处理模块

该模块提供实时字幕翻译功能，支持视频播放时的实时音频提取、语音识别和翻译。
主要功能包括：
- 实时音频分段处理
- 语音识别（基于 faster-whisper）
- 文本翻译（支持多种翻译引擎）
- 回调函数机制用于与前端通信

主要接口：
- realtime_trans: 统一的实时翻译入口函数
"""

from .core import realtime_trans, start_realtime_translation
from .models import AudioSegment, SubtitleSegment, ProcessingStatus, RealtimeConfig
from .exceptions import (
    RealtimeProcessingError,
    AudioExtractionError, 
    SpeechRecognitionError,
    TranslationError,
    BufferUnderrunError,
    ConfigurationError
)
from .processing_pipeline import pipeline_manager

__all__ = [
    'realtime_trans',
    'start_realtime_translation',
    'AudioSegment',
    'SubtitleSegment', 
    'ProcessingStatus',
    'RealtimeConfig',
    'RealtimeProcessingError',
    'AudioExtractionError',
    'SpeechRecognitionError', 
    'TranslationError',
    'BufferUnderrunError',
    'ConfigurationError',
    'pipeline_manager'
]