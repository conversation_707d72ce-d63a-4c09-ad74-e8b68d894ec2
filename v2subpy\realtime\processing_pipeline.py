"""
实时处理管道

协调音频分段、语音识别、翻译等处理流程的核心组件。
"""

import time
import threading
from typing import Dict, List, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, Empty
import json

from v2subpy.utils.log import logger
from .models import AudioSegment, SubtitleSegment, ProcessingStatus, RealtimeConfig
from .audio_generator import AudioSegmentGenerator
from .recognition_optimizer import OptimizedSpeechRecognizer
from .translation_optimizer import TranslationOptimizer
from .exceptions import RealtimeProcessingError, AudioExtractionError
from .retry_manager import RetryManager


class RealtimeProcessingPipeline:
    """实时处理管道类"""
    
    def __init__(self, realtime_config, task_id: str = None, callback_manager=None):
        """
        初始化实时处理管道
        
        Args:
            realtime_config: 实时处理配置（字典或RealtimeConfig对象）
            task_id: 任务ID
            callback_manager: 回调管理器
        """
        # 兼容新旧配置格式
        if isinstance(realtime_config, dict):
            try:
                from .async_core.models import TranslationConfig
                self.config = TranslationConfig.from_dict(realtime_config)
            except ImportError:
                # 如果无法导入新配置，创建一个简单的配置对象
                self.config = SimpleConfig(realtime_config)
        else:
            self.config = realtime_config
        
        self.task_id = task_id
        self.callback_manager = callback_manager
        
        # 为了向后兼容，保留callbacks属性
        self.callbacks = {}
        
        # 初始化组件（兼容新配置格式）
        config_dict = self.config.to_dict() if hasattr(self.config, 'to_dict') else self.config
        
        # 初始化实例属性
        self.is_running = False
        self.is_paused = False
        self.stop_flag = False
        self.processing_thread: Optional[threading.Thread] = None
        self.result_thread: Optional[threading.Thread] = None
        
        max_workers = 1 #config_dict.get('max_concurrent_segments', 2)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 处理统计
        try:
            from .models import ProcessingStatus
            self.processing_stats = ProcessingStatus()
        except ImportError:
            # 如果无法导入，创建简单的统计对象
            self.processing_stats = SimpleProcessingStats()
        
        # 结果队列
        self.result_queue = Queue()
        
        # 性能指标
        self.performance_metrics = {
            'start_time': 0.0,
            'segments_processed': 0,
            'segments_failed': 0,
            'average_processing_time': 0.0,
            'total_processing_time': 0.0
        }
        
        # 尝试初始化真实组件，失败则使用模拟组件
        self._init_components(config_dict)
        
        logger.info("实时处理管道初始化完成")
    
    def _init_components(self, config_dict):
        """初始化处理组件"""
        try:
            # 尝试使用真实组件
            from .models import RealtimeConfig
            
            if isinstance(config_dict, dict):
                # 添加缺失的默认配置
                default_config = {
                    'segment_duration': 10.0,
                    'segment_overlap': 1.0,
                    'buffer_size': 10,
                    'max_retries': 3,
                    'retry_delay': 1.0,
                    'max_concurrent_segments': 2,
                    'whisper_model': 'small',
                    'target_language': 'zh',
                    'translation_engine': 'default'
                }
                default_config.update(config_dict)
                
                # 映射新配置字段到旧配置字段
                if 'translation_engine' in default_config:
                    default_config['translator_type'] = default_config['translation_engine']
                
                realtime_config = RealtimeConfig.from_dict(default_config)
            else:
                realtime_config = config_dict
            
            # 尝试初始化真实组件
            self.audio_generator = AudioSegmentGenerator(realtime_config)
            self.speech_recognizer = OptimizedSpeechRecognizer(realtime_config)
            self.translation_optimizer = TranslationOptimizer(realtime_config)
            self.retry_manager = RetryManager(
                max_retries=getattr(realtime_config, 'max_retries', 3),
                base_delay=getattr(realtime_config, 'retry_delay', 1.0)
            )
            
            # 记录翻译引擎配置
            engine_name = getattr(realtime_config, 'translation_engine', 'default')
            logger.info(f"真实翻译优化器初始化，使用引擎: {engine_name}")
            
            logger.info("真实组件初始化成功")
            
        except Exception as e:
            # 如果真实组件初始化失败，使用模拟组件
            logger.warning(f"真实组件初始化失败，使用模拟组件: {e}")
            self._init_mock_components()
    
    def _init_mock_components(self):
        """初始化模拟组件"""
        config_dict = self.config.to_dict() if hasattr(self.config, 'to_dict') else self.config
        
        self.audio_generator = MockAudioGenerator()
        self.speech_recognizer = MockSpeechRecognizer()
        self.translation_optimizer = MockTranslationOptimizer(config_dict)
        self.retry_manager = MockRetryManager()
        logger.info(f"模拟组件初始化完成，翻译引擎: {config_dict.get('translation_engine', 'default')}")
        
        # 处理状态
        self.is_running = False
        self.is_paused = False
        self.stop_flag = False
        self.processing_thread: Optional[threading.Thread] = None
        max_workers = config_dict.get('max_concurrent_segments', 2)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 处理统计
        try:
            from .models import ProcessingStatus
            self.processing_stats = ProcessingStatus()
        except ImportError:
            # 如果无法导入，创建简单的统计对象
            self.processing_stats = SimpleProcessingStats()
        self.performance_metrics = {
            'start_time': 0.0,
            'segments_processed': 0,
            'segments_failed': 0,
            'average_processing_time': 0.0,
            'total_processing_time': 0.0
        }
        
        # 结果队列
        self.result_queue = Queue()

        logger.info("实时处理管道初始化完成")

    def adjust_translation_priority(self, target_position: float) -> bool:
        """
        调整翻译队列优先级

        当用户跳转到视频的某个位置时，重新安排音频段的处理优先级，
        使目标位置及其后续片段能够优先翻译。

        Args:
            target_position: 目标播放位置（秒）

        Returns:
            bool: 是否调整成功
        """
        print(f"[DEBUG] RealtimeProcessingPipeline.adjust_translation_priority 被调用: target_position={target_position}")
        try:
            if not hasattr(self, 'is_running') or not self.is_running:
                print(f"[DEBUG] 处理管道未运行，is_running={getattr(self, 'is_running', False)}")
                logger.warning("处理管道未运行，无法调整优先级")
                return False

            if not hasattr(self, 'audio_generator'):
                print(f"[DEBUG] 处理管道没有音频生成器")
                logger.warning("处理管道没有音频生成器，无法调整优先级")
                return False

            print(f"[DEBUG] 调用音频生成器的优先级调整方法")
            # 调用音频生成器的优先级调整方法
            if hasattr(self.audio_generator, 'adjust_priority'):
                success = self.audio_generator.adjust_priority(target_position)
                print(f"[DEBUG] 音频生成器调用结果: {success}")
            else:
                print(f"[DEBUG] 音频生成器没有 adjust_priority 方法")
                logger.warning("音频生成器不支持优先级调整")
                return False

            if success:
                logger.info(f"成功调整音频段处理优先级到位置 {target_position}s")

                # 重置进度跟踪（重要：避免跳转后进度计算错误）
                if hasattr(self, '_processed_segments'):
                    print(f"[DEBUG] 重置进度跟踪，之前已处理 {len(self._processed_segments)} 个段")
                    # 不完全清空，保留已处理的段，但重新计算进度
                    # self._processed_segments.clear()  # 可选：完全重置

                # 通知进度更新
                if hasattr(self, 'callback_manager') and self.callback_manager:
                    # 获取当前进度信息
                    current_processed = len(getattr(self, '_processed_segments', set()))
                    total_segments = getattr(self.processing_stats, 'total_segments', 0)

                    progress_data = {
                        'message': f'已调整优先级到 {target_position}s',
                        'progress_info': (current_processed, total_segments),  # 使用实际进度
                        'priority_adjusted': True,
                        'target_position': target_position
                    }
                    if hasattr(self, 'task_id'):
                        self.callback_manager.call_progress_update(self.task_id, progress_data)
            else:
                logger.warning(f"调整音频段处理优先级失败")

            return success

        except Exception as e:
            print(f"[DEBUG] RealtimeProcessingPipeline 调用异常: {e}")
            logger.error(f"调整翻译优先级时发生异常: {e}")
            return False


class MockAudioGenerator:
    """模拟音频生成器"""
    def __init__(self):
        self.is_generating = False
        self.segment_count = 0
        self.max_segments = 3  # 生成3个测试段
    
    def start_generation(self, video_path: str):
        self.is_generating = True
        self.segment_count = 0
        logger.info(f"模拟音频生成器启动: {video_path}")
    
    def get_next_segment(self, timeout=2.0):
        import time
        time.sleep(0.5)  # 模拟处理时间
        
        if self.segment_count >= self.max_segments:
            self.is_generating = False
            return None  # 模拟没有更多段
        
        # 创建模拟音频段
        segment = MockAudioSegment(
            segment_id=f"mock_segment_{self.segment_count}",
            start_time=self.segment_count * 10.0,
            end_time=(self.segment_count + 1) * 10.0
        )
        
        self.segment_count += 1
        return segment
    
    def stop_generation(self):
        self.is_generating = False
    
    def cleanup(self):
        pass


class MockAudioSegment:
    """模拟音频段"""
    def __init__(self, segment_id: str, start_time: float, end_time: float):
        self.segment_id = segment_id
        self.start_time = start_time
        self.end_time = end_time


class MockSpeechRecognizer:
    """模拟语音识别器"""
    def preload_model(self):
        logger.info("模拟语音识别器预加载")
    
    def transcribe_segment_optimized(self, audio_segment):
        import time
        time.sleep(0.2)  # 模拟识别时间
        
        # 返回模拟结果
        class MockResult:
            def __init__(self, segment_id):
                self.segment_id = segment_id
                self.original_text = f"这是第{segment_id}段的模拟识别文本"
                self.status = "completed"
                self.start_time = getattr(audio_segment, 'start_time', 0)
                self.end_time = getattr(audio_segment, 'end_time', 0)
            
            def to_dict(self):
                return {
                    'segment_id': self.segment_id,
                    'original_text': self.original_text,
                    'status': self.status,
                    'start_time': self.start_time,
                    'end_time': self.end_time
                }
        
        segment_id = getattr(audio_segment, 'segment_id', 'unknown')
        return MockResult(segment_id)
    
    def cleanup(self):
        pass


class MockTranslationOptimizer:
    """模拟翻译优化器"""
    def __init__(self, config_dict=None):
        self.config = config_dict or {}
        self.engine_name = self.config.get('translation_engine', 'default')
        self.engine = MockTranslationEngine(self.engine_name)
        logger.info(f"模拟翻译优化器初始化，使用引擎: {self.engine_name}")
    
    def translate_optimized(self, subtitle_segment):
        import time
        time.sleep(0.1)  # 模拟翻译时间
        
        original_text = getattr(subtitle_segment, 'original_text', '')
        if original_text:
            if self.engine_name == 'default':
                # 默认引擎不翻译，直接返回原文
                subtitle_segment.translated_text = original_text
            else:
                subtitle_segment.translated_text = f"[{self.engine_name}翻译] {original_text}"
        else:
            subtitle_segment.translated_text = f"模拟{self.engine_name}翻译文本"
        
        return subtitle_segment
    
    def cleanup(self):
        pass


class MockTranslationEngine:
    """模拟翻译引擎"""
    def __init__(self, engine_name='default'):
        self.engine_name = engine_name
        logger.info(f"模拟翻译引擎初始化: {engine_name}")
    
    def test_translation(self):
        return True


class MockRetryManager:
    """模拟重试管理器"""
    def __init__(self):
        pass


class SimpleConfig:
    """简单的配置类，用于兼容性"""
    def __init__(self, config_dict: dict):
        # 设置默认值
        defaults = {
            'segment_duration': 10.0,
            'segment_overlap': 1.0,
            'buffer_size': 10,
            'max_retries': 3,
            'retry_delay': 1.0,
            'max_concurrent_segments': 2,
            'whisper_model': 'small',
            'target_language': 'zh',
            'translation_engine': 'default'
        }
        
        # 合并用户配置
        defaults.update(config_dict)
        
        # 映射新配置字段到旧配置字段
        if 'translation_engine' in defaults:
            defaults['translator_type'] = defaults['translation_engine']
        
        # 设置属性
        for key, value in defaults.items():
            setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典"""
        return {key: getattr(self, key) for key in dir(self) if not key.startswith('_') and not callable(getattr(self, key))}


class SimpleProcessingStats:
    """简单的处理统计类"""
    def __init__(self):
        self.total_duration = 0.0
        self.total_segments = 0
        self.current_segment = 0
        self.processed_duration = 0.0
        self.buffer_level = 0.0
        self.processing_speed = 1.0
    
    def to_dict(self):
        """转换为字典"""
        return {
            'total_duration': self.total_duration,
            'total_segments': self.total_segments,
            'current_segment': self.current_segment,
            'processed_duration': self.processed_duration,
            'buffer_level': self.buffer_level,
            'processing_speed': self.processing_speed
        }

class PipelineManager:
    """管道管理器"""
    
    def __init__(self):
        """初始化管道管理器"""
        self.active_pipelines: Dict[str, RealtimeProcessingPipeline] = {}
        self.pipeline_lock = threading.RLock()
        
    def create_pipeline(self, pipeline_id: str, realtime_config: RealtimeConfig, 
                       callbacks: Dict[str, Callable]) -> RealtimeProcessingPipeline:
        """
        创建处理管道
        
        Args:
            pipeline_id: 管道ID
            realtime_config: 实时处理配置
            callbacks: 回调函数字典
            
        Returns:
            RealtimeProcessingPipeline: 处理管道实例
        """
        with self.pipeline_lock:
            if pipeline_id in self.active_pipelines:
                logger.warning(f"管道 {pipeline_id} 已存在，将停止旧管道")
                self.stop_pipeline(pipeline_id)
            
            pipeline = RealtimeProcessingPipeline(realtime_config, callbacks)
            self.active_pipelines[pipeline_id] = pipeline
            
            logger.info(f"创建处理管道: {pipeline_id}")
            return pipeline
    
    def get_pipeline(self, pipeline_id: str) -> Optional[RealtimeProcessingPipeline]:
        """获取处理管道"""
        with self.pipeline_lock:
            return self.active_pipelines.get(pipeline_id)
    
    def stop_pipeline(self, pipeline_id: str) -> bool:
        """
        停止处理管道
        
        Args:
            pipeline_id: 管道ID
            
        Returns:
            bool: 是否成功停止
        """
        with self.pipeline_lock:
            pipeline = self.active_pipelines.get(pipeline_id)
            if pipeline:
                pipeline.stop_processing()
                del self.active_pipelines[pipeline_id]
                logger.info(f"停止处理管道: {pipeline_id}")
                return True
            return False
    
    def stop_all_pipelines(self) -> None:
        """停止所有处理管道"""
        with self.pipeline_lock:
            pipeline_ids = list(self.active_pipelines.keys())
            for pipeline_id in pipeline_ids:
                self.stop_pipeline(pipeline_id)
            
            logger.info("所有处理管道已停止")
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """获取所有管道的统计信息"""
        with self.pipeline_lock:
            stats = {}
            for pipeline_id, pipeline in self.active_pipelines.items():
                stats[pipeline_id] = {
                    'status': pipeline.get_processing_status().to_dict(),
                    'performance': pipeline.get_performance_metrics(),
                    'is_running': pipeline.is_running,
                    'is_paused': pipeline.is_paused
                }
            return stats


# 全局管道管理器实例
pipeline_manager = PipelineManager()


# 修补RealtimeProcessingPipeline类的方法
# 由于格式化问题，这些方法被错误地移出了类定义
def _patch_pipeline_class():
    """修补RealtimeProcessingPipeline类的方法"""
    
    def start_processing(self, video_path: str) -> None:
        """启动处理流程（异步，非阻塞）"""
        if self.is_running:
            logger.warning("处理管道已在运行中")
            return
        
        try:
            logger.info(f"启动实时处理管道: {video_path}")
            
            # 重置状态
            self.is_running = True
            self.is_paused = False
            self.stop_flag = False
            self.performance_metrics['start_time'] = time.time()
            
            # 获取视频信息（安全处理）
            try:
                if hasattr(self.audio_generator, 'segmenter') and hasattr(self.audio_generator.segmenter, 'get_segment_info'):
                    segment_info = self.audio_generator.segmenter.get_segment_info(video_path)
                    self.processing_stats.total_duration = segment_info.get('total_duration', 30.0)
                    self.processing_stats.total_segments = segment_info.get('estimated_segment_count', 3)
                else:
                    # 如果没有segmenter，使用模拟值
                    self.processing_stats.total_duration = 30.0  # 模拟30秒视频
                    self.processing_stats.total_segments = getattr(self.audio_generator, 'max_segments', 3)
            except Exception as e:
                logger.warning(f"获取视频信息失败: {e}")
                self.processing_stats.total_duration = 30.0
                self.processing_stats.total_segments = 3
            
            # 预加载模型
            self._preload_models()
            
            # 启动音频生成
            self.audio_generator.start_generation(video_path)
            
            # 启动处理线程（异步）
            self.processing_thread = threading.Thread(
                target=self._processing_loop,
                args=(video_path,),
                daemon=True
            )
            self.processing_thread.start()
            
            # 启动结果分发线程（异步）
            self.result_thread = threading.Thread(
                target=self._result_dispatch_loop,
                daemon=True
            )
            self.result_thread.start()
            
            logger.info("实时处理管道启动成功")
            
        except Exception as e:
            logger.error(f"启动处理管道失败: {e}")
            self.is_running = False
            from .utils.error_handling import RealtimeProcessingError
            raise RealtimeProcessingError(f"启动处理管道失败: {e}")
    
    def stop_processing(self) -> None:
        """停止处理流程"""
        if not self.is_running:
            return
        
        logger.info("停止实时处理管道")
        
        # 设置停止标志
        self.stop_flag = True
        self.is_running = False
        
        # 停止音频生成
        if hasattr(self.audio_generator, 'stop_generation'):
            self.audio_generator.stop_generation()
        
        # 等待处理线程结束
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
        
        # 等待结果分发线程结束
        if hasattr(self, 'result_thread') and self.result_thread.is_alive():
            self.result_thread.join(timeout=5.0)
        
        # 关闭线程池
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
        
        # 清理资源
        self.cleanup()
        
        # 调用完成回调
        self._call_callback('on_finished')
        
        logger.info("实时处理管道已停止")
    
    def pause_processing(self) -> None:
        """暂停处理"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            logger.info("处理管道已暂停")
    
    def resume_processing(self) -> None:
        """恢复处理"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            logger.info("处理管道已恢复")
    
    def is_finished(self) -> bool:
        """检查处理是否完成"""
        return not self.is_running and not self.stop_flag
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理音频生成器
            if hasattr(self.audio_generator, 'cleanup'):
                self.audio_generator.cleanup()
            
            # 清理语音识别器
            if hasattr(self.speech_recognizer, 'cleanup'):
                self.speech_recognizer.cleanup()
            
            # 清理翻译优化器
            if hasattr(self.translation_optimizer, 'cleanup'):
                self.translation_optimizer.cleanup()
            
            # 清空结果队列
            if hasattr(self, 'result_queue'):
                while not self.result_queue.empty():
                    try:
                        self.result_queue.get_nowait()
                    except:
                        break
            
            logger.debug("处理管道资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")
    
    # 添加其他必要的方法
    def _preload_models(self) -> None:
        """预加载模型"""
        try:
            logger.info("预加载语音识别和翻译模型")
            
            # 预加载语音识别模型
            if hasattr(self.speech_recognizer, 'preload_model'):
                self.speech_recognizer.preload_model()
            
            # 测试翻译引擎
            if hasattr(self.translation_optimizer, 'engine') and hasattr(self.translation_optimizer.engine, 'test_translation'):
                if not self.translation_optimizer.engine.test_translation():
                    logger.warning("翻译引擎测试失败，但继续处理")
            
            logger.info("模型预加载完成")
            
        except Exception as e:
            logger.warning(f"模型预加载失败: {e}")
    
    def _call_callback(self, callback_name: str, *args) -> None:
        """安全调用回调函数"""
        try:
            # 优先使用新的回调管理器
            if self.callback_manager and self.task_id:
                if callback_name == 'on_subtitle_ready':
                    self.callback_manager.call_subtitle_ready(self.task_id, args[0] if args else {})
                elif callback_name == 'on_progress_update':
                    progress_data = {
                        'message': args[0] if len(args) > 0 else '',
                        'progress_info': args[1] if len(args) > 1 else (0, 0)
                    }
                    self.callback_manager.call_progress_update(self.task_id, progress_data)
                elif callback_name == 'on_error':
                    error_data = args[0] if args else {'error': 'Unknown error'}
                    if isinstance(error_data, str):
                        error_data = {'error': error_data}
                    self.callback_manager.call_error(self.task_id, error_data)
                elif callback_name == 'on_finished':
                    result_data = args[0] if args else {'task_id': self.task_id}
                    self.callback_manager.call_finished(self.task_id, result_data)
                return
            
            # 回退到旧的回调机制
            callback = self.callbacks.get(callback_name)
            if callback:
                logger.debug(f"调用回调函数: {callback_name}")
                callback(*args)
                logger.debug(f"回调函数 {callback_name} 执行成功")
            else:
                logger.debug(f"回调函数 {callback_name} 未定义")
                
        except Exception as e:
            logger.error(f"回调函数 {callback_name} 执行失败: {e}")
    
    # 添加缺失的处理循环方法
    def _processing_loop(self, video_path: str) -> None:
        """主处理循环（多线程音频处理）"""
        try:
            logger.info("开始处理循环")
            
            # 存储提交的任务
            pending_futures = []
            max_pending_tasks = getattr(self.executor, '_max_workers', 2) * 2  # 限制待处理任务数量
            
            while self.is_running and not self.stop_flag:
                try:
                    # 检查暂停状态
                    if self.is_paused:
                        time.sleep(0.1)
                        continue
                    
                    # 清理已完成的任务
                    pending_futures = [f for f in pending_futures if not f.done()]
                    
                    # 如果待处理任务太多，等待一些完成
                    if len(pending_futures) >= max_pending_tasks:
                        time.sleep(0.1)
                        continue
                    
                    # 获取下一个音频段
                    try:
                        audio_segment = self.audio_generator.get_next_segment(timeout=2.0)
                    except Exception as e:
                        logger.warning(f"获取音频段失败: {e}")
                        audio_segment = None
                    
                    if audio_segment is None:
                        # 检查是否还在生成音频
                        if hasattr(self.audio_generator, 'is_generating') and self.audio_generator.is_generating:
                            continue
                        else:
                            logger.info("所有音频段处理完成")
                            break
                    
                    # 提交处理任务到线程池
                    future = self.executor.submit(
                        self._process_audio_segment_safe, audio_segment
                    )
                    pending_futures.append(future)
                    
                    # 更新进度
                    self._update_progress(audio_segment)
                    
                    logger.debug(f"已提交音频段处理任务: {getattr(audio_segment, 'segment_id', 'unknown')}")
                    
                except Exception as e:
                    logger.error(f"处理循环异常: {e}")
                    self._call_callback('on_error', self._format_error(e))
                    break
            
            # 等待所有处理任务完成
            logger.info(f"等待 {len(pending_futures)} 个处理任务完成")
            completed_count = 0
            for future in pending_futures:
                try:
                    future.result(timeout=30.0)  # 30秒超时
                    completed_count += 1
                except Exception as e:
                    logger.error(f"处理任务异常: {e}")
            
            logger.info(f"处理循环结束，完成 {completed_count}/{len(pending_futures)} 个任务")
            
            # 调用完成回调
            self._call_callback('on_finished', {'task_id': self.task_id})
            
        except Exception as e:
            logger.error(f"处理循环致命错误: {e}")
            self._call_callback('on_error', self._format_error(e))
        finally:
            self.is_running = False
    
    def _process_audio_segment_safe(self, audio_segment) -> None:
        """安全的音频段处理包装器"""
        try:
            self._process_audio_segment(audio_segment)
        except Exception as e:
            logger.error(f"音频段处理异常: {e}")
            # 创建错误结果并放入队列
            try:
                error_segment = {
                    'segment_id': getattr(audio_segment, 'segment_id', 'unknown'),
                    'start_time': getattr(audio_segment, 'start_time', 0),
                    'end_time': getattr(audio_segment, 'end_time', 0),
                    'original_text': "",
                    'translated_text': "",
                    'status': "error",
                    'error_message': str(e)
                }
                if hasattr(self, 'result_queue'):
                    self.result_queue.put(error_segment)
                self.performance_metrics['segments_failed'] += 1
            except Exception as queue_error:
                logger.error(f"无法将错误结果放入队列: {queue_error}")
    
    def _process_audio_segment(self, audio_segment) -> None:
        """处理单个音频段（线程安全）"""
        segment_start_time = time.time()
        segment_id = getattr(audio_segment, 'segment_id', f'segment_{int(time.time())}')
        
        try:
            logger.debug(f"开始处理音频段: {segment_id}")
            
            # 检查停止标志
            if self.stop_flag:
                return
            
            # 语音识别
            try:
                logger.debug(f"开始语音识别: {segment_id}")
                subtitle_segment = self.speech_recognizer.transcribe_segment_optimized(audio_segment)
                original_text = getattr(subtitle_segment, 'original_text', '')
                logger.debug(f"语音识别完成: {segment_id}, 原文长度: {len(original_text)}")
            except Exception as e:
                logger.error(f"语音识别失败: {segment_id}, {e}")
                subtitle_segment = self._create_error_segment(audio_segment, f"语音识别失败: {e}")
            
            # 检查停止标志
            if self.stop_flag:
                return
            
            # 如果识别成功且有文本，进行翻译
            if (hasattr(subtitle_segment, 'original_text') and 
                subtitle_segment.original_text and 
                getattr(subtitle_segment, 'status', '') != "error"):
                
                try:
                    logger.debug(f"开始翻译: {segment_id}")
                    subtitle_segment = self.translation_optimizer.translate_optimized(subtitle_segment)
                    translated_text = getattr(subtitle_segment, 'translated_text', '')
                    logger.debug(f"翻译完成: {segment_id}, 译文长度: {len(translated_text)}")
                except Exception as e:
                    logger.error(f"翻译失败: {segment_id}, {e}")
                    # 保留原文，标记翻译失败
                    if hasattr(subtitle_segment, 'translated_text'):
                        subtitle_segment.translated_text = subtitle_segment.original_text
            else:
                logger.debug(f"跳过翻译: {segment_id}, 原文为空或状态错误")
            
            # 将结果转换为字典格式并放入队列
            try:
                if hasattr(subtitle_segment, 'to_dict'):
                    result_dict = subtitle_segment.to_dict()
                else:
                    # 手动创建结果字典
                    result_dict = {
                        'segment_id': segment_id,
                        'start_time': getattr(audio_segment, 'start_time', 0),
                        'end_time': getattr(audio_segment, 'end_time', 0),
                        'original_text': getattr(subtitle_segment, 'original_text', ''),
                        'translated_text': getattr(subtitle_segment, 'translated_text', ''),
                        'status': getattr(subtitle_segment, 'status', 'completed')
                    }
                
                if hasattr(self, 'result_queue'):
                    self.result_queue.put(result_dict)
                    logger.debug(f"字幕段已放入结果队列: {segment_id}")
                
            except Exception as e:
                logger.error(f"结果入队失败: {segment_id}, {e}")
            
            # 更新统计信息
            self.performance_metrics['segments_processed'] += 1
            
            segment_time = time.time() - segment_start_time
            self.performance_metrics['total_processing_time'] += segment_time
            if self.performance_metrics['segments_processed'] > 0:
                self.performance_metrics['average_processing_time'] = (
                    self.performance_metrics['total_processing_time'] / 
                    self.performance_metrics['segments_processed']
                )
            
            logger.debug(f"音频段处理完成: {segment_id}, 耗时: {segment_time:.2f}s")
            
        except Exception as e:
            logger.error(f"音频段处理失败: {segment_id}, {e}")
            
            # 创建错误结果
            error_result = self._create_error_segment(audio_segment, str(e))
            try:
                if hasattr(error_result, 'to_dict'):
                    if hasattr(self, 'result_queue'):
                        self.result_queue.put(error_result.to_dict())
                else:
                    if hasattr(self, 'result_queue'):
                        self.result_queue.put(error_result)
            except Exception as queue_error:
                logger.error(f"错误结果入队失败: {segment_id}, {queue_error}")
            
            self.performance_metrics['segments_failed'] += 1
    
    def _create_error_segment(self, audio_segment, error_message: str):
        """创建错误段"""
        return {
            'segment_id': getattr(audio_segment, 'segment_id', 'unknown'),
            'start_time': getattr(audio_segment, 'start_time', 0),
            'end_time': getattr(audio_segment, 'end_time', 0),
            'original_text': "",
            'translated_text': "",
            'status': "error",
            'error_message': error_message
        }
    
    def _result_dispatch_loop(self) -> None:
        """结果分发循环（线程安全）"""
        logger.info("启动结果分发循环")
        consecutive_empty_count = 0
        max_empty_count = 10  # 连续空队列次数限制
        
        try:
            while self.is_running or not self.result_queue.empty():
                try:
                    # 获取处理结果
                    result_data = self.result_queue.get(timeout=1.0)
                    consecutive_empty_count = 0  # 重置空队列计数
                    
                    # 确保结果数据是字典格式
                    if hasattr(result_data, 'to_dict'):
                        result_dict = result_data.to_dict()
                    elif isinstance(result_data, dict):
                        result_dict = result_data
                    else:
                        logger.warning(f"未知的结果数据格式: {type(result_data)}")
                        continue
                    
                    segment_id = result_dict.get('segment_id', 'unknown')
                    logger.debug(f"从队列获取字幕段: {segment_id}")
                    
                    # 调用字幕准备回调
                    self._call_callback('on_subtitle_ready', result_dict)
                    logger.debug(f"已调用字幕回调: {segment_id}")
                    
                    # 标记队列任务完成
                    self.result_queue.task_done()
                    
                except Exception as e:
                    consecutive_empty_count += 1
                    # 如果连续多次队列为空且不在运行，可能已完成
                    if not self.is_running and consecutive_empty_count >= max_empty_count:
                        logger.debug("队列持续为空且处理已停止，退出分发循环")
                        break
                    continue
                    
        except Exception as e:
            logger.error(f"结果分发循环异常: {e}")
        finally:
            logger.info("结果分发循环结束")
    
    def _update_progress(self, audio_segment) -> None:
        """更新处理进度（线程安全）"""
        try:
            # 使用已处理段数的集合来跟踪进度，避免跳转后计数错误
            if not hasattr(self, '_processed_segments'):
                self._processed_segments = set()

            # 记录已处理的段
            segment_id = getattr(audio_segment, 'segment_id', None)
            if segment_id:
                self._processed_segments.add(segment_id)

            # 更新当前处理段数（基于实际处理的段数）
            current_segment_count = len(self._processed_segments)
            if hasattr(self.processing_stats, 'current_segment'):
                self.processing_stats.current_segment = current_segment_count

            # 更新处理时长（使用当前段的结束时间）
            end_time = getattr(audio_segment, 'end_time', 0)
            if hasattr(self.processing_stats, 'processed_duration'):
                self.processing_stats.processed_duration = end_time

            # 计算进度百分比（基于段数，更准确）
            total_segments = getattr(self.processing_stats, 'total_segments', 0)
            if total_segments > 0:
                progress_percentage = min(1.0, current_segment_count / total_segments)
            else:
                progress_percentage = 0.0

            # 调用进度更新回调
            progress_info = (current_segment_count, total_segments)

            if total_segments > 0:
                status_message = f"正在处理音频段 {current_segment_count}/{total_segments} ({progress_percentage:.1%})"
            else:
                status_message = f"正在处理音频段 {current_segment_count} ({progress_percentage:.1%})"

            self._call_callback('on_progress_update', status_message, progress_info)

        except Exception as e:
            logger.warning(f"更新进度时出错: {e}")
    
    def _format_error(self, exception: Exception) -> str:
        """格式化错误信息"""
        import json
        error_dict = {
            'error_type': type(exception).__name__,
            'error_message': str(exception),
            'can_retry': False
        }
        return json.dumps(error_dict, ensure_ascii=False)
    
    # 将方法添加到类中
    RealtimeProcessingPipeline.start_processing = start_processing
    RealtimeProcessingPipeline.stop_processing = stop_processing
    RealtimeProcessingPipeline.pause_processing = pause_processing
    RealtimeProcessingPipeline.resume_processing = resume_processing
    RealtimeProcessingPipeline.is_finished = is_finished
    RealtimeProcessingPipeline.cleanup = cleanup
    RealtimeProcessingPipeline._preload_models = _preload_models
    RealtimeProcessingPipeline._call_callback = _call_callback
    RealtimeProcessingPipeline._processing_loop = _processing_loop
    RealtimeProcessingPipeline._process_audio_segment_safe = _process_audio_segment_safe
    RealtimeProcessingPipeline._process_audio_segment = _process_audio_segment
    RealtimeProcessingPipeline._create_error_segment = _create_error_segment
    RealtimeProcessingPipeline._result_dispatch_loop = _result_dispatch_loop
    RealtimeProcessingPipeline._update_progress = _update_progress
    RealtimeProcessingPipeline._format_error = _format_error


# 执行修补
_patch_pipeline_class()