"""
V2subpy异步核心回调接口

定义翻译过程中的回调接口和标准化的回调管理机制。
"""

import threading
from abc import ABC, abstractmethod
from typing import Dict, Any, Callable, Optional
import logging

logger = logging.getLogger(__name__)


class TranslationCallbacks(ABC):
    """翻译回调接口基类"""
    
    @abstractmethod
    def on_subtitle_ready(self, task_id: str, subtitle: Dict[str, Any]) -> None:
        """
        字幕准备就绪回调
        
        Args:
            task_id: 任务ID
            subtitle: 字幕数据，包含text、start_time、end_time等
        """
        pass
    
    @abstractmethod
    def on_progress_update(self, task_id: str, progress: Dict[str, Any]) -> None:
        """
        进度更新回调
        
        Args:
            task_id: 任务ID
            progress: 进度信息，包含percentage、message等
        """
        pass
    
    @abstractmethod
    def on_error(self, task_id: str, error: Dict[str, Any]) -> None:
        """
        错误回调
        
        Args:
            task_id: 任务ID
            error: 错误信息，包含error_type、message、traceback等
        """
        pass
    
    @abstractmethod
    def on_finished(self, task_id: str, result: Dict[str, Any]) -> None:
        """
        完成回调
        
        Args:
            task_id: 任务ID
            result: 最终结果信息
        """
        pass


class FunctionCallbacks(TranslationCallbacks):
    """基于函数的回调实现"""
    
    def __init__(self, 
                 on_subtitle_ready: Optional[Callable[[str, Dict[str, Any]], None]] = None,
                 on_progress_update: Optional[Callable[[str, Dict[str, Any]], None]] = None,
                 on_error: Optional[Callable[[str, Dict[str, Any]], None]] = None,
                 on_finished: Optional[Callable[[str, Dict[str, Any]], None]] = None):
        self._on_subtitle_ready = on_subtitle_ready
        self._on_progress_update = on_progress_update
        self._on_error = on_error
        self._on_finished = on_finished
    
    def on_subtitle_ready(self, task_id: str, subtitle: Dict[str, Any]) -> None:
        if self._on_subtitle_ready:
            try:
                self._on_subtitle_ready(task_id, subtitle)
            except Exception as e:
                logger.error(f"Error in subtitle_ready callback: {e}")
    
    def on_progress_update(self, task_id: str, progress: Dict[str, Any]) -> None:
        if self._on_progress_update:
            try:
                self._on_progress_update(task_id, progress)
            except Exception as e:
                logger.error(f"Error in progress_update callback: {e}")
    
    def on_error(self, task_id: str, error: Dict[str, Any]) -> None:
        if self._on_error:
            try:
                self._on_error(task_id, error)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")
    
    def on_finished(self, task_id: str, result: Dict[str, Any]) -> None:
        if self._on_finished:
            try:
                self._on_finished(task_id, result)
            except Exception as e:
                logger.error(f"Error in finished callback: {e}")


class CallbackManager:
    """回调管理器，提供线程安全的回调分发"""
    
    def __init__(self):
        self._callbacks: Dict[str, TranslationCallbacks] = {}
        self._lock = threading.RLock()
    
    def register_callbacks(self, task_id: str, callbacks: TranslationCallbacks):
        """注册任务的回调"""
        with self._lock:
            self._callbacks[task_id] = callbacks
    
    def unregister_callbacks(self, task_id: str):
        """注销任务的回调"""
        with self._lock:
            self._callbacks.pop(task_id, None)
    
    def call_subtitle_ready(self, task_id: str, subtitle: Dict[str, Any]):
        """调用字幕准备就绪回调"""
        with self._lock:
            callbacks = self._callbacks.get(task_id)
            if callbacks:
                try:
                    callbacks.on_subtitle_ready(task_id, subtitle)
                except Exception as e:
                    logger.error(f"Error calling subtitle_ready callback for task {task_id}: {e}")
    
    def call_progress_update(self, task_id: str, progress: Dict[str, Any]):
        """调用进度更新回调"""
        with self._lock:
            callbacks = self._callbacks.get(task_id)
            if callbacks:
                try:
                    callbacks.on_progress_update(task_id, progress)
                except Exception as e:
                    logger.error(f"Error calling progress_update callback for task {task_id}: {e}")
    
    def call_error(self, task_id: str, error: Dict[str, Any]):
        """调用错误回调"""
        with self._lock:
            callbacks = self._callbacks.get(task_id)
            if callbacks:
                try:
                    callbacks.on_error(task_id, error)
                except Exception as e:
                    logger.error(f"Error calling error callback for task {task_id}: {e}")
    
    def call_finished(self, task_id: str, result: Dict[str, Any]):
        """调用完成回调"""
        with self._lock:
            callbacks = self._callbacks.get(task_id)
            if callbacks:
                try:
                    callbacks.on_finished(task_id, result)
                except Exception as e:
                    logger.error(f"Error calling finished callback for task {task_id}: {e}")
                finally:
                    # 任务完成后自动注销回调
                    self.unregister_callbacks(task_id)


def create_callbacks_from_functions(**kwargs) -> FunctionCallbacks:
    """
    从函数创建回调对象的便捷函数
    
    Args:
        on_subtitle_ready: 字幕准备就绪回调函数
        on_progress_update: 进度更新回调函数
        on_error: 错误回调函数
        on_finished: 完成回调函数
    
    Returns:
        FunctionCallbacks对象
    """
    return FunctionCallbacks(**kwargs)