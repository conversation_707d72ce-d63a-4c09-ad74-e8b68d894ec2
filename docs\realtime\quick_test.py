#!/usr/bin/env python3
"""
快速测试脚本

用于快速验证实时翻译功能是否正常工作。
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    try:
        from v2subpy.realtime import realtime_trans, RealtimeConfig
        from v2subpy.realtime.models import AudioSegment, SubtitleSegment
        print("✅ 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config_creation():
    """测试配置创建"""
    print("🔍 测试配置创建...")
    try:
        from v2subpy.realtime.models import RealtimeConfig
        
        config = RealtimeConfig(
            whisper_model='medium',
            translator_type='default',
            target_language='zh'
        )
        
        config.validate()
        print("✅ 配置创建成功")
        return True
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试基本功能...")
    
    VIDEO_PATH = r"d:\testvideo\en1.mp4"
    
    # 检查视频文件
    if not Path(VIDEO_PATH).exists():
        print(f"⚠️ 测试视频文件不存在: {VIDEO_PATH}")
        print("跳过功能测试")
        return True
    
    try:
        from v2subpy.realtime import realtime_trans
        
        # 简单的回调函数
        subtitle_count = 0
        
        def on_subtitle_ready(subtitle_dict):
            nonlocal subtitle_count
            subtitle_count += 1
            print(f"📝 收到第 {subtitle_count} 条字幕: {subtitle_dict['original_text'][:30]}...")
        
        def on_progress_update(status_message, progress_info):
            current, total = progress_info
            percentage = (current / total * 100) if total > 0 else 0
            print(f"\r📊 进度: {percentage:.1f}%", end="", flush=True)
        
        def on_error(error_message):
            print(f"\n❌ 错误: {error_message}")
        
        finished = False
        def on_finished():
            nonlocal finished
            finished = True
            print(f"\n✅ 处理完成，共收到 {subtitle_count} 条字幕")
        
        # 配置
        config = {
            'whisper_model': 'base',  # 使用较小的模型进行快速测试
            'translator_type': 'default',
            'target_language': 'zh',
            'segment_duration': 10.0,  # 较长的段落减少处理量
            'max_concurrent_segments': 1,  # 单线程处理
        }
        
        print(f"🚀 开始处理视频: {VIDEO_PATH}")
        
        # 启动处理
        realtime_trans(
            video_path=VIDEO_PATH,
            config=config,
            on_subtitle_ready=on_subtitle_ready,
            on_progress_update=on_progress_update,
            on_error=on_error,
            on_finished=on_finished
        )
        
        # 等待完成（最多2分钟）
        timeout = 120
        start_time = time.time()
        
        while not finished and (time.time() - start_time) < timeout:
            time.sleep(1)
        
        if finished:
            print("✅ 基本功能测试成功")
            return True
        else:
            print("⚠️ 测试超时，但可能仍在正常工作")
            return True
            
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 v2subpy 实时翻译快速测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置创建", test_config_creation),
        ("基本功能", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ 测试失败: {test_name}")
        except Exception as e:
            print(f"💥 测试异常: {test_name} - {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(0)