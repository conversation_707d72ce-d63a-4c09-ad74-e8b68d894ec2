#!/usr/bin/env python3
"""
V2subpy通用异步集成示例

展示如何使用v2subpy的通用异步接口进行实时翻译。
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from v2subpy.realtime.integrations import AsyncRealtimeTranslator


class SimpleTranslationApp:
    """简单的翻译应用示例"""
    
    def __init__(self):
        self.translator = AsyncRealtimeTranslator(max_concurrent_tasks=2)
        self.active_tasks = {}
        self.running = True
    
    def on_subtitle_ready(self, task_id: str, subtitle_data: dict):
        """字幕准备就绪回调"""
        original_text = subtitle_data.get('original_text', '')
        translated_text = subtitle_data.get('translated_text', '')
        start_time = subtitle_data.get('start_time', 0)
        end_time = subtitle_data.get('end_time', 0)
        
        print(f"[{task_id}] {start_time:.1f}s-{end_time:.1f}s")
        print(f"  原文: {original_text}")
        print(f"  译文: {translated_text}")
        print("-" * 50)
    
    def on_progress_update(self, task_id: str, progress_data: dict):
        """进度更新回调"""
        message = progress_data.get('message', '')
        progress_info = progress_data.get('progress_info', (0, 0))
        
        if len(progress_info) >= 2:
            current, total = progress_info[0], progress_info[1]
            if total > 0:
                percentage = (current / total) * 100
                print(f"[{task_id}] 进度: {percentage:.1f}% - {message}")
    
    def on_error(self, task_id: str, error_data: dict):
        """错误回调"""
        error_message = error_data.get('message', error_data.get('error', 'Unknown error'))
        error_type = error_data.get('error_type', 'Unknown')
        
        print(f"[{task_id}] 错误 ({error_type}): {error_message}")
        
        # 从活跃任务中移除
        self.active_tasks.pop(task_id, None)
    
    def on_finished(self, task_id: str, result_data: dict):
        """完成回调"""
        print(f"[{task_id}] 翻译完成!")
        
        # 从活跃任务中移除
        self.active_tasks.pop(task_id, None)
        
        # 如果没有活跃任务，退出应用
        if not self.active_tasks:
            print("所有任务完成，应用退出")
            self.running = False
    
    def start_translation(self, video_path: str, config: dict = None):
        """启动翻译任务"""
        try:
            callbacks = {
                'on_subtitle_ready': self.on_subtitle_ready,
                'on_progress_update': self.on_progress_update,
                'on_error': self.on_error,
                'on_finished': self.on_finished
            }
            
            task_id = self.translator.start_translation(
                video_path=video_path,
                config=config or {},
                callbacks=callbacks
            )
            
            self.active_tasks[task_id] = {
                'video_path': video_path,
                'config': config
            }
            
            print(f"启动翻译任务: {task_id}")
            print(f"视频文件: {video_path}")
            print(f"配置: {config}")
            print("=" * 50)
            
            return task_id
            
        except Exception as e:
            print(f"启动翻译失败: {e}")
            return None
    
    def stop_translation(self, task_id: str):
        """停止翻译任务"""
        if self.translator.stop_translation(task_id):
            print(f"停止翻译任务: {task_id}")
            self.active_tasks.pop(task_id, None)
            return True
        else:
            print(f"停止翻译任务失败: {task_id}")
            return False
    
    def list_tasks(self):
        """列出所有任务"""
        tasks = self.translator.list_tasks()
        print("\n当前任务列表:")
        print("-" * 30)
        
        if not tasks:
            print("无活跃任务")
        else:
            for task_id, status in tasks.items():
                video_path = status.get('video_path', 'Unknown')
                task_status = status.get('status', 'Unknown')
                progress = status.get('progress', 0)
                
                print(f"任务ID: {task_id}")
                print(f"  视频: {Path(video_path).name}")
                print(f"  状态: {task_status}")
                print(f"  进度: {progress:.1%}")
                print()
    
    def get_statistics(self):
        """获取统计信息"""
        stats = self.translator.get_statistics()
        print("\n统计信息:")
        print("-" * 20)
        print(f"总任务数: {stats.get('total_tasks', 0)}")
        print(f"最大并发数: {stats.get('max_concurrent_tasks', 0)}")
        
        status_counts = stats.get('status_counts', {})
        if status_counts:
            print("状态分布:")
            for status, count in status_counts.items():
                print(f"  {status}: {count}")
    
    def run_interactive(self):
        """运行交互式模式"""
        print("V2subpy异步翻译示例")
        print("=" * 30)
        print("命令:")
        print("  start <video_path> - 启动翻译")
        print("  stop <task_id> - 停止翻译")
        print("  list - 列出任务")
        print("  stats - 显示统计")
        print("  quit - 退出")
        print()
        
        while self.running:
            try:
                command = input("> ").strip().split()
                
                if not command:
                    continue
                
                cmd = command[0].lower()
                
                if cmd == 'start' and len(command) > 1:
                    video_path = command[1]
                    config = {
                        'whisper_model': 'small',
                        'target_language': 'zh',
                        'translation_engine': 'default'
                    }
                    self.start_translation(video_path, config)
                
                elif cmd == 'stop' and len(command) > 1:
                    task_id = command[1]
                    self.stop_translation(task_id)
                
                elif cmd == 'list':
                    self.list_tasks()
                
                elif cmd == 'stats':
                    self.get_statistics()
                
                elif cmd == 'quit':
                    print("退出应用...")
                    self.translator.stop_all_tasks()
                    self.running = False
                
                else:
                    print("未知命令，请重试")
                
            except KeyboardInterrupt:
                print("\n退出应用...")
                self.translator.stop_all_tasks()
                self.running = False
            except Exception as e:
                print(f"命令执行错误: {e}")
    
    def run_batch(self, video_files: list):
        """批量处理模式"""
        print(f"批量处理 {len(video_files)} 个视频文件")
        print("=" * 40)
        
        config = {
            'whisper_model': 'small',
            'target_language': 'zh',
            'translation_engine': 'openai'
        }
        
        # 启动所有任务
        for video_path in video_files:
            if Path(video_path).exists():
                self.start_translation(video_path, config)
            else:
                print(f"文件不存在: {video_path}")
        
        # 等待所有任务完成
        while self.running and self.active_tasks:
            time.sleep(1)
            
            # 定期显示统计信息
            if len(self.active_tasks) > 0:
                self.get_statistics()
                time.sleep(5)


def main():
    """主函数"""
    app = SimpleTranslationApp()
    
    if len(sys.argv) > 1:
        # 批量处理模式
        video_files = sys.argv[1:]
        app.run_batch(video_files)
    else:
        # 交互式模式
        app.run_interactive()


if __name__ == '__main__':
    main()