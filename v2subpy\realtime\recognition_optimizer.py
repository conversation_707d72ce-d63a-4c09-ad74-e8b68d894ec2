"""
语音识别优化器

提供语音识别的性能优化功能，包括缓存、批量处理、错误处理和重试机制。
"""

import time
import threading
from typing import Dict, List, Optional, Tuple
from collections import OrderedDict
from concurrent.futures import ThreadPoolExecutor, Future
import hashlib

from v2subpy.utils.log import logger
from .models import AudioSegment, SubtitleSegment, RealtimeConfig
from .speech_recognizer import SpeechRecognizer
from .exceptions import SpeechRecognitionError
from .retry_manager import RetryManager


class RecognitionCache:
    """语音识别结果缓存"""
    
    def __init__(self, max_size: int = 100):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
        """
        self.max_size = max_size
        self.cache: OrderedDict[str, SubtitleSegment] = OrderedDict()
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
    
    def _generate_cache_key(self, audio_segment: AudioSegment) -> str:
        """生成缓存键"""
        # 基于音频段的关键信息生成哈希键
        key_data = f"{audio_segment.start_time}_{audio_segment.end_time}_{audio_segment.duration()}"
        
        # 如果有文件路径，加入文件修改时间
        if audio_segment.file_path:
            try:
                import os
                mtime = os.path.getmtime(audio_segment.file_path)
                key_data += f"_{mtime}"
            except OSError:
                pass
        
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, audio_segment: AudioSegment) -> Optional[SubtitleSegment]:
        """获取缓存的识别结果"""
        cache_key = self._generate_cache_key(audio_segment)
        
        with self.lock:
            if cache_key in self.cache:
                # 移动到末尾（LRU）
                result = self.cache.pop(cache_key)
                self.cache[cache_key] = result
                self.hit_count += 1
                logger.debug(f"缓存命中: {audio_segment.segment_id}")
                return result
            else:
                self.miss_count += 1
                return None
    
    def put(self, audio_segment: AudioSegment, result: SubtitleSegment) -> None:
        """存储识别结果到缓存"""
        cache_key = self._generate_cache_key(audio_segment)
        
        with self.lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size:
                self.cache.popitem(last=False)
            
            self.cache[cache_key] = result
            logger.debug(f"缓存存储: {audio_segment.segment_id}")
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.hit_count = 0
            self.miss_count = 0
    
    def get_stats(self) -> Dict[str, any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate
            }


class OptimizedSpeechRecognizer:
    """优化的语音识别器"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化优化的语音识别器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.recognizer = SpeechRecognizer(realtime_config)
        self.cache = RecognitionCache(max_size=200)
        self.retry_manager = RetryManager(
            max_retries=realtime_config.max_retries,
            base_delay=realtime_config.retry_delay
        )
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.performance_stats = {
            'total_segments': 0,
            'successful_segments': 0,
            'failed_segments': 0,
            'cache_hits': 0,
            'total_time': 0.0,
            'average_time': 0.0
        }
    
    def transcribe_segment_optimized(self, audio_segment: AudioSegment) -> SubtitleSegment:
        """
        优化的音频段转录
        
        Args:
            audio_segment: 音频段对象
            
        Returns:
            SubtitleSegment: 字幕段对象
        """
        start_time = time.time()
        self.performance_stats['total_segments'] += 1
        
        try:
            # 首先检查缓存
            cached_result = self.cache.get(audio_segment)
            if cached_result is not None:
                self.performance_stats['cache_hits'] += 1
                return cached_result
            
            # 使用重试机制进行识别
            result = self.retry_manager.retry_with_backoff(
                self.recognizer.transcribe_segment,
                audio_segment
            )
            
            # 存储到缓存
            self.cache.put(audio_segment, result)
            
            self.performance_stats['successful_segments'] += 1
            return result
            
        except Exception as e:
            self.performance_stats['failed_segments'] += 1
            logger.error(f"优化识别失败: {audio_segment.segment_id}, {e}")
            
            # 返回空的字幕段
            return SubtitleSegment(
                segment_id=audio_segment.segment_id,
                start_time=audio_segment.start_time,
                end_time=audio_segment.end_time,
                original_text="",
                status="error"
            )
        finally:
            # 更新性能统计
            process_time = time.time() - start_time
            self.performance_stats['total_time'] += process_time
            if self.performance_stats['total_segments'] > 0:
                self.performance_stats['average_time'] = (
                    self.performance_stats['total_time'] / 
                    self.performance_stats['total_segments']
                )
    
    def transcribe_batch_optimized(self, audio_segments: List[AudioSegment]) -> List[SubtitleSegment]:
        """
        优化的批量转录
        
        Args:
            audio_segments: 音频段列表
            
        Returns:
            List[SubtitleSegment]: 字幕段列表
        """
        if not audio_segments:
            return []
        
        logger.debug(f"开始批量转录: {len(audio_segments)} 个音频段")
        
        # 分离缓存命中和需要处理的段落
        cached_results = {}
        segments_to_process = []
        
        for segment in audio_segments:
            cached_result = self.cache.get(segment)
            if cached_result is not None:
                cached_results[segment.segment_id] = cached_result
                self.performance_stats['cache_hits'] += 1
            else:
                segments_to_process.append(segment)
        
        # 并发处理需要识别的段落
        future_to_segment = {}
        for segment in segments_to_process:
            future = self.executor.submit(
                self.transcribe_segment_optimized, segment
            )
            future_to_segment[future] = segment
        
        # 收集结果
        processed_results = {}
        for future in future_to_segment:
            segment = future_to_segment[future]
            try:
                result = future.result(timeout=30.0)  # 30秒超时
                processed_results[segment.segment_id] = result
            except Exception as e:
                logger.error(f"批量转录失败: {segment.segment_id}, {e}")
                processed_results[segment.segment_id] = SubtitleSegment(
                    segment_id=segment.segment_id,
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    original_text="",
                    status="error"
                )
        
        # 按原始顺序组合结果
        results = []
        for segment in audio_segments:
            if segment.segment_id in cached_results:
                results.append(cached_results[segment.segment_id])
            elif segment.segment_id in processed_results:
                results.append(processed_results[segment.segment_id])
            else:
                # 创建错误结果
                results.append(SubtitleSegment(
                    segment_id=segment.segment_id,
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    original_text="",
                    status="error"
                ))
        
        logger.debug(f"批量转录完成: {len(results)} 个结果")
        return results
    
    def preload_model(self) -> None:
        """预加载模型"""
        try:
            self.recognizer.load_model()
            logger.info("语音识别模型预加载完成")
        except Exception as e:
            logger.error(f"模型预加载失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        stats.update({
            'cache_stats': self.cache.get_stats(),
            'success_rate': (
                (stats['successful_segments'] / stats['total_segments'] * 100)
                if stats['total_segments'] > 0 else 0
            )
        })
        return stats
    
    def optimize_for_realtime(self) -> None:
        """针对实时处理进行优化"""
        # 预加载模型
        self.preload_model()
        
        # 调整缓存大小
        if self.config.max_concurrent_segments > 5:
            self.cache.max_size = 300
        
        logger.info("语音识别器已优化用于实时处理")
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.cache.clear()
            self.executor.shutdown(wait=False)
            self.recognizer.unload_model()
            logger.debug("优化识别器资源清理完成")
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")


class AdaptiveRecognitionManager:
    """自适应语音识别管理器"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化自适应识别管理器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.recognizer = OptimizedSpeechRecognizer(realtime_config)
        self.performance_history = []
        self.current_batch_size = 3
        self.min_batch_size = 1
        self.max_batch_size = 8
        
    def process_segments_adaptive(self, audio_segments: List[AudioSegment]) -> List[SubtitleSegment]:
        """
        自适应处理音频段
        
        Args:
            audio_segments: 音频段列表
            
        Returns:
            List[SubtitleSegment]: 字幕段列表
        """
        if not audio_segments:
            return []
        
        start_time = time.time()
        
        # 根据当前批量大小处理
        results = []
        for i in range(0, len(audio_segments), self.current_batch_size):
            batch = audio_segments[i:i + self.current_batch_size]
            batch_results = self.recognizer.transcribe_batch_optimized(batch)
            results.extend(batch_results)
        
        # 记录性能
        process_time = time.time() - start_time
        self.performance_history.append({
            'batch_size': self.current_batch_size,
            'segment_count': len(audio_segments),
            'process_time': process_time,
            'avg_time_per_segment': process_time / len(audio_segments)
        })
        
        # 调整批量大小
        self._adjust_batch_size()
        
        return results
    
    def _adjust_batch_size(self) -> None:
        """根据性能历史调整批量大小"""
        if len(self.performance_history) < 3:
            return
        
        # 分析最近的性能数据
        recent_performance = self.performance_history[-3:]
        avg_time_per_segment = sum(p['avg_time_per_segment'] for p in recent_performance) / 3
        
        # 如果处理速度太慢，减少批量大小
        if avg_time_per_segment > 2.0 and self.current_batch_size > self.min_batch_size:
            self.current_batch_size = max(self.min_batch_size, self.current_batch_size - 1)
            logger.debug(f"减少批量大小到: {self.current_batch_size}")
        
        # 如果处理速度很快，增加批量大小
        elif avg_time_per_segment < 1.0 and self.current_batch_size < self.max_batch_size:
            self.current_batch_size = min(self.max_batch_size, self.current_batch_size + 1)
            logger.debug(f"增加批量大小到: {self.current_batch_size}")
        
        # 限制历史记录大小
        if len(self.performance_history) > 10:
            self.performance_history = self.performance_history[-10:]