#!/usr/bin/env python3
"""
v2subpy 实时字幕简单集成示例

这个示例展示了如何在命令行环境中使用 v2subpy 实时字幕功能。
适合用于测试、调试或简单的脚本集成。
"""

import sys
import json
import time
import argparse
from pathlib import Path
from typing import Dict, Any

# 导入 v2subpy 实时翻译功能
try:
    from v2subpy.realtime import realtime_trans
except ImportError:
    print("错误: 无法导入 v2subpy.realtime 模块")
    print("请确保 v2subpy 已正确安装并且实时翻译模块可用")
    sys.exit(1)


class SimpleRealtimeTranslator:
    """简单的实时翻译器"""
    
    def __init__(self):
        self.subtitle_count = 0
        self.start_time = 0
        self.subtitles = []  # 存储所有字幕
    
    def translate_video(self, video_path: str, config: Dict[str, Any] = None):
        """翻译视频"""
        print(f"开始处理视频: {video_path}")
        print(f"配置参数: {json.dumps(config or {}, ensure_ascii=False, indent=2)}")
        print("-" * 60)
        
        self.start_time = time.time()
        
        try:
            realtime_trans(
                video_path=video_path,
                config=config or {},
                on_subtitle_ready=self.on_subtitle_ready,
                on_progress_update=self.on_progress_update,
                on_error=self.on_error,
                on_finished=self.on_finished
            )
        except KeyboardInterrupt:
            print("\n用户中断处理")
        except Exception as e:
            print(f"\n处理失败: {e}")
    
    def on_subtitle_ready(self, subtitle_dict: dict):
        """字幕准备就绪回调"""
        self.subtitle_count += 1
        
        # 提取字幕信息
        start_time = subtitle_dict.get('start_time', 0)
        end_time = subtitle_dict.get('end_time', 0)
        original_text = subtitle_dict.get('original_text', '')
        translated_text = subtitle_dict.get('translated_text', '')
        confidence = subtitle_dict.get('confidence', 0)
        language = subtitle_dict.get('language', '')
        
        # 存储字幕
        self.subtitles.append(subtitle_dict)
        
        # 格式化时间
        start_str = self.format_time(start_time)
        end_str = self.format_time(end_time)
        
        # 显示字幕
        print(f"\n📝 字幕 #{self.subtitle_count}")
        print(f"⏰ 时间: {start_str} - {end_str}")
        print(f"🌐 语言: {language} (置信度: {confidence:.2f})")
        print(f"📄 原文: {original_text}")
        print(f"🔄 译文: {translated_text}")
        print("-" * 60)
    
    def on_progress_update(self, status_message: str, progress_info: tuple):
        """进度更新回调"""
        # 显示进度信息
        elapsed = time.time() - self.start_time
        elapsed_str = self.format_time(elapsed)
        
        print(f"\r⏳ {status_message} | 运行时间: {elapsed_str}", end="", flush=True)
        
        # 如果有详细进度信息，显示进度条
        if len(progress_info) >= 4:
            current_segment, total_segments, processed_duration, total_duration = progress_info
            
            if total_duration > 0:
                progress_percent = (processed_duration / total_duration) * 100
                progress_bar = self.create_progress_bar(progress_percent)
                print(f" | {progress_bar} {progress_percent:.1f}%", end="", flush=True)
    
    def on_error(self, error_json: str):
        """错误回调"""
        try:
            error_data = json.loads(error_json)
            error_type = error_data.get('error_type', 'Unknown')
            error_message = error_data.get('error_message', '未知错误')
            segment_id = error_data.get('segment_id', '')
            can_retry = error_data.get('can_retry', False)
            
            print(f"\n❌ 错误发生:")
            print(f"   类型: {error_type}")
            print(f"   消息: {error_message}")
            if segment_id:
                print(f"   段落: {segment_id}")
            print(f"   可重试: {'是' if can_retry else '否'}")
            
            if can_retry:
                print("   系统将自动重试...")
            
        except json.JSONDecodeError:
            print(f"\n❌ 错误: {error_json}")
    
    def on_finished(self):
        """完成回调"""
        elapsed = time.time() - self.start_time
        elapsed_str = self.format_time(elapsed)
        
        print(f"\n\n🎉 翻译完成!")
        print(f"📊 统计信息:")
        print(f"   总字幕数: {self.subtitle_count}")
        print(f"   总用时: {elapsed_str}")
        if self.subtitle_count > 0:
            avg_time = elapsed / self.subtitle_count
            print(f"   平均每条字幕用时: {avg_time:.2f}秒")
    
    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def create_progress_bar(self, percent: float, width: int = 20) -> str:
        """创建进度条"""
        filled = int(width * percent / 100)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"
    
    def save_subtitles(self, output_path: str):
        """保存字幕到文件"""
        if not self.subtitles:
            print("没有字幕可保存")
            return
        
        try:
            output_file = Path(output_path)
            
            if output_file.suffix.lower() == '.json':
                # 保存为JSON格式
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(self.subtitles, f, ensure_ascii=False, indent=2)
            elif output_file.suffix.lower() == '.srt':
                # 保存为SRT格式
                with open(output_file, 'w', encoding='utf-8') as f:
                    for i, subtitle in enumerate(self.subtitles, 1):
                        start_time = self.format_srt_time(subtitle.get('start_time', 0))
                        end_time = self.format_srt_time(subtitle.get('end_time', 0))
                        text = subtitle.get('translated_text', subtitle.get('original_text', ''))
                        
                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{text}\n\n")
            else:
                # 保存为纯文本格式
                with open(output_file, 'w', encoding='utf-8') as f:
                    for subtitle in self.subtitles:
                        start_time = self.format_time(subtitle.get('start_time', 0))
                        end_time = self.format_time(subtitle.get('end_time', 0))
                        original = subtitle.get('original_text', '')
                        translated = subtitle.get('translated_text', '')
                        
                        f.write(f"[{start_time} - {end_time}]\n")
                        f.write(f"原文: {original}\n")
                        f.write(f"译文: {translated}\n\n")
            
            print(f"✅ 字幕已保存到: {output_path}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def format_srt_time(self, seconds: float) -> str:
        """格式化SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace('.', ',')


def create_config_from_args(args) -> Dict[str, Any]:
    """从命令行参数创建配置"""
    config = {}
    
    if args.whisper_model:
        config['whisper_model'] = args.whisper_model
    if args.whisper_device:
        config['whisper_device'] = args.whisper_device
    if args.source_language:
        config['source_language'] = args.source_language
    if args.translator:
        config['translator_type'] = args.translator
    if args.target_language:
        config['target_language'] = args.target_language
    if args.segment_duration:
        config['segment_duration'] = args.segment_duration
    if args.overlap_duration:
        config['overlap_duration'] = args.overlap_duration
    if args.max_concurrent:
        config['max_concurrent_segments'] = args.max_concurrent
    if args.max_retries:
        config['max_retries'] = args.max_retries
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="v2subpy 实时字幕命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python simple_integration_example.py video.mp4
  python simple_integration_example.py video.mp4 --whisper-model base --target-language zh
  python simple_integration_example.py video.mp4 --output subtitles.srt
  python simple_integration_example.py video.mp4 --translator openai --max-concurrent 5
        """
    )
    
    # 必需参数
    parser.add_argument('video_path', help='视频文件路径')
    
    # 可选参数
    parser.add_argument('--output', '-o', help='输出字幕文件路径 (支持 .txt, .srt, .json)')
    
    # 语音识别参数
    parser.add_argument('--whisper-model', choices=['tiny', 'base', 'small', 'medium', 'large'],
                       help='Whisper模型选择 (默认: base)')
    parser.add_argument('--whisper-device', choices=['auto', 'cpu', 'cuda'],
                       help='设备选择 (默认: auto)')
    parser.add_argument('--source-language', 
                       help='源语言 (如: en, zh, ja, auto)')
    
    # 翻译参数
    parser.add_argument('--translator', choices=['openai', 'google', 'default', 'deepl'],
                       help='翻译引擎选择 (默认: openai)')
    parser.add_argument('--target-language',
                       help='目标语言 (如: zh, en, ja)')
    
    # 性能参数
    parser.add_argument('--segment-duration', type=float,
                       help='音频段长度(秒) (默认: 8.0)')
    parser.add_argument('--overlap-duration', type=float,
                       help='重叠时长(秒) (默认: 1.0)')
    parser.add_argument('--max-concurrent', type=int,
                       help='最大并发数 (默认: 3)')
    parser.add_argument('--max-retries', type=int,
                       help='最大重试次数 (默认: 3)')
    
    # 解析参数
    args = parser.parse_args()
    
    # 检查视频文件
    video_path = Path(args.video_path)
    if not video_path.exists():
        print(f"错误: 视频文件不存在: {args.video_path}")
        sys.exit(1)
    
    # 创建配置
    config = create_config_from_args(args)
    
    # 创建翻译器并开始处理
    translator = SimpleRealtimeTranslator()
    
    try:
        translator.translate_video(str(video_path), config)
        
        # 如果指定了输出文件，保存字幕
        if args.output:
            translator.save_subtitles(args.output)
            
    except Exception as e:
        print(f"处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()