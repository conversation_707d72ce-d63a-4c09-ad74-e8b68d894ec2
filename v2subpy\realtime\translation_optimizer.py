"""
翻译优化器

提供翻译的高级优化功能，包括智能缓存、批量优化、降级处理等。
"""

import time
import threading
from typing import Dict, List, Optional, Set, Tuple
from collections import OrderedDict, defaultdict
from dataclasses import dataclass
import hashlib
import json

from v2subpy.utils.log import logger
from .models import SubtitleSegment, RealtimeConfig
from .translation_engine import TranslationEngine
from .exceptions import TranslationError


@dataclass
class TranslationCacheEntry:
    """翻译缓存条目"""
    original_text: str
    translated_text: str
    source_lang: str
    target_lang: str
    timestamp: float
    hit_count: int = 0
    confidence: float = 1.0


class IntelligentTranslationCache:
    """智能翻译缓存"""
    
    def __init__(self, max_size: int = 500, ttl: float = 3600.0):
        """
        初始化智能缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: OrderedDict[str, TranslationCacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
        
        logger.debug(f"智能翻译缓存初始化: max_size={max_size}, ttl={ttl}s")
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """生成缓存键"""
        # 标准化文本（去除多余空格、标点符号等）
        normalized_text = self._normalize_text(text)
        key_data = f"{normalized_text}_{source_lang}_{target_lang}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本"""
        import re
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        # 统一标点符号
        text = text.replace('，', ',').replace('。', '.').replace('？', '?').replace('！', '!')
        return text.lower()
    
    def get(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """获取缓存的翻译结果"""
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        
        with self.lock:
            if cache_key in self.cache:
                entry = self.cache[cache_key]
                
                # 检查是否过期
                if time.time() - entry.timestamp > self.ttl:
                    del self.cache[cache_key]
                    self.stats['expired'] += 1
                    return None
                
                # 更新访问统计
                entry.hit_count += 1
                # 移动到末尾（LRU）
                self.cache.move_to_end(cache_key)
                self.stats['hits'] += 1
                
                logger.debug(f"缓存命中: {text[:30]}...")
                return entry.translated_text
            else:
                self.stats['misses'] += 1
                return None
    
    def put(self, text: str, translated_text: str, source_lang: str, 
            target_lang: str, confidence: float = 1.0) -> None:
        """存储翻译结果到缓存"""
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        
        with self.lock:
            # 如果缓存已满，删除最少使用的条目
            while len(self.cache) >= self.max_size:
                oldest_key, _ = self.cache.popitem(last=False)
                self.stats['evictions'] += 1
                logger.debug(f"缓存淘汰: {oldest_key}")
            
            # 创建缓存条目
            entry = TranslationCacheEntry(
                original_text=text,
                translated_text=translated_text,
                source_lang=source_lang,
                target_lang=target_lang,
                timestamp=time.time(),
                confidence=confidence
            )
            
            self.cache[cache_key] = entry
            logger.debug(f"缓存存储: {text[:30]}...")
    
    def get_similar_translations(self, text: str, source_lang: str, 
                               target_lang: str, threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """获取相似的翻译结果"""
        similar_translations = []
        normalized_text = self._normalize_text(text)
        
        with self.lock:
            for entry in self.cache.values():
                if (entry.source_lang == source_lang and 
                    entry.target_lang == target_lang):
                    
                    # 计算文本相似度
                    similarity = self._calculate_similarity(
                        normalized_text, 
                        self._normalize_text(entry.original_text)
                    )
                    
                    if similarity >= threshold:
                        similar_translations.append((
                            entry.original_text,
                            entry.translated_text,
                            similarity
                        ))
        
        # 按相似度排序
        similar_translations.sort(key=lambda x: x[2], reverse=True)
        return similar_translations
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单的字符级相似度）"""
        if not text1 or not text2:
            return 0.0
        
        # 使用简单的编辑距离算法
        len1, len2 = len(text1), len(text2)
        if len1 == 0:
            return 0.0 if len2 > 0 else 1.0
        if len2 == 0:
            return 0.0
        
        # 计算最长公共子序列
        dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]
        
        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        lcs_length = dp[len1][len2]
        return (2.0 * lcs_length) / (len1 + len2)
    
    def cleanup_expired(self) -> int:
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = []
        
        with self.lock:
            for key, entry in self.cache.items():
                if current_time - entry.timestamp > self.ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                self.stats['expired'] += 1
        
        if expired_keys:
            logger.debug(f"清理过期缓存: {len(expired_keys)} 个条目")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': hit_rate,
                **self.stats
            }


class TranslationOptimizer:
    """翻译优化器"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化翻译优化器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.engine = TranslationEngine(realtime_config)
        self.cache = IntelligentTranslationCache(max_size=1000)
        self.batch_optimizer = BatchTranslationOptimizer(realtime_config)
        self.fallback_handler = TranslationFallbackHandler()
        
        # 性能统计
        self.performance_stats = {
            'total_translations': 0,
            'successful_translations': 0,
            'failed_translations': 0,
            'cache_hits': 0,
            'fallback_used': 0,
            'total_time': 0.0,
            'average_time': 0.0
        }
        
        logger.debug("翻译优化器初始化完成")
    
    def translate_optimized(self, subtitle_segment: SubtitleSegment) -> SubtitleSegment:
        """
        优化的字幕段翻译
        
        Args:
            subtitle_segment: 字幕段对象
            
        Returns:
            SubtitleSegment: 翻译后的字幕段对象
        """
        if not subtitle_segment.original_text:
            return subtitle_segment
        
        start_time = time.time()
        self.performance_stats['total_translations'] += 1
        
        try:
            # 首先检查缓存
            cached_result = self.cache.get(
                subtitle_segment.original_text,
                subtitle_segment.language,
                self.config.target_language
            )
            
            if cached_result is not None:
                subtitle_segment.translated_text = cached_result
                subtitle_segment.status = "completed"
                self.performance_stats['cache_hits'] += 1
                return subtitle_segment
            
            # 尝试翻译
            translated_text = self.engine.translate_text(
                subtitle_segment.original_text,
                subtitle_segment.language
            )
            
            # 存储到缓存
            self.cache.put(
                subtitle_segment.original_text,
                translated_text,
                subtitle_segment.language,
                self.config.target_language,
                subtitle_segment.confidence
            )
            
            subtitle_segment.translated_text = translated_text
            subtitle_segment.status = "completed"
            self.performance_stats['successful_translations'] += 1
            
            return subtitle_segment
            
        except Exception as e:
            logger.warning(f"翻译失败，尝试降级处理: {subtitle_segment.segment_id}, {e}")
            
            # 使用降级处理
            fallback_result = self.fallback_handler.handle_translation_failure(
                subtitle_segment, e
            )
            
            if fallback_result != subtitle_segment.original_text:
                self.performance_stats['fallback_used'] += 1
            else:
                self.performance_stats['failed_translations'] += 1
            
            subtitle_segment.translated_text = fallback_result
            subtitle_segment.status = "error" if fallback_result == subtitle_segment.original_text else "fallback"
            
            return subtitle_segment
            
        finally:
            # 更新性能统计
            process_time = time.time() - start_time
            self.performance_stats['total_time'] += process_time
            if self.performance_stats['total_translations'] > 0:
                self.performance_stats['average_time'] = (
                    self.performance_stats['total_time'] / 
                    self.performance_stats['total_translations']
                )
    
    def translate_batch_optimized(self, subtitle_segments: List[SubtitleSegment]) -> List[SubtitleSegment]:
        """
        优化的批量翻译
        
        Args:
            subtitle_segments: 字幕段列表
            
        Returns:
            List[SubtitleSegment]: 翻译后的字幕段列表
        """
        if not subtitle_segments:
            return []
        
        logger.debug(f"开始优化批量翻译: {len(subtitle_segments)} 个字幕段")
        
        # 使用批量优化器
        return self.batch_optimizer.optimize_batch_translation(subtitle_segments, self)
    
    def preload_common_translations(self, common_phrases: List[Tuple[str, str, str]]) -> None:
        """
        预加载常用翻译
        
        Args:
            common_phrases: 常用短语列表 [(原文, 源语言, 目标语言), ...]
        """
        logger.info(f"预加载常用翻译: {len(common_phrases)} 个短语")
        
        for original, source_lang, target_lang in common_phrases:
            try:
                translated = self.engine.translate_text(original, source_lang, target_lang)
                self.cache.put(original, translated, source_lang, target_lang)
            except Exception as e:
                logger.warning(f"预加载翻译失败: {original}, {e}")
    
    def get_performance_stats(self) -> Dict[str, any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        stats.update({
            'cache_stats': self.cache.get_stats(),
            'success_rate': (
                (stats['successful_translations'] / stats['total_translations'] * 100)
                if stats['total_translations'] > 0 else 0
            )
        })
        return stats
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.cache.cleanup_expired()
            self.engine.clear_cache()
            logger.debug("翻译优化器资源清理完成")
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")


class BatchTranslationOptimizer:
    """批量翻译优化器"""
    
    def __init__(self, realtime_config: RealtimeConfig):
        """
        初始化批量翻译优化器
        
        Args:
            realtime_config: 实时处理配置
        """
        self.config = realtime_config
        self.optimal_batch_size = 5
        self.min_batch_size = 2
        self.max_batch_size = 10
        
    def optimize_batch_translation(self, subtitle_segments: List[SubtitleSegment], 
                                 optimizer: 'TranslationOptimizer') -> List[SubtitleSegment]:
        """
        优化批量翻译处理
        
        Args:
            subtitle_segments: 字幕段列表
            optimizer: 翻译优化器实例
            
        Returns:
            List[SubtitleSegment]: 翻译后的字幕段列表
        """
        # 按语言分组
        language_groups = defaultdict(list)
        for segment in subtitle_segments:
            language_groups[segment.language].append(segment)
        
        results = []
        
        # 分别处理每种语言
        for language, segments in language_groups.items():
            # 进一步按文本长度分组
            short_segments = [s for s in segments if len(s.original_text) <= 50]
            long_segments = [s for s in segments if len(s.original_text) > 50]
            
            # 短文本批量处理
            if short_segments:
                short_results = self._process_short_segments(short_segments, optimizer)
                results.extend(short_results)
            
            # 长文本单独处理
            if long_segments:
                long_results = self._process_long_segments(long_segments, optimizer)
                results.extend(long_results)
        
        # 按原始顺序排序
        segment_order = {id(seg): i for i, seg in enumerate(subtitle_segments)}
        results.sort(key=lambda x: segment_order.get(id(x), float('inf')))
        
        return results
    
    def _process_short_segments(self, segments: List[SubtitleSegment], 
                              optimizer: 'TranslationOptimizer') -> List[SubtitleSegment]:
        """处理短文本段落"""
        # 批量处理短文本
        batch_size = min(self.optimal_batch_size, len(segments))
        results = []
        
        for i in range(0, len(segments), batch_size):
            batch = segments[i:i + batch_size]
            batch_results = [optimizer.translate_optimized(seg) for seg in batch]
            results.extend(batch_results)
        
        return results
    
    def _process_long_segments(self, segments: List[SubtitleSegment], 
                             optimizer: 'TranslationOptimizer') -> List[SubtitleSegment]:
        """处理长文本段落"""
        # 长文本单独处理，避免超时
        results = []
        for segment in segments:
            result = optimizer.translate_optimized(segment)
            results.append(result)
        
        return results


class TranslationFallbackHandler:
    """翻译降级处理器"""
    
    def __init__(self):
        """初始化降级处理器"""
        self.fallback_strategies = [
            self._try_similar_translation,
            self._try_simple_replacement,
            self._return_original_text
        ]
    
    def handle_translation_failure(self, subtitle_segment: SubtitleSegment, 
                                 exception: Exception) -> str:
        """
        处理翻译失败的情况
        
        Args:
            subtitle_segment: 字幕段对象
            exception: 翻译异常
            
        Returns:
            str: 降级处理后的文本
        """
        logger.debug(f"开始降级处理: {subtitle_segment.segment_id}")
        
        for strategy in self.fallback_strategies:
            try:
                result = strategy(subtitle_segment, exception)
                if result and result != subtitle_segment.original_text:
                    logger.debug(f"降级策略成功: {strategy.__name__}")
                    return result
            except Exception as e:
                logger.debug(f"降级策略失败: {strategy.__name__}, {e}")
                continue
        
        # 所有策略都失败，返回原文
        logger.warning(f"所有降级策略失败，返回原文: {subtitle_segment.segment_id}")
        return subtitle_segment.original_text
    
    def _try_similar_translation(self, subtitle_segment: SubtitleSegment, 
                               exception: Exception) -> Optional[str]:
        """尝试使用相似的翻译结果"""
        # 这里可以实现基于相似度的翻译查找
        # 暂时返回 None，表示此策略不可用
        return None
    
    def _try_simple_replacement(self, subtitle_segment: SubtitleSegment, 
                              exception: Exception) -> Optional[str]:
        """尝试简单的词汇替换"""
        # 实现简单的词汇替换逻辑
        text = subtitle_segment.original_text
        
        # 简单的英文到中文替换
        simple_replacements = {
            'hello': '你好',
            'goodbye': '再见',
            'thank you': '谢谢',
            'yes': '是',
            'no': '不',
            'please': '请',
            'sorry': '对不起'
        }
        
        text_lower = text.lower()
        for en_word, zh_word in simple_replacements.items():
            if en_word in text_lower:
                return text.replace(en_word, zh_word).replace(en_word.capitalize(), zh_word)
        
        return None
    
    def _return_original_text(self, subtitle_segment: SubtitleSegment, 
                            exception: Exception) -> str:
        """返回原始文本"""
        return subtitle_segment.original_text