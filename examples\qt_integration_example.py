#!/usr/bin/env python3
"""
Qt集成示例

展示如何在Qt应用程序中集成v2subpy的实时翻译功能。
使用新的异步接口和Qt专用翻译器。
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent).join('examples'))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton,
                               QTextEdit, QFileDialog, QLabel,
                               QProgressBar, QMessageBox, QListWidget,
                               QListWidgetItem, QSplitter, QComboBox,
                               QSpinBox, QGroupBox, QFormLayout, QLineEdit,
                               QInputDialog)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont

from integration_helpers import QtRealtimeTranslator


class PriorityAdjustmentThread(QThread):
    """优先级调整线程"""
    finished = Signal(bool, str)  # 完成信号：(成功, 消息)

    def __init__(self, translator, task_id, target_position):
        super().__init__()
        self.translator = translator
        self.task_id = task_id
        self.target_position = target_position

    def run(self):
        """在后台线程中执行优先级调整"""
        try:
            print(f"[DEBUG] PriorityAdjustmentThread 开始执行")
            success = self.translator.adjust_translation_priority(self.task_id, self.target_position)
            if success:
                message = f"已将任务 {self.task_id[:8]} 的翻译优先级调整到 {self.target_position} 秒位置"
                self.finished.emit(True, message)
            else:
                message = "优先级调整失败，请检查任务状态"
                self.finished.emit(False, message)
        except Exception as e:
            print(f"[DEBUG] PriorityAdjustmentThread 异常: {e}")
            message = f"优先级调整时发生错误: {e}"
            self.finished.emit(False, message)


class TranslationMainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self, max_concurrent_tasks=2)
        self.current_tasks = {}
        self.priority_thread = None  # 优先级调整线程

        # 优先级调整管理
        self.last_adjustment_time = 0  # 上次成功调整的时间戳
        self.pending_adjustment = None  # 待执行的调整请求 (task_id, target_position, timestamp)
        self.adjustment_delay = 3.0  # 调整间隔（秒）

        # 延迟执行定时器
        self.delay_timer = QTimer()
        self.delay_timer.setSingleShot(True)
        self.delay_timer.timeout.connect(self._execute_pending_adjustment)

        self.init_ui()
        self.connect_signals()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"V2subpy Qt集成示例)")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧结果显示
        result_panel = self.create_result_panel()
        splitter.addWidget(result_panel)
        
        # 设置分割器比例
        splitter.setSizes([300, 700])
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 文件选择
        file_group = QGroupBox("视频文件")
        file_layout = QVBoxLayout(file_group)
        
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setWordWrap(True)
        file_layout.addWidget(self.file_path_label)
        
        self.select_file_btn = QPushButton("选择视频文件")
        self.select_file_btn.clicked.connect(self.select_video_file)
        file_layout.addWidget(self.select_file_btn)
        
        layout.addWidget(file_group)
        
        # 配置选项
        config_group = QGroupBox("翻译配置")
        config_layout = QFormLayout(config_group)
        
        self.model_combo = QComboBox()
        self.model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        self.model_combo.setCurrentText("small")
        config_layout.addRow("Whisper模型:", self.model_combo)
        
        self.target_lang_combo = QComboBox()
        self.target_lang_combo.addItems(["zh", "en", "ja", "ko", "fr", "de", "es"])
        self.target_lang_combo.setCurrentText("zh")
        config_layout.addRow("目标语言:", self.target_lang_combo)
        
        self.engine_combo = QComboBox()
        self.engine_combo.addItems(["default", "google", "openai"])
        self.engine_combo.setCurrentText("default")
        config_layout.addRow("翻译引擎:", self.engine_combo)
        
        layout.addWidget(config_group)
        
        # 控制按钮
        button_group = QGroupBox("任务控制")
        button_layout = QVBoxLayout(button_group)
        
        self.start_btn = QPushButton("开始翻译")
        self.start_btn.clicked.connect(self.start_translation)
        button_layout.addWidget(self.start_btn)
        
        self.stop_all_btn = QPushButton("停止所有任务")
        self.stop_all_btn.clicked.connect(self.stop_all_tasks)
        button_layout.addWidget(self.stop_all_btn)
        
        self.cleanup_btn = QPushButton("清理完成任务")
        self.cleanup_btn.clicked.connect(self.cleanup_finished_tasks)
        button_layout.addWidget(self.cleanup_btn)
        
        layout.addWidget(button_group)

        # 优先级调整
        priority_group = QGroupBox("优先级调整")
        priority_layout = QVBoxLayout(priority_group)

        # 时间输入
        time_input_layout = QHBoxLayout()
        time_input_layout.addWidget(QLabel("跳转时间(秒):"))
        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("例如: 300")
        time_input_layout.addWidget(self.time_input)
        priority_layout.addLayout(time_input_layout)

        # 跳转按钮
        self.jump_btn = QPushButton("调整优先级并跳转")
        self.jump_btn.clicked.connect(self.adjust_priority_and_jump)
        priority_layout.addWidget(self.jump_btn)

        layout.addWidget(priority_group)

        # 任务列表
        task_group = QGroupBox("活跃任务")
        task_layout = QVBoxLayout(task_group)
        
        self.task_list = QListWidget()
        self.task_list.itemDoubleClicked.connect(self.on_task_item_double_clicked)
        task_layout.addWidget(self.task_list)
        
        layout.addWidget(task_group)
        
        # 状态信息
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        return panel
    
    def create_result_panel(self):
        """创建结果显示面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 结果显示区域
        result_group = QGroupBox("翻译结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFont(self.get_monospace_font())
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
        
        return panel
    
    def get_monospace_font(self):
        """获取等宽字体"""
        font = QFont("Consolas")
        if not font.exactMatch():
            font = QFont("Monaco")
        if not font.exactMatch():
            font = QFont("Courier New")
        font.setPointSize(10)
        return font
    
    def connect_signals(self):
        """连接信号"""
        self.translator.subtitle_ready.connect(self.on_subtitle_ready)
        self.translator.progress_updated.connect(self.on_progress_updated)
        self.translator.error_occurred.connect(self.on_error_occurred)
        self.translator.task_finished.connect(self.on_task_finished)
        self.translator.task_started.connect(self.on_task_started)
        self.translator.task_stopped.connect(self.on_task_stopped)
    
    def select_video_file(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.wmv *.flv);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path_label.setText(file_path)
    
    def get_translation_config(self):
        """获取翻译配置"""
        return {
            'whisper_model': self.model_combo.currentText(),
            'target_language': self.target_lang_combo.currentText(),
            'translation_engine': self.engine_combo.currentText(),
            'max_retries': 3,
            'timeout': 30.0
        }
    
    def start_translation(self):
        """开始翻译"""
        video_path = self.file_path_label.text()
        
        if video_path == "未选择文件" or not Path(video_path).exists():
            QMessageBox.warning(self, "警告", "请先选择有效的视频文件")
            return
        
        try:
            config = self.get_translation_config()
            task_id = self.translator.start_translation(video_path, config)
            
            if task_id:
                self.current_tasks[task_id] = {
                    'video_path': video_path,
                    'config': config,
                    'start_time': time.time()
                }
                self.update_task_list()
                self.status_label.setText(f"启动翻译任务: {task_id}")
            else:
                QMessageBox.critical(self, "错误", "启动翻译任务失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动翻译失败: {e}")
    
    def stop_all_tasks(self):
        """停止所有任务"""
        count = self.translator.stop_all_tasks()
        self.status_label.setText(f"停止了 {count} 个任务")
        self.current_tasks.clear()
        self.update_task_list()
    
    def cleanup_finished_tasks(self):
        """清理完成的任务"""
        count = self.translator.cleanup_finished_tasks()
        self.status_label.setText(f"清理了 {count} 个完成的任务")

    def adjust_priority_and_jump(self):
        """调整优先级并跳转（带智能延迟）"""
        print(f"[DEBUG] 开始调整优先级并跳转")
        try:
            # 获取输入的时间
            time_text = self.time_input.text().strip()
            print(f"[DEBUG] 输入的时间文本: '{time_text}'")
            if not time_text:
                QMessageBox.warning(self, "警告", "请输入跳转时间")
                return

            target_position = float(time_text)
            print(f"[DEBUG] 目标位置: {target_position}秒")
            if target_position < 0:
                QMessageBox.warning(self, "警告", "时间不能为负数")
                return

            # 获取当前运行的任务
            running_tasks = self.translator.get_running_tasks()
            print(f"[DEBUG] 当前运行的任务: {running_tasks}")
            if not running_tasks:
                QMessageBox.warning(self, "警告", "没有正在运行的翻译任务")
                return

            # 如果有多个任务，让用户选择
            if len(running_tasks) > 1:
                print(f"[DEBUG] 有多个任务，需要用户选择")
                task_id, ok = self._select_task_dialog(running_tasks)
                if not ok or not task_id:
                    print(f"[DEBUG] 用户取消选择任务")
                    return
            else:
                task_id = running_tasks[0]

            print(f"[DEBUG] 选择的任务ID: {task_id}")

            # 智能调度优先级调整
            self._schedule_priority_adjustment(task_id, target_position)

        except ValueError as e:
            print(f"[DEBUG] 数值转换错误: {e}")
            QMessageBox.warning(self, "错误", "请输入有效的数字")
        except Exception as e:
            print(f"[DEBUG] 调整优先级时发生异常: {e}")
            QMessageBox.critical(self, "错误", f"调整优先级时发生错误: {e}")

    def _schedule_priority_adjustment(self, task_id: str, target_position: float):
        """智能调度优先级调整"""
        import time
        current_time = time.time()

        # 检查是否有正在进行的调整
        if self.priority_thread and self.priority_thread.isRunning():
            print(f"[DEBUG] 有正在进行的调整，记录新请求: {target_position}s")
            # 记录最新请求，覆盖之前的待执行请求
            self.pending_adjustment = (task_id, target_position, current_time)
            self.status_label.setText(f"已记录跳转请求: {target_position}s (等待执行)")
            return

        # 检查距离上次调整的时间
        time_since_last = current_time - self.last_adjustment_time

        if time_since_last >= self.adjustment_delay:
            # 距离上次调整超过1秒，立即执行
            print(f"[DEBUG] 距离上次调整 {time_since_last:.2f}s，立即执行")
            self._execute_priority_adjustment(task_id, target_position)
        else:
            # 距离上次调整不足1秒，延迟执行
            remaining_delay = self.adjustment_delay - time_since_last
            print(f"[DEBUG] 距离上次调整 {time_since_last:.2f}s，延迟 {remaining_delay:.2f}s 后执行")

            # 记录待执行请求
            self.pending_adjustment = (task_id, target_position, current_time)

            # 停止之前的定时器
            if self.delay_timer.isActive():
                self.delay_timer.stop()

            # 启动延迟定时器
            self.delay_timer.start(int(remaining_delay * 1000))  # 转换为毫秒
            self.status_label.setText(f"已记录跳转请求: {target_position}s (将在 {remaining_delay:.1f}s 后执行)")

    def _execute_pending_adjustment(self):
        """执行待执行的优先级调整"""
        if self.pending_adjustment:
            task_id, target_position, _ = self.pending_adjustment
            print(f"[DEBUG] 执行延迟的优先级调整: {target_position}s")
            self.pending_adjustment = None
            self._execute_priority_adjustment(task_id, target_position)

    def _execute_priority_adjustment(self, task_id: str, target_position: float):
        """实际执行优先级调整"""
        print(f"[DEBUG] 开始执行优先级调整: task_id={task_id}, target_position={target_position}")

        # 禁用按钮并显示进度
        self.jump_btn.setEnabled(False)
        self.jump_btn.setText("调整中...")
        self.status_label.setText(f"正在调整任务 {task_id[:8]} 的优先级到 {target_position}s...")

        # 创建并启动优先级调整线程
        self.priority_thread = PriorityAdjustmentThread(self.translator, task_id, target_position)
        self.priority_thread.finished.connect(self._on_priority_adjustment_finished)
        self.priority_thread.start()

    def _on_priority_adjustment_finished(self, success, message):
        """优先级调整完成回调"""
        import time
        print(f"[DEBUG] 优先级调整完成: success={success}, message={message}")

        # 如果成功，更新最后调整时间
        if success:
            self.last_adjustment_time = time.time()
            print(f"[DEBUG] 更新最后调整时间: {self.last_adjustment_time}")

        # 恢复按钮状态
        self.jump_btn.setEnabled(True)
        self.jump_btn.setText("调整优先级并跳转")

        # 更新状态和显示结果
        self.status_label.setText(message)

        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.warning(self, "失败", message)

        # 清理线程引用
        self.priority_thread = None

        # 检查是否有待执行的调整
        if self.pending_adjustment and success:
            # 如果有待执行的调整且当前调整成功，检查是否需要立即执行
            current_time = time.time()
            time_since_last = current_time - self.last_adjustment_time

            if time_since_last >= self.adjustment_delay:
                # 立即执行待执行的调整
                print(f"[DEBUG] 立即执行待执行的调整")
                self._execute_pending_adjustment()
            else:
                # 重新计算延迟时间
                remaining_delay = self.adjustment_delay - time_since_last
                print(f"[DEBUG] 重新设置延迟执行: {remaining_delay:.2f}s")
                if self.delay_timer.isActive():
                    self.delay_timer.stop()
                self.delay_timer.start(int(remaining_delay * 1000))

    def _select_task_dialog(self, task_ids):
        """选择任务对话框"""
        items = [f"{tid[:8]} - {self.current_tasks.get(tid, {}).get('video_path', 'Unknown')}"
                for tid in task_ids]

        item, ok = QInputDialog.getItem(
            self, "选择任务", "选择要调整优先级的任务:", items, 0, False
        )

        if ok and item:
            # 提取任务ID
            task_id = task_ids[items.index(item)]
            return task_id, True

        return None, False
    
    def on_task_item_double_clicked(self, item):
        """任务项双击事件"""
        task_id = item.data(Qt.UserRole)
        if task_id:
            # 显示任务详情或停止任务
            reply = QMessageBox.question(
                self, "任务操作", 
                f"任务 {task_id}\\n\\n选择操作:",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                self.translator.stop_translation(task_id)
    
    def on_subtitle_ready(self, task_id: str, subtitle_data: dict):
        """字幕准备就绪"""
        original_text = subtitle_data.get('original_text', '')
        translated_text = subtitle_data.get('translated_text', '')
        start_time = subtitle_data.get('start_time', 0)
        end_time = subtitle_data.get('end_time', 0)
        
        # 格式化显示
        timestamp = f"{start_time:.1f}s - {end_time:.1f}s"
        text = f"[{task_id[:8]}] {timestamp}\\n"
        text += f"原文: {original_text}\\n"
        text += f"译文: {translated_text}\\n"
        text += "-" * 50 + "\\n"
        
        self.result_text.append(text)
        
        # 自动滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)
    
    def on_progress_updated(self, task_id: str, progress_data: dict):
        """进度更新"""
        message = progress_data.get('message', '')
        progress_info = progress_data.get('progress_info', (0, 0))
        
        if len(progress_info) >= 2:
            current, total = progress_info[0], progress_info[1]
            if total > 0:
                percentage = int((current / total) * 100)
                self.progress_bar.setValue(percentage)
                self.status_label.setText(f"[{task_id[:8]}] {message} ({percentage}%)")
    
    def on_error_occurred(self, task_id: str, error_data: dict):
        """错误发生"""
        error_message = error_data.get('message', error_data.get('error', 'Unknown error'))
        error_type = error_data.get('error_type', 'Unknown')
        
        self.status_label.setText(f"错误: {error_message}")
        
        # 显示错误对话框
        QMessageBox.critical(
            self, f"翻译错误 ({error_type})", 
            f"任务 {task_id} 发生错误:\\n\\n{error_message}"
        )
        
        # 从当前任务中移除
        self.current_tasks.pop(task_id, None)
        self.update_task_list()
    
    def on_task_finished(self, task_id: str, result_data: dict):
        """任务完成"""
        self.status_label.setText(f"任务完成: {task_id}")
        self.progress_bar.setValue(100)
        
        # 从当前任务中移除
        task_info = self.current_tasks.pop(task_id, {})
        self.update_task_list()
        
        # 显示完成信息
        if task_info:
            elapsed_time = time.time() - task_info.get('start_time', 0)
            video_name = Path(task_info.get('video_path', '')).name
            
            QMessageBox.information(
                self, "任务完成",
                f"翻译完成!\\n\\n"
                f"视频: {video_name}\\n"
                f"任务ID: {task_id}\\n"
                f"耗时: {elapsed_time:.1f}秒"
            )
    
    def on_task_started(self, task_id: str):
        """任务启动"""
        self.status_label.setText(f"任务启动: {task_id}")
        self.update_task_list()
    
    def on_task_stopped(self, task_id: str):
        """任务停止"""
        self.status_label.setText(f"任务停止: {task_id}")
        self.current_tasks.pop(task_id, None)
        self.update_task_list()
    
    def update_task_list(self):
        """更新任务列表"""
        self.task_list.clear()
        
        for task_id, task_info in self.current_tasks.items():
            video_name = Path(task_info.get('video_path', '')).name
            item_text = f"{task_id[:8]} - {video_name}"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, task_id)
            self.task_list.addItem(item)
    
    def update_status(self):
        """更新状态信息"""
        try:
            stats = self.translator.get_statistics()
            running_tasks = self.translator.get_running_tasks()

            # 更新窗口标题
            title = f"V2subpy Qt集成示例 (PySide6) - "
            if stats and isinstance(stats, dict):
                max_tasks = stats.get('max_concurrent_tasks', 0)
                title += f"运行中: {len(running_tasks)}/{max_tasks}"
            else:
                title += f"运行中: {len(running_tasks)}/?"
            self.setWindowTitle(title)

        except Exception as e:
            print(f"更新状态失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.current_tasks:
            reply = QMessageBox.question(
                self, "确认退出",
                f"还有 {len(self.current_tasks)} 个任务正在运行，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.translator.stop_all_tasks()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("V2subpy Qt集成示例")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("V2subpy")
    
    # 创建主窗口
    window = TranslationMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()