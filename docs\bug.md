## v2subpy bug 记录

- [x] data目录下文件找不到， 如果程序不是当前目录运行， 需要改成相对于basedir 
- [x] > D:\scoop\apps\miniconda3\current\envs\v2subqt\lib\site-packages\whisper\tokenizer.py", line 354, in get_tokenizer
       raise ValueError(f"Unsupported language: {language}")
       ValueError: Unsupported language: auto
- [x] model 名字和规定不一致可能导致下载（model目录里为large， 但下载目标名为large-v2, 所以导致重复下载, 可以改为传文件path到whisper.load_model）
- [x] 翻译开始时， 增加提示信息
- [x] log文件创建错误
- [x] v2sub.gen_subtitles , translator_name 传递None报错， 因为类型声明为str， 需改为Optional[str]
- [x] log 里面时间错误， 后发现是 "%H:%m:%S" , 应为"%H:%M:%S"
- [x] 前台修改config后， 应该重新加载config
- [x] 前台更改device后， model应该重新加载
- [x] deepl改动页面结构， 翻译器需修改
- [x] 免费版和专业版支持后缀一样 
- [x] pyinstaller打包时不能导入v2subpy.trans包， 重构代码