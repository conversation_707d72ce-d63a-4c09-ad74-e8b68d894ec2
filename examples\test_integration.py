#!/usr/bin/env python3
"""
v2subpy 实时字幕集成测试脚本

这个脚本用于快速测试实时字幕功能是否正常工作。
包含基本的功能测试和错误处理验证。
"""

import sys
import json
import time
import tempfile
from pathlib import Path
from typing import List, Dict, Any

# 导入 v2subpy 实时翻译功能
try:
    from v2subpy.realtime import realtime_trans
    from v2subpy.realtime.models import RealtimeConfig
    from v2subpy.realtime.exceptions import RealtimeProcessingError, ConfigurationError
except ImportError as e:
    print(f"错误: 无法导入 v2subpy.realtime 模块: {e}")
    print("请确保 v2subpy 已正确安装并且实时翻译模块可用")
    sys.exit(1)


class IntegrationTester:
    """集成测试器"""
    
    def __init__(self):
        self.test_results = []
        self.received_subtitles = []
        self.received_errors = []
        self.progress_updates = []
        self.finished_called = False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 v2subpy 实时字幕集成测试")
        print("=" * 60)
        
        # 测试1: 配置类测试
        self.test_config_class()
        
        # 测试2: 异常类测试
        self.test_exception_classes()
        
        # 测试3: API接口测试
        self.test_api_interface()
        
        # 测试4: 回调函数测试
        self.test_callback_functions()
        
        # 显示测试结果
        self.show_test_results()
    
    def test_config_class(self):
        """测试配置类"""
        print("\n📋 测试配置类...")
        
        try:
            # 测试默认配置
            config = RealtimeConfig()
            assert config.segment_duration == 8.0
            assert config.whisper_model == "base"
            assert config.target_language == "zh"
            self.add_test_result("配置类默认值", True, "默认配置正确")
            
            # 测试从字典创建配置
            config_dict = {
                "segment_duration": 10.0,
                "whisper_model": "small",
                "target_language": "en",
                "invalid_field": "should_be_ignored"  # 无效字段应被忽略
            }
            config = RealtimeConfig.from_dict(config_dict)
            assert config.segment_duration == 10.0
            assert config.whisper_model == "small"
            assert config.target_language == "en"
            self.add_test_result("配置类字典创建", True, "从字典创建配置正确")
            
            # 测试配置验证
            config.validate()
            self.add_test_result("配置验证", True, "配置验证通过")
            
            # 测试无效配置
            invalid_config = RealtimeConfig(segment_duration=-1)
            try:
                invalid_config.validate()
                self.add_test_result("无效配置验证", False, "应该抛出异常但没有")
            except ValueError:
                self.add_test_result("无效配置验证", True, "正确检测到无效配置")
            
        except Exception as e:
            self.add_test_result("配置类测试", False, f"异常: {e}")
    
    def test_exception_classes(self):
        """测试异常类"""
        print("\n⚠️  测试异常类...")
        
        try:
            # 测试基础异常类
            error = RealtimeProcessingError("测试错误", segment_id="test_001", can_retry=True)
            error_dict = error.to_dict()
            
            assert error_dict['error_type'] == 'RealtimeProcessingError'
            assert error_dict['error_message'] == '测试错误'
            assert error_dict['segment_id'] == 'test_001'
            assert error_dict['can_retry'] == True
            
            # 测试JSON序列化
            error_json = error.to_json()
            parsed = json.loads(error_json)
            assert parsed['error_type'] == 'RealtimeProcessingError'
            
            self.add_test_result("基础异常类", True, "异常类功能正常")
            
            # 测试具体异常类
            from v2subpy.realtime.exceptions import (
                AudioExtractionError, SpeechRecognitionError, 
                TranslationError, ConfigurationError
            )
            
            audio_error = AudioExtractionError("音频提取失败", video_path="test.mp4")
            assert audio_error.video_path == "test.mp4"
            
            speech_error = SpeechRecognitionError("识别失败", model_name="base")
            assert speech_error.model_name == "base"
            
            trans_error = TranslationError("翻译失败", translator_type="openai", retry_count=2)
            assert trans_error.translator_type == "openai"
            assert trans_error.retry_count == 2
            
            config_error = ConfigurationError("配置错误", config_key="whisper_model")
            assert config_error.config_key == "whisper_model"
            assert config_error.can_retry == False  # 配置错误不可重试
            
            self.add_test_result("具体异常类", True, "所有异常类功能正常")
            
        except Exception as e:
            self.add_test_result("异常类测试", False, f"异常: {e}")
    
    def test_api_interface(self):
        """测试API接口"""
        print("\n🔌 测试API接口...")
        
        try:
            # 测试函数是否存在
            assert callable(realtime_trans)
            self.add_test_result("API函数存在", True, "realtime_trans 函数可调用")
            
            # 测试参数验证 - 空路径
            try:
                realtime_trans("")
                self.add_test_result("空路径验证", False, "应该抛出异常但没有")
            except (FileNotFoundError, ConfigurationError):
                self.add_test_result("空路径验证", True, "正确检测到空路径")
            
            # 测试参数验证 - 不存在的文件
            try:
                realtime_trans("nonexistent_file.mp4")
                self.add_test_result("不存在文件验证", False, "应该抛出异常但没有")
            except FileNotFoundError:
                self.add_test_result("不存在文件验证", True, "正确检测到文件不存在")
            
        except Exception as e:
            self.add_test_result("API接口测试", False, f"异常: {e}")
    
    def test_callback_functions(self):
        """测试回调函数"""
        print("\n📞 测试回调函数...")
        
        # 重置测试状态
        self.received_subtitles.clear()
        self.received_errors.clear()
        self.progress_updates.clear()
        self.finished_called = False
        
        # 创建一个临时的测试视频文件（实际上不会被处理，只是测试回调机制）
        try:
            # 测试回调函数是否被正确调用
            # 注意：这里我们故意使用一个不存在的文件来触发错误回调
            realtime_trans(
                video_path="definitely_nonexistent_file.mp4",
                config={"whisper_model": "tiny"},  # 使用最小模型加快测试
                on_subtitle_ready=self.test_subtitle_callback,
                on_progress_update=self.test_progress_callback,
                on_error=self.test_error_callback,
                on_finished=self.test_finished_callback
            )
        except Exception:
            # 预期会有异常，这是正常的
            pass
        
        # 验证错误回调是否被调用
        if self.received_errors:
            self.add_test_result("错误回调", True, f"收到 {len(self.received_errors)} 个错误回调")
        else:
            self.add_test_result("错误回调", False, "没有收到错误回调")
    
    def test_subtitle_callback(self, subtitle_dict: dict):
        """测试字幕回调"""
        self.received_subtitles.append(subtitle_dict)
        
        # 验证字幕数据结构
        required_fields = ['segment_id', 'start_time', 'end_time', 'original_text']
        for field in required_fields:
            if field not in subtitle_dict:
                self.add_test_result("字幕数据结构", False, f"缺少字段: {field}")
                return
        
        self.add_test_result("字幕回调", True, "字幕回调正常工作")
    
    def test_progress_callback(self, status_message: str, progress_info: tuple):
        """测试进度回调"""
        self.progress_updates.append((status_message, progress_info))
        self.add_test_result("进度回调", True, f"收到进度更新: {status_message}")
    
    def test_error_callback(self, error_json: str):
        """测试错误回调"""
        self.received_errors.append(error_json)
        
        # 验证错误数据格式
        try:
            error_data = json.loads(error_json)
            required_fields = ['error_type', 'error_message']
            for field in required_fields:
                if field not in error_data:
                    self.add_test_result("错误数据结构", False, f"缺少字段: {field}")
                    return
            
            self.add_test_result("错误回调", True, f"错误回调正常: {error_data['error_type']}")
        except json.JSONDecodeError:
            self.add_test_result("错误数据格式", False, "错误数据不是有效的JSON")
    
    def test_finished_callback(self):
        """测试完成回调"""
        self.finished_called = True
        self.add_test_result("完成回调", True, "完成回调被调用")
    
    def add_test_result(self, test_name: str, passed: bool, message: str):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message,
            'timestamp': time.time()
        })
        
        # 实时显示测试结果
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {status}: {test_name} - {message}")
    
    def show_test_results(self):
        """显示测试结果摘要"""
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"  - {result['name']}: {result['message']}")
        
        print("\n" + "=" * 60)
        
        if failed_tests == 0:
            print("🎉 所有测试通过！实时字幕功能集成正常。")
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
        
        return failed_tests == 0


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    dependencies = [
        ('numpy', 'numpy'),
        ('faster-whisper', 'faster_whisper'),
        ('PyQt5', 'PyQt5'),
    ]
    
    missing_deps = []
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"  ✅ {dep_name}: 已安装")
        except ImportError:
            print(f"  ❌ {dep_name}: 未安装")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖项: {', '.join(missing_deps)}")
        print("请使用以下命令安装:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        return False
    
    print("✅ 所有依赖项已安装")
    return True


def main():
    """主函数"""
    print("🚀 v2subpy 实时字幕集成测试")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败，请先安装缺少的依赖项")
        return False
    
    # 运行集成测试
    tester = IntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 建议下一步:")
        print("1. 运行 Qt 集成示例: python examples/qt_integration_example.py")
        print("2. 运行命令行示例: python examples/simple_integration_example.py your_video.mp4")
        print("3. 开始集成到你的应用中")
    else:
        print("\n🔧 建议修复:")
        print("1. 检查 v2subpy 安装是否完整")
        print("2. 确认所有依赖项已正确安装")
        print("3. 查看错误信息并修复相关问题")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)