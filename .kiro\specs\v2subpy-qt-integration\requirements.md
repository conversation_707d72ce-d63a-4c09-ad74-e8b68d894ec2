# V2subpy Qt集成优化需求文档

## 项目概述

当前v2subpy的实时翻译功能在Qt应用程序中使用时存在程序退出问题。需要优化v2subpy的架构设计，使其能够更好地与Qt等GUI框架集成，提供真正的异步、非阻塞接口。

## 问题分析

### 当前问题
1. **阻塞式设计**：`realtime_trans`函数使用阻塞等待循环 `while pipeline.is_running: time.sleep(0.5)`
2. **线程池冲突**：v2subpy内部使用`ThreadPoolExecutor`与Qt的线程管理产生冲突
3. **退出函数调用**：某些情况下v2subpy会调用`sys.exit()`导致整个应用程序退出
4. **事件循环冲突**：v2subpy的处理循环与Qt事件循环产生冲突

### 根本原因
v2subpy当前设计为独立的命令行工具，采用同步阻塞模式，不适合作为库在GUI应用程序中使用。

## 需求规格

### 需求1：异步接口设计

**用户故事**：作为GUI应用程序开发者，我希望v2subpy提供真正的异步接口，这样我可以在不阻塞主线程的情况下使用实时翻译功能。

#### 验收标准
1. 当调用实时翻译接口时，函数应该立即返回，不阻塞调用线程
2. 当翻译处理完成时，应该通过回调函数通知调用方
3. 当翻译过程中出现错误时，应该通过错误回调通知调用方
4. 当翻译进度更新时，应该通过进度回调通知调用方

### 需求2：线程安全设计

**用户故事**：作为GUI应用程序开发者，我希望v2subpy能够安全地在多线程环境中使用，不与应用程序的线程管理产生冲突。

#### 验收标准
1. 当在Qt工作线程中调用v2subpy时，不应该影响Qt主线程
2. 当多个翻译任务同时运行时，应该能够正确管理资源
3. 当翻译任务被取消时，应该能够正确清理资源
4. 当应用程序退出时，v2subpy不应该阻止程序正常退出

### 需求3：生命周期管理

**用户故事**：作为GUI应用程序开发者，我希望能够完全控制翻译任务的生命周期，包括启动、暂停、恢复和停止。

#### 验收标准
1. 当启动翻译任务时，应该返回任务句柄用于后续控制
2. 当需要暂停翻译时，应该能够暂停处理但保持资源
3. 当需要恢复翻译时，应该能够从暂停点继续处理
4. 当需要停止翻译时，应该能够立即停止并清理所有资源
5. 当查询任务状态时，应该能够获取详细的状态信息

### 需求4：错误处理优化

**用户故事**：作为GUI应用程序开发者，我希望v2subpy的错误处理不会影响我的应用程序稳定性。

#### 验收标准
1. 当v2subpy内部出现错误时，不应该调用`sys.exit()`或`os._exit()`
2. 当出现致命错误时，应该通过异常或错误回调通知调用方
3. 当出现可恢复错误时，应该自动重试或提供重试选项
4. 当错误信息需要记录时，应该使用标准的logging模块

### 需求5：资源管理优化

**用户故事**：作为GUI应用程序开发者，我希望v2subpy能够高效管理系统资源，不影响应用程序性能。

#### 验收标准
1. 当翻译任务完成时，应该自动释放所有占用的资源
2. 当多个翻译任务运行时，应该合理共享模型资源
3. 当系统资源不足时，应该优雅降级而不是崩溃
4. 当长时间运行时，不应该出现内存泄漏

## 技术实现建议

### 1. 接口重构

```python
class RealtimeTranslator:
    """异步实时翻译器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化翻译器"""
        pass
    
    def start_translation(self, video_path: str, callbacks: Dict[str, Callable]) -> str:
        """
        启动翻译任务
        
        Returns:
            str: 任务ID
        """
        pass
    
    def stop_translation(self, task_id: str) -> bool:
        """停止翻译任务"""
        pass
    
    def pause_translation(self, task_id: str) -> bool:
        """暂停翻译任务"""
        pass
    
    def resume_translation(self, task_id: str) -> bool:
        """恢复翻译任务"""
        pass
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        pass
```

### 2. 回调机制

```python
class TranslationCallbacks:
    """翻译回调接口"""
    
    def on_subtitle_ready(self, task_id: str, subtitle: Dict[str, Any]) -> None:
        """字幕准备就绪回调"""
        pass
    
    def on_progress_update(self, task_id: str, progress: Dict[str, Any]) -> None:
        """进度更新回调"""
        pass
    
    def on_error(self, task_id: str, error: Dict[str, Any]) -> None:
        """错误回调"""
        pass
    
    def on_finished(self, task_id: str, result: Dict[str, Any]) -> None:
        """完成回调"""
        pass
```

### 3. 线程模型重构

- 使用专用的后台线程池，与调用方的线程模型解耦
- 实现真正的异步处理，避免阻塞等待
- 提供线程安全的状态查询和控制接口
- 支持优雅的任务取消和资源清理

### 4. 错误处理改进

- 移除所有`sys.exit()`和`os._exit()`调用
- 使用异常和错误回调来报告错误
- 实现分级错误处理（警告、错误、致命错误）
- 提供详细的错误信息和恢复建议

## 兼容性考虑

### 向后兼容
- 保留现有的`realtime_trans`函数作为兼容接口
- 在兼容接口中使用新的异步实现
- 提供迁移指南帮助现有用户升级

### 框架集成
- 提供Qt专用的集成模块
- 提供Tkinter专用的集成模块
- 提供通用的异步集成接口

## 测试要求

### 单元测试
- 异步接口的功能测试
- 线程安全性测试
- 错误处理测试
- 资源管理测试

### 集成测试
- Qt应用程序集成测试
- 多任务并发测试
- 长时间运行稳定性测试
- 内存泄漏测试

### 性能测试
- 翻译处理性能测试
- 资源占用测试
- 并发处理能力测试

## 实施计划

### 第一阶段：核心重构
1. 重构核心处理管道，实现真正的异步处理
2. 移除所有阻塞等待和退出调用
3. 实现新的回调机制

### 第二阶段：接口优化
1. 设计并实现新的异步接口
2. 实现任务生命周期管理
3. 优化错误处理机制

### 第三阶段：集成测试
1. 完成Qt集成测试
2. 完成性能和稳定性测试
3. 编写文档和迁移指南

## 预期收益

1. **更好的用户体验**：GUI应用程序不会因为翻译处理而卡顿或崩溃
2. **更高的稳定性**：减少程序异常退出和资源泄漏问题
3. **更强的扩展性**：支持更多GUI框架和使用场景
4. **更易维护**：清晰的接口设计和错误处理机制