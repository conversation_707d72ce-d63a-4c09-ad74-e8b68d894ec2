import time
import traceback
from v2subpy import v2sub
from v2subpy.utils import config
from v2subpy.utils.common import V2subError


def test_video():
    # video_path = r"D:\test_files\repeat-video\1.mp4"
    # video_path = r"d:\test_files\fail_files\en1.mp4"
    # video_path = r"d:\test_files\fail_files\38. ICT Live Session - June 12, 2022.mp4"
    # video_path = "d:/testvideo/Bunker.mp4"
    # video_path = r"D:\testvideo\video\en1.mp4"
    video_path = r"D:\test_files\test_250328\yue\en1.mp4"
    # video_path = r"D:\v2sub_release\20250329\问题文件\Risk Event Trading – Gold – FOMC – 13.12.17.mp4"
    # video_path = r"D:\testaudio\演讲_2.mp3"
    # video_path = r"D:\testvideo\video\en1.mp4"
    # video_path = r"D:\v2sub_release\20230707\files\How To Study ICT Properly.mp3"
    # video_path = r"d:\v2sub_release\20230606\issue\08 Impulse Price Swing and Market Protraction.mp4"
    # video_path = r"d:\testvideo_dir\gachi1151_hd.wmv"
    # video_path = r"d:\testvideo\compare\iphone.mp4"
    # video_path = "d:/testvideo/iphone.mp4"
    # video_path = r"D:\dev\v2subpy\play\stable-ts\examples\demo.wav"
    source_lang = 'en'
    target_lang = 'yue'
    # translator_name = "default"
    translator_name = "gemini"
    # translator_name = "google"
    model_name = 'large-turbo'
    v2sub.init_sys()
    config.set_app_version(config.Version.ULTRA)
    # config.set_app_version(config.Version.MAX)
    try:
        start = time.time()
        report = v2sub.gen_subtitles(video_path, source_lang=source_lang,
                                     target_lang=target_lang, translator_name=translator_name,
                                     model_name=model_name, auto_retry=True)
        end = time.time()
        print(f"\n\n total  transcribe time is {end - start} seconds")
        print(report)
    except Exception as e:
        traceback.print_exc()


def test_audio():
    filepath = "d:/testaudio/audio.wav"
    # filepath = "d:/testaudio/music1.mp3"
    source_lang = 'auto'
    target_lang = 'zh'
    translator_name = 'default'
    model_name = 'small'
    v2sub.init_sys()
    config.set_app_version(config.Version.PRO)
    v2sub.init_model(model_name)
    try:
        start = time.time()
        v2sub.gen_subtitles(filepath, source_lang=source_lang,
                            target_lang=target_lang, translator_name=translator_name,
                            model_name=model_name)
        end = time.time()
        print(f"\n\n total  transcribe time is {end - start} seconds")
    except Exception as e:
        traceback.print_exc()


def test_subtitle():
    filepath = "d:/ff_video/manda.srt"
    source_lang = 'en'
    target_lang = 'zh'
    translator_name = 'default'
    model_name = 'small'
    v2sub.init_sys()
    config.set_app_version(config.Version.PRO)

    start = time.time()
    v2sub.gen_subtitles(filepath, source_lang=source_lang,
                        target_lang=target_lang, translator_name=translator_name,
                        model_name=model_name)
    end = time.time()
    print(f"\n\n total  transcribe time is {end - start} seconds")


def test_batch_trans():
    dirpath = r"D:\test_files\test_batch_srt\1"
    source_lang = 'en'
    target_lang = 'zh'
    translator_name = 'default'
    model_name = 'small'
    v2sub.init_sys()
    config.set_app_version(config.Version.ULTRA)

    start = time.time()
    v2sub.gen_subtitles(dirpath, source_lang=source_lang,
                        target_lang=target_lang, translator_name=translator_name,
                        model_name=model_name, batch_subtitle=True)
    end = time.time()
    print(f"\n\n total  transcribe time is {end - start} seconds")
