"""
Qt专用的实时翻译器封装

提供Qt信号/槽机制的翻译器，方便Qt应用程序集成。
"""

from PySide6.QtCore import QObject, Signal, QTimer
from typing import Dict, Any, Optional
import logging

from v2subpy.realtime.integrations import AsyncRealtimeTranslator

logger = logging.getLogger(__name__)

class QtRealtimeTranslator(QObject):
    """Qt专用的实时翻译器"""
    
    # Qt信号定义
    subtitle_ready = Signal(str, dict)  # task_id, subtitle_data
    progress_updated = Signal(str, dict)  # task_id, progress_data
    error_occurred = Signal(str, dict)  # task_id, error_data
    task_finished = Signal(str, dict)  # task_id, result_data
    task_started = Signal(str)  # task_id
    task_stopped = Signal(str)  # task_id
    task_paused = Signal(str)  # task_id
    task_resumed = Signal(str)  # task_id
    
    def __init__(self, parent=None, max_concurrent_tasks: int = 2):
        """
        初始化Qt翻译器
        
        Args:
            parent: Qt父对象
            max_concurrent_tasks: 最大并发任务数
        """
        super().__init__(parent)
        self.translator = AsyncRealtimeTranslator(max_concurrent_tasks)
        self.active_timers: Dict[str, QTimer] = {}
        
        logger.info(f"初始化Qt翻译器 ，最大并发任务数: {max_concurrent_tasks}")
    
    def start_translation(self, video_path: str, config: Optional[Dict[str, Any]] = None) -> str:
        """
        启动翻译任务
        
        Args:
            video_path: 视频文件路径
            config: 翻译配置字典
        
        Returns:
            str: 任务ID，如果启动失败则返回空字符串
        """
        try:
            # 创建回调函数
            callbacks = {
                'on_subtitle_ready': self._on_subtitle_ready,
                'on_progress_update': self._on_progress_update,
                'on_error': self._on_error,
                'on_finished': self._on_finished
            }
            
            # 启动翻译
            task_id = self.translator.start_translation(video_path, config, callbacks)
            
            # 使用QTimer.singleShot创建定时器，确保在主线程中
            self._create_task_timer(task_id)
            
            # 发射任务启动信号
            self.task_started.emit(task_id)
            
            logger.info(f"Qt翻译任务启动成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Qt翻译任务启动失败: {e}")
            # 发射错误信号
            self.error_occurred.emit("", {"error": str(e), "error_type": "startup_error"})
            return ""
    
    def stop_translation(self, task_id: str) -> bool:
        """
        停止翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功停止
        """
        try:
            success = self.translator.stop_translation(task_id)
            
            if success:
                self._cleanup_task_timer(task_id)
                self.task_stopped.emit(task_id)
                logger.info(f"Qt翻译任务停止成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Qt翻译任务停止失败: {task_id}, {e}")
            return False
    
    def pause_translation(self, task_id: str) -> bool:
        """
        暂停翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功暂停
        """
        try:
            success = self.translator.pause_translation(task_id)
            
            if success:
                self.task_paused.emit(task_id)
                logger.info(f"Qt翻译任务暂停成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Qt翻译任务暂停失败: {task_id}, {e}")
            return False
    
    def resume_translation(self, task_id: str) -> bool:
        """
        恢复翻译任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            success = self.translator.resume_translation(task_id)
            
            if success:
                self.task_resumed.emit(task_id)
                logger.info(f"Qt翻译任务恢复成功: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Qt翻译任务恢复失败: {task_id}, {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            Optional[Dict[str, Any]]: 任务状态字典
        """
        return self.translator.get_task_status(task_id)
    
    def list_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有任务
        
        Returns:
            Dict[str, Dict[str, Any]]: 任务ID到状态字典的映射
        """
        return self.translator.list_tasks()
    
    def get_running_tasks(self) -> list:
        """
        获取所有运行中的任务ID
        
        Returns:
            list: 运行中的任务ID列表
        """
        return self.translator.get_running_tasks()
    
    def stop_all_tasks(self) -> int:
        """
        停止所有任务
        
        Returns:
            int: 成功停止的任务数量
        """
        stopped_count = self.translator.stop_all_tasks()
        
        # 清理所有定时器
        for task_id in list(self.active_timers.keys()):
            self._cleanup_task_timer(task_id)
        
        return stopped_count
    
    def cleanup_finished_tasks(self) -> int:
        """
        清理已完成的任务
        
        Returns:
            int: 清理的任务数量
        """
        return self.translator.cleanup_finished_tasks()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        return self.translator.get_statistics()

    def adjust_translation_priority(self, task_id: str, target_position: float) -> bool:
        """
        调整翻译队列优先级

        Args:
            task_id: 任务ID
            target_position: 目标播放位置（秒）

        Returns:
            bool: 是否调整成功
        """
        print(f"[DEBUG] QtRealtimeTranslator.adjust_translation_priority 被调用: task_id={task_id}, target_position={target_position}")
        try:
            success = self.translator.adjust_translation_priority(task_id, target_position)
            print(f"[DEBUG] QtRealtimeTranslator 调用结果: {success}")

            if success:
                logger.info(f"Qt翻译优先级调整成功: {task_id}, 目标位置: {target_position}s")
            else:
                logger.warning(f"Qt翻译优先级调整失败: {task_id}")

            return success

        except Exception as e:
            print(f"[DEBUG] QtRealtimeTranslator 调用异常: {e}")
            logger.error(f"Qt翻译优先级调整异常: {task_id}, {e}")
            return False
    
    def _on_subtitle_ready(self, task_id: str, subtitle_data: Dict[str, Any]):
        """字幕准备就绪回调（线程安全）"""
        # Qt信号是线程安全的，可以从任何线程发射
        self.subtitle_ready.emit(task_id, subtitle_data)
    
    def _on_progress_update(self, task_id: str, progress_data: Dict[str, Any]):
        """进度更新回调（线程安全）"""
        # Qt信号是线程安全的，可以从任何线程发射
        self.progress_updated.emit(task_id, progress_data)
    
    def _on_error(self, task_id: str, error_data: Dict[str, Any]):
        """错误回调"""
        self.error_occurred.emit(task_id, error_data)
        # 延迟清理定时器，避免跨线程问题
        self._schedule_timer_cleanup(task_id)
    
    def _on_finished(self, task_id: str, result_data: Dict[str, Any]):
        """完成回调"""
        self.task_finished.emit(task_id, result_data)
        # 延迟清理定时器，避免跨线程问题
        self._schedule_timer_cleanup(task_id)
    
    def _monitor_task_status(self, task_id: str):
        """监控任务状态"""
        status = self.get_task_status(task_id)
        if not status:
            # 任务不存在，清理定时器
            self._cleanup_task_timer(task_id)
            return
        
        # 检查任务是否已完成
        task_status = status.get('status', '')
        if task_status in ['completed', 'error', 'cancelled']:
            self._cleanup_task_timer(task_id)
    
    def _schedule_timer_cleanup(self, task_id: str):
        """安排定时器清理（线程安全）"""
        try:
            from PySide6.QtCore import QTimer as QtTimer
        except ImportError:
            try:
                from PyQt6.QtCore import QTimer as QtTimer
            except ImportError:
                from PyQt5.QtCore import QTimer as QtTimer
        
        # 使用QTimer.singleShot在主线程中执行清理
        QtTimer.singleShot(0, lambda: self._cleanup_task_timer(task_id))
    
    def _create_task_timer(self, task_id: str):
        """创建任务监控定时器（确保在主线程中）"""
        try:
            from PySide6.QtCore import QTimer as QtTimer, QThread
        except ImportError:
            try:
                from PyQt6.QtCore import QTimer as QtTimer, QThread
            except ImportError:
                from PyQt5.QtCore import QTimer as QtTimer, QThread
        
        def create_timer():
            try:
                if task_id not in self.active_timers:  # 避免重复创建
                    # 检查是否在主线程中
                    if QThread.currentThread() == self.thread():
                        timer = QTimer(self)
                        timer.timeout.connect(lambda: self._monitor_task_status(task_id))
                        timer.start(500)  # 每500ms检查一次状态
                        self.active_timers[task_id] = timer
                        logger.debug(f"创建任务定时器: {task_id}")
                    else:
                        logger.warning(f"尝试在非主线程中创建定时器: {task_id}")
                        # 如果不在主线程，再次尝试调度到主线程
                        QtTimer.singleShot(100, create_timer)
            except Exception as e:
                logger.error(f"创建定时器失败: {task_id}, {e}")
        
        # 确保在主线程中创建定时器
        if QThread.currentThread() == self.thread():
            create_timer()
        else:
            QtTimer.singleShot(0, create_timer)
    
    def _cleanup_task_timer(self, task_id: str):
        """清理任务定时器（在主线程中调用）"""
        if task_id in self.active_timers:
            timer = self.active_timers[task_id]
            timer.stop()
            timer.deleteLater()
            del self.active_timers[task_id]
            logger.debug(f"清理任务定时器: {task_id}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop_all_tasks()
        except:
            pass


# 便捷函数
def create_qt_translator(**kwargs):
    """
    创建Qt翻译器的便捷函数
    
    Args:
        **kwargs: 传递给QtRealtimeTranslator的参数
    
    Returns:
        QtRealtimeTranslator: Qt翻译器实例
    
    Raises:
        ImportError: 如果Qt库不可用
    """
    return QtRealtimeTranslator(**kwargs)

