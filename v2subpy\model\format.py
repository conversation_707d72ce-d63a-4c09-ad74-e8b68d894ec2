'''
处理转录结果的模块
包括合并行，调整等函数
'''
import string
import time
import sys
import re


def cjk_detect(texts):
    # korean
    if re.search("[\uac00-\ud7a3]", texts):
        return "ko"
    # japanese
    if re.search("[\u3040-\u30ff]", texts):
        return "ja"
    # chinese
    if re.search("[\u4e00-\u9FFF]", texts):
        return "zh"
    return None


def is_cjk(c):
    if re.search("[\uac00-\ud7a3]", c) or re.search("[\u3040-\u30ff]", c) or re.search("[\u4e00-\u9FFF]", c):
        return True
    return False


def qualify_for_merge(p, nextp, maxgap, max_length):
    if p != None and p.text != None and nextp != None and nextp.text != None:
        s = p.text
        next_text = nextp.text
        if len(s) + len(next_text) < max_length and total_milli_seconds(nextp.start - p.end) < maxgap:
            if s == '':
                return True

            isLineContinuation = s.endswith(',') \
                or s.endswith('-') \
                or s.endswith("...") \
                or s.endswith("…") \
                or s[-1] in string.ascii_letters

            return isLineContinuation

    return False


def has_sentence_ending(value: str, language_code: str = 'en') -> bool:
    if not value:
        return False

    last = value[-1]
    return last in ['.', '!', '?', ']', ')', '…', '♪', '؟'] or \
        (language_code == "el" and last == ';') or \
        (language_code == "el" and last == '\u037E') or \
        (last == '-' and len(value) > 3 and value.endswith("--")) and value[-3].isalpha() or \
        (last == '—' and len(value) > 2 and value[-2].isalpha())


def total_milli_seconds(senonds):
    return senonds * 1000


def wrap_line(text: str, line_wrap_limit: int = 55) -> str:
    """Wraps a line of text without breaking any word in half

    Args:
        text (str): Line text to wrap
        line_wrap_limit (int): Number of maximum characters in a line before wrap. Defaults to 50.

    Returns:
        str: Text line wraped
    """
    text = text.replace('\n', ' ').replace('  ', ' ')
    wraped_lines = []
    for word in text.split():
        # Check if inserting a word in the last sentence goes beyond the wrap limit
        if (
            len(wraped_lines) != 0
            and len(wraped_lines[-1]) + len(word) < line_wrap_limit
        ):
            # If not, add it to it
            wraped_lines[-1] += f" {word}"
            continue

        # Insert a new sentence
        wraped_lines.append(f"{word}")

    # Join sentences with line break
    return "\n".join(wraped_lines)


def merge_sentences(segments):
    language = 'en'
    merged_subtitles = []
    merged_lines = []
    index = 0
    next_para = None
    mini_gap = 250
    max_length = 75
    while index < len(segments):
        paragraph = next_para or segments[index]
        index += 1
        if index >= len(segments):
            merged_subtitles.append(paragraph)
            break
        next_para = segments[index]
        if (has_sentence_ending(paragraph.text) or total_milli_seconds(next_para.start - paragraph.end) > mini_gap
                or len(next_para.text) + len(paragraph.text) >= max_length):
            merged_subtitles.append(paragraph)
            continue
        # if not qualify_for_merge(paragraph, next_para, maxgap=250, max_length=60):
        #     merged_subtitles.append(paragraph)
        #     continue
        # next_para.Text = Utilities.UnbreakLine(
        #     paragraph.Text + '\n' + next_para.Text)
        merged_text = paragraph.text + '\n' + next_para.text
        # next_para = next_para._replace(text=merged_text, start=paragraph.start)
        next_para.start = paragraph.start
        next_para.text = merged_text
        merged_lines.append((paragraph.id+1, next_para.id+1))
    # _subtitle.Paragraphs.clear()
    # _subtitle.Paragraphs.extend(merged_subtitle.Paragraphs)
    # _subtitle.Renumber()
    for ids in merged_lines:
        print(ids)
    for p in merged_subtitles:
        p.text = wrap_line(p.text)
    return merged_subtitles
