# V2subpy集成辅助工具

这个目录包含了帮助将v2subpy集成到各种GUI框架中的辅助工具和示例代码。

## 文件说明

- `qt_translator.py` - Qt专用的翻译器封装类
- `README.md` - 本文件，集成指南

## Qt集成

### 基本用法

```python
from examples.integration_helpers import QtRealtimeTranslator
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject

class MyApp(QObject):
    def __init__(self):
        super().__init__()
        self.translator = QtRealtimeTranslator(self)
        
        # 连接信号
        self.translator.subtitle_ready.connect(self.on_subtitle_ready)
        self.translator.progress_updated.connect(self.on_progress_updated)
        self.translator.error_occurred.connect(self.on_error)
        self.translator.task_finished.connect(self.on_finished)
    
    def start_translation(self, video_path):
        config = {"whisper_model": "small", "target_language": "zh"}
        task_id = self.translator.start_translation(video_path, config)
        return task_id
    
    def on_subtitle_ready(self, task_id, subtitle_data):
        print(f"新字幕: {subtitle_data}")
    
    def on_progress_updated(self, task_id, progress_data):
        print(f"进度更新: {progress_data}")
    
    def on_error(self, task_id, error_data):
        print(f"错误: {error_data}")
    
    def on_finished(self, task_id, result_data):
        print(f"任务完成: {task_id}")
```

## 其他框架集成

### Tkinter集成

可以参考Qt集成的模式，创建类似的Tkinter集成辅助类。

### 通用集成

对于其他框架，可以直接使用v2subpy的通用异步接口：

```python
from v2subpy.realtime.integrations import AsyncRealtimeTranslator

translator = AsyncRealtimeTranslator()
task_id = translator.start_translation(
    video_path="video.mp4",
    config={"whisper_model": "small"},
    callbacks={
        "on_subtitle_ready": my_subtitle_callback,
        "on_progress_update": my_progress_callback,
        "on_error": my_error_callback,
        "on_finished": my_finished_callback
    }
)
```

## 最佳实践

1. **线程安全**: 所有回调函数都在后台线程中执行，如果需要更新UI，请使用相应框架的线程安全机制
2. **资源管理**: 记得在应用程序退出时停止所有翻译任务
3. **错误处理**: 始终处理错误回调，提供用户友好的错误信息
4. **性能优化**: 对于长视频，考虑分段处理以提高响应性