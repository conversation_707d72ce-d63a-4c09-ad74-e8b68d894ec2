#!/usr/bin/env python3
"""
简化的实时翻译测试

用于快速验证实时翻译功能是否正常工作。
"""

import sys
import time
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

# 启用调试日志
os.environ['V2SUB_DEBUG'] = '1'

def test_simple_realtime():
    """简化的实时翻译测试"""
    print("🎬 简化实时翻译测试")
    print("=" * 50)
    
    VIDEO_PATH = r"d:\testvideo\en1.mp4"
    
    # 检查视频文件
    if not Path(VIDEO_PATH).exists():
        print(f"❌ 视频文件不存在: {VIDEO_PATH}")
        return False
    
    # 简化配置
    config = {
        'whisper_model': 'medium',  # 使用较小的模型
        'translator_type': 'default',
        'target_language': 'zh',
        'segment_duration': 15.0,  # 较长的段落，减少处理量
        'max_concurrent_segments': 1,  # 单线程处理
        'buffer_size': 3,  # 小缓冲区
    }
    
    # 计数器
    subtitle_count = 0
    progress_count = 0
    error_count = 0
    finished = False
    
    def on_subtitle_ready(subtitle_dict):
        nonlocal subtitle_count
        subtitle_count += 1
        print(f"\n🎯 字幕 #{subtitle_count}:")
        print(f"   时间: {subtitle_dict['start_time']:.1f}s - {subtitle_dict['end_time']:.1f}s")
        print(f"   原文: {subtitle_dict['original_text']}")
        print(f"   译文: {subtitle_dict['translated_text']}")
        print(f"   状态: {subtitle_dict['status']}")
        print(f"   置信度: {subtitle_dict.get('confidence', 0):.2f}")
    
    def on_progress_update(status_message, progress_info):
        nonlocal progress_count
        progress_count += 1
        current, total = progress_info
        percentage = (current / total * 100) if total > 0 else 0
        print(f"\r📊 进度 #{progress_count}: {percentage:.1f}% - {status_message}", 
              end="", flush=True)
    
    def on_error(error_message):
        nonlocal error_count
        error_count += 1
        print(f"\n❌ 错误 #{error_count}: {error_message}")
    
    def on_finished():
        nonlocal finished
        finished = True
        print(f"\n✅ 处理完成")
    
    try:
        from v2subpy.realtime import realtime_trans
        
        print(f"🚀 开始处理: {VIDEO_PATH}")
        print(f"📋 配置: {config}")
        
        start_time = time.time()
        
        # 启动处理
        realtime_trans(
            video_path=VIDEO_PATH,
            config=config,
            on_subtitle_ready=on_subtitle_ready,
            on_progress_update=on_progress_update,
            on_error=on_error,
            on_finished=on_finished
        )
        
        end_time = time.time()
        
        print(f"\n📊 测试结果:")
        print(f"  - 处理时间: {end_time - start_time:.1f}s")
        print(f"  - 字幕数量: {subtitle_count}")
        print(f"  - 进度更新: {progress_count}")
        print(f"  - 错误数量: {error_count}")
        print(f"  - 是否完成: {finished}")
        
        if subtitle_count > 0:
            print("✅ 测试成功：收到了字幕")
            return True
        else:
            print("❌ 测试失败：没有收到字幕")
            return False
            
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_simple_realtime()
        if success:
            print("\n🎉 简化测试成功！")
        else:
            print("\n😞 简化测试失败")
        return success
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        return False

if __name__ == "__main__":
    main()