# load file based on basedir
from pathlib import Path
import shutil
import sys
import os
# basedir = 'd:/dev/v2subpy'
basedir = ''


def set_basedir(base_file):
    global basedir
    basedir = str(Path(base_file).parent)
    print('set basedir', basedir)


def get_file(path):
    abs_path = Path(basedir).joinpath(path)
    return str(abs_path)

def user_data_dir(file_name: str) -> Path:
    r"""
    Get OS specific data directory path for SwagLyrics.

    Typical user data directories are:
        macOS:    ~/Library/Application Support/SwagLyrics
        Unix:     ~/.local/share/SwagLyrics   # or in $XDG_DATA_HOME, if defined
        Win 10:   C:\Users\<USER>\AppData\Local\SwagLyrics
    For Unix, we follow the XDG spec and support $XDG_DATA_HOME if defined.
    :param file_name: file to be fetched from the data dir
    :return: full path to the user-specific data dir
    """
    # get os specific path
    if sys.platform.startswith("win"):
        os_path = os.getenv("LOCALAPPDATA")
    elif sys.platform.startswith("darwin"):
        os_path = "~/Library/Application Support"
    else:
        # linux
        os_path = os.getenv("XDG_DATA_HOME", "~/.local/share")

    # join with SwagLyrics dir
    app_dir = "v2sub"
    path = Path(os_path) / app_dir
    path = path.expanduser()
    if not path.exists():
        path.mkdir(parents=True, exist_ok=True)
    file_path = path / file_name
    return file_path

def copy_model_dir(app_dir):
    '''
    复制data目录到user data dir 下面
    '''
    if sys.platform.startswith("win"):
        # windows 不需要此操作
        return
    app_dir = Path(app_dir)
    model_path = 'data/model'
    model_dir = str(app_dir / model_path)
    target_dir = user_data_dir('model')
    print(f'{model_dir=}')
    print(f'{target_dir=}')

    try:
        model_link = Path(model_dir)
        # 检查是否符号链接, 不是的话需要删除重建符号连接
        if not model_link.is_symlink():
            # 复制model目录及以下文件
            shutil.copytree(model_dir, str(target_dir), dirs_exist_ok=True)
            print(f'copy model folder to {str(target_dir)}')
            shutil.rmtree(model_dir)
            model_link.symlink_to(target_dir, target_is_directory=True)
            print(f'create symlink to {str(target_dir)}')
    except:
        print('copy error')