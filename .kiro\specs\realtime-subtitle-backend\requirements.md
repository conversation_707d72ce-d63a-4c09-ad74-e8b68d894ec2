# v2subpy 实时字幕后端服务需求文档

## 项目简介

本文档描述了在 `v2subpy` 中实现实时字幕处理后端服务的需求，该服务将为 `v2subqt` 的实时字幕播放器功能提供支持。

## 架构概述

**后端职责（v2subpy）：**
- 提供统一的实时翻译接口 `realtime_trans(video_path, config)`
- 自动处理音频提取、分段、语音识别、翻译的完整流程
- 通过 Qt 信号机制实时发送处理结果给前端
- 管理处理状态、错误处理和资源清理

**前端职责（v2subqt）：**
- 通过 Qt Worker Thread 调用后端接口
- 接收后端发送的字幕段信号
- 负责字幕的缓存、合并、时间对齐和显示
- 管理播放器界面和用户交互

**通信方式：**
- 前端通过 Worker Thread 调用后端函数
- 后端通过 Qt 信号实时推送字幕段给前端
- 使用共享的配置对象传递参数

## 需求

### 需求 1：统一实时翻译接口

**用户故事：** 作为前端应用程序（v2subqt），我希望能够调用一个统一的后端接口来启动实时字幕翻译，该接口自动处理完整的音频提取、识别、翻译流程。

#### 验收标准

1. WHEN 前端调用 `realtime_trans(video_path, config)` THEN 后端 SHALL 启动完整的实时处理流程
2. WHEN 处理流程启动 THEN 后端 SHALL 自动进行音频提取、分段、语音识别、翻译
3. WHEN 每个字幕段处理完成 THEN 后端 SHALL 通过 Qt 信号发送给前端
4. WHEN 处理过程中发生错误 THEN 后端 SHALL 通过信号通知前端错误信息
5. IF 视频文件无效 THEN 后端 SHALL 立即抛出异常并停止处理

### 需求 2：Qt 信号通信机制

**用户故事：** 作为前端应用程序，我希望能够通过 Qt 信号实时接收后端处理的字幕段，以便及时显示给用户。

#### 验收标准

1. WHEN 字幕段处理完成 THEN 后端 SHALL 发送包含时间、原文、译文的信号
2. WHEN 处理状态变化 THEN 后端 SHALL 发送进度更新信号
3. WHEN 发生错误 THEN 后端 SHALL 发送错误信号包含详细错误信息
4. WHEN 处理完成 THEN 后端 SHALL 发送完成信号
5. IF 需要停止处理 THEN 前端 SHALL 能够发送停止信号给后端

### 需求 3：前端字幕管理职责

**用户故事：** 作为前端应用程序，我希望能够高效管理接收到的字幕段，以便与视频播放同步显示。

#### 验收标准

1. WHEN 接收到字幕段信号 THEN 前端 SHALL 将字幕段存储到本地缓冲区
2. WHEN 字幕段时间重叠 THEN 前端 SHALL 进行智能合并处理
3. WHEN 播放器请求字幕 THEN 前端 SHALL 根据时间戳返回对应字幕
4. WHEN 字幕显示延迟过大 THEN 前端 SHALL 进行时间对齐调整
5. IF 缓冲区容量不足 THEN 前端 SHALL 清理过期的字幕段

### 需求 5：错误处理和恢复

**用户故事：** 作为用户，我希望系统能够自动处理各种错误情况，确保实时翻译过程的稳定性。

#### 验收标准

1. WHEN 翻译引擎失败 THEN 后端 SHALL 自动重试并通过信号通知前端状态
2. WHEN 音频提取失败 THEN 后端 SHALL 跳过该段并继续处理后续段落
3. WHEN 发生关键错误 THEN 后端 SHALL 通过错误信号通知前端详细信息
4. WHEN 网络连接中断 THEN 后端 SHALL 暂停处理并等待网络恢复
5. IF 系统资源不足 THEN 后端 SHALL 降低处理速度并通知前端

### 需求 6：配置和集成

**用户故事：** 作为用户，我希望实时翻译功能能够复用现有的配置，保持与批处理模式的一致性。

#### 验收标准

1. WHEN 启动实时翻译 THEN 后端 SHALL 使用现有的 faster-whisper 配置
2. WHEN 进行翻译 THEN 后端 SHALL 支持所有现有的翻译引擎配置
3. WHEN 处理完成 THEN 后端 SHALL 可选择保存完整字幕文件
4. WHEN 配置参数传递 THEN 前端 SHALL 通过 config 对象传递所有必要参数
5. IF 配置缺失 THEN 后端 SHALL 使用合理的默认值

### 需求 7：性能和资源管理

**用户故事：** 作为用户，我希望实时翻译过程高效稳定，不会影响系统整体性能。

#### 验收标准

1. WHEN 处理音频段 THEN 后端 SHALL 控制并发处理数量避免资源过载
2. WHEN 内存使用增长 THEN 后端 SHALL 及时清理已处理的临时数据
3. WHEN 处理速度跟不上播放 THEN 后端 SHALL 优先处理当前播放位置附近的段落
4. WHEN 系统负载高 THEN 后端 SHALL 动态调整处理优先级
5. IF 资源严重不足 THEN 后端 SHALL 暂停处理并通知前端

### 需求 4：Worker Thread 集成

**用户故事：** 作为前端开发者，我希望能够通过 Qt Worker Thread 调用后端处理，以便不阻塞 UI 线程。

#### 验收标准

1. WHEN 前端启动实时翻译 THEN 前端 SHALL 在 Worker Thread 中调用后端接口
2. WHEN 后端处理运行 THEN 后端 SHALL 在独立线程中执行避免阻塞
3. WHEN 需要停止处理 THEN 前端 SHALL 能够安全中断 Worker Thread
4. WHEN 线程间通信 THEN 后端 SHALL 使用线程安全的 Qt 信号机制
5. IF Worker Thread 异常 THEN 前端 SHALL 能够捕获并处理异常

### 需求 8：日志记录和调试

**用户故事：** 作为开发者，我希望有详细的日志记录，以便调试和优化实时翻译功能。

#### 验收标准

1. WHEN 处理每个音频段 THEN 后端 SHALL 记录处理时间和状态
2. WHEN 发生错误 THEN 后端 SHALL 记录详细的错误信息和上下文
3. WHEN 翻译引擎调用 THEN 后端 SHALL 记录请求和响应的关键信息
4. WHEN 性能出现问题 THEN 后端 SHALL 记录资源使用情况
5. IF 调试模式开启 THEN 后端 SHALL 提供更详细的处理流程日志