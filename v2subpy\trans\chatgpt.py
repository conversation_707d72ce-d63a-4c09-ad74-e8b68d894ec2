"""
translator use chatgpt
"""
import json
import sys
import os
from pathlib import Path
import random
from urllib.parse import urljoin
import time
from enum import Enum
from typing import List, Union, Dict, Callable, Any
import asyncio
import tiktoken
import httpx
from openai import OpenAI
import openai
from v2subpy.sub.subutils import handle_chinese_text
from v2subpy.trans.base import BaseTranslator, BaseBot, TimeOutException, TranslationError, ChatBotException
from v2subpy.utils import config
from v2subpy.utils.config import get_text
from v2subpy.trans import langutil
from v2subpy.trans.gpt_prompt import prompter_map, BaseTranslatePrompter
from v2subpy.utils.log import logger
from v2subpy.utils.version_manage import VersionManager, Fn
from v2subpy.utils.common import GptDomainTypeEnum, GptModelEnum, GptTemperatureEnum, write_file,  get_extra_config, LLMMaxcharEnum


class GptConfigEnum(str, Enum):
    OPENAI_BASE_URL = "OPENAI_BASE_URL"
    OPENAI_API_KEY = "OPENAI_API_KEY"
    OPENAI_PROXY = "OPENAI_PROXY"
    OPENAI_TIMEOUT_TUPLE = 'OPENAI_TIMEOUT_TUPLE'

    def __str__(self) -> str:
        return str.__str__(self)


# Pricing for 1k tokens, info from https://openai.com/pricing
GPT_PRICE_MAP = {
    GptModelEnum.GPT_3__5_TURBO: (0.001, 0.002),
    GptModelEnum.GPT_4: (0.01, 0.03)
}


class ChatgptTranslator(BaseTranslator):
    def __init__(self, **kwargs):
        # need api_key
        super().__init__(**kwargs)
        self.setup_openai(self.translator_config.get('api_key'))
        self.translator = GPTTranslator(
            fee_limit=5, chunk_size=30, translator_config=self.translator_config)

    def setup_translator_config(self):
        self.translator_config["wait_for_retry"] = 30
        # 增加gpt 超时时间设置， 60秒连接， 200秒读取
        self.translator_config["timeout"] = (60, 200)
        if langutil.is_cjk_lang(self.source_lang):
            self.translator_config["max_char"] = LLMMaxcharEnum.LOCAL_LLM_MAX_CJK.value
        else:  
            self.translator_config["max_char"] = LLMMaxcharEnum.LOCAL_LLM_MAX_EN.value

    def setup_api_domain(self):
        '''
        设置需要的api domain地址
        根据gpt_domain_type
        '''
        # api domain
        gpt_domain_type = self.translator_config.get('gpt_domain_type')
        api_domain = None

        if gpt_domain_type == GptDomainTypeEnum.openai:
            # 不用改变， 返回
            return

        if gpt_domain_type == GptDomainTypeEnum.v2sub:
            # 使用v2sub 自动配置域名
            extra_cfg = get_extra_config()
            if extra_cfg:
                api_domain_list = extra_cfg['api_domain_list']
                api_domain = random.choice(api_domain_list)
        else:
            # 使用自定义域名
            api_domain = self.translator_config.get('api_domain')

        if api_domain:
            self.replace_openai_domain(api_domain)

    def replace_openai_domain(self, api_domain:str):
        '''
        替换openai 的api domain
        '''
        api_suffix = '/chat/completions'
        api_version = 'v1'
        if api_domain.endswith(api_suffix):
            api_domain = api_domain.replace(api_suffix, '')
        if not api_domain.endswith(api_version):
            api_domain = urljoin(api_domain, api_version)
        os.environ[GptConfigEnum.OPENAI_BASE_URL] = api_domain
        logger.debug(f'{api_domain=}')

    def setup_openai(self, api_key: str) -> None:
        logger.debug(f'setup openai: {api_key}')
        os.environ[GptConfigEnum.OPENAI_API_KEY] = api_key
        self.setup_api_domain()
        if self.proxies:
            os.environ[GptConfigEnum.OPENAI_PROXY] = self.proxies['http']

    def _translate(self, text, source_lang: str, target_lang: str):

        source_lang = langutil.get_lang_longname(source_lang)
        target_lang = langutil.get_lang_longname(target_lang)
        txt_list = text.strip().split('\n')
        try:
            trans_list = self.translator.translate(txt_list, src_lang=source_lang, target_lang=target_lang)
            logger.debug(
                f'lines , before: {len(txt_list)}, after: {len(trans_list)}')
            translation = '\n'.join(trans_list)
            return translation
        except ChatBotException as e:
            raise TranslationError(
                config.get_text("error.TranslationError").format(str(e)), **e._args)

    def finished(self, output_file):
        total_fee = self.translator.api_fee
        # logger.info(config.get_text('info.ChatgptTotalFee').format(total_fee))
        if VersionManager.check_fn(Fn.GPT_SUMMARY):
            if self.translator_config.get(Fn.GPT_SUMMARY):
                pf = Path(output_file)
                pf = pf.with_stem(f'{pf.stem}_summary')
                pf = pf.with_suffix('.txt')
                self.write_summary(str(pf))

    def write_summary(self, filepath):
        summaries = self.remove_duplicates_ordered(self.translator.summaries)
        text_summary = '\n'.join(summaries)
        logger.debug(f'summary: \n {text_summary}')
        scenes = self.remove_duplicates_ordered(self.translator.scenes)
        text_scene = '\n'.join(scenes)
        logger.debug(f'scene: \n{text_scene}')
        if self.target_lang == 'zh':
            text_summary = handle_chinese_text(text_summary)
        write_file(filepath, text_summary)
        logger.info(config.get_text("info.ChatGptSummary").format(filepath))


def get_text_token_number(text: str, model: str = "gpt-3.5-turbo") -> int:
    tokens = tiktoken.encoding_for_model(model).encode(text)

    return len(tokens)


def get_messages_token_number(messages: List[Dict[str, Any]], model: str = "gpt-3.5-turbo") -> int:
    total = sum([get_text_token_number(element['content'], model=model)
                for element in messages])

    return total


class GPTBot:
    def __init__(self, model=GptModelEnum.GPT_4O_MINI.value, temperature=1, top_p=1, retry=1, max_async=16, json_mode=False,
                 fee_limit=0.05, timeout=(60, 200)):
        logger.debug(f'use gpt model: {model}')
        client_args = {}
        client_args['timeout'] = timeout
        logger.debug(
            f'use timeout config: {timeout}')
        if GptConfigEnum.OPENAI_BASE_URL in os.environ:
            client_args['base_url'] = os.environ[GptConfigEnum.OPENAI_BASE_URL]
            logger.debug(
                f'use base_url: {os.environ[GptConfigEnum.OPENAI_BASE_URL]}')
        if GptConfigEnum.OPENAI_PROXY in os.environ:
            logger.debug(
                f'use proxy: {os.environ[GptConfigEnum.OPENAI_PROXY]}')
            http_client = httpx.Client(
                proxies=os.environ[GptConfigEnum.OPENAI_PROXY])
            client_args['http_client'] = http_client
        self.client = OpenAI(**client_args)

        self.model = model
        self.temperature = temperature
        self.top_p = top_p
        self.retry = retry
        self.max_async = max_async
        self.json_mode = json_mode
        self.fee_limit = fee_limit

        self.api_fees = []  # OpenAI API fee for each call

    def update(self, temperature=None, top_p=None):
        if not temperature is None:
            self.temperature = temperature

        if top_p:
            self.top_p = top_p

    def get_price(self, model):
        '''
        获取模型价格信息
        '''
        if 'gpt-3.5' in model:
            return GPT_PRICE_MAP[GptModelEnum.GPT_3__5_TURBO]
        elif 'gpt-4' in model:
            return GPT_PRICE_MAP[GptModelEnum.GPT_4]
        else:
            return GPT_PRICE_MAP[GptModelEnum.GPT_3__5_TURBO]

    def estimate_fee(self, messages: List[Dict]):
        """
        Estimate the total fee for the given messages.
        """
        token_map = {'system': 0, 'user': 0, 'assistant': 0}
        for message in messages:
            token_map[message['role']
                      ] += get_text_token_number(message['content'])

        prompt_price, completion_price = self.get_price(self.model)
        logger.debug(f"use price info( {prompt_price},  {completion_price})")

        total_price = (sum(token_map.values()) * prompt_price +
                       token_map['user'] * completion_price * 2) / 1000

        return total_price

    def update_fee(self, response):
        prompt_price, completion_price = self.get_price(self.model)

        prompt_tokens = response.usage.prompt_tokens
        completion_tokens = response.usage.completion_tokens

        self.api_fees[-1] += (prompt_tokens * prompt_price +
                              completion_tokens * completion_price) / 1000

    def create_achat(self, messages: List[Dict], output_checker: Callable = lambda *args, **kw: True):
        # logger.debug(f'Raw content: {messages}')

        response = None
        error = ''
        need_retry = True
        for i in range(self.retry):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    response_format={
                        'type': 'json_object' if self.json_mode else 'text'}
                )
                self.update_fee(response)
                if response.choices[0].finish_reason == 'length':
                    raise ChatBotException(
                        f'Failed to get completion. Exceed max token length. '
                        f'Prompt tokens: {response.usage.prompt_tokens}, '
                        f'Completion tokens: {response.usage.completion_tokens}, '
                        f'Total tokens: {response.usage.total_tokens} '
                        f'Reduce chunk_size may help.'
                    )
                if not output_checker(messages[1]['content'], response.choices[0].message.content):
                    logger.debug(
                        f'Invalid response format. Retry num: {i + 1}.')
                    continue

                break
            except openai.AuthenticationError as e:
                logger.debug('api key error')
                error = get_text('error.ChatGptKeyError')
                need_retry = False
                break
            except openai.NotFoundError:
                logger.debug('model not found')
                error = get_text('error.ChatGptModelNotFound')
                need_retry = False
                break
            except openai.RateLimitError:
                logger.debug(
                    f'Rate limit exceeded. Wait 10s before retry. Retry num: {i + 1}.')
                time.sleep(10)
                error = get_text('error.ChatGptRateLimit')
            except openai.APITimeoutError:
                logger.debug(
                    f'Timeout. Wait 3 before retry. Retry num: {i + 1}.')
                time.sleep(3)
                error = get_text('error.ChatGptTimeout')
            except openai.APIConnectionError:
                logger.warning(
                    f'API connection error. Wait 15s before retry. Retry num: {i + 1}.')
                time.sleep(15)
                error = get_text('error.ChatGptAPIConnect')
            except openai.APIError as e:
                logger.debug(f'exception type: {type(e)}')
                # logger.exception(e)
                logger.warning(
                    f'API error. Wait 15s before retry. Retry num: {i + 1}.')
                time.sleep(15)
                error = get_text('error.ChatGptAPIConnect')

        if not response:
            raise ChatBotException(get_text(
                'error.ChatGptError').format(error), need_retry=need_retry)

        return response

    def message(self, messages_list: Union[List[Dict], List[List[Dict]]],
                output_checker: Callable = lambda *args, **kw: True):
        """
        Send chunked messages to the GPT chatbot.
        """
        assert messages_list, 'Empty message list.'

        # Calculate the total sending token number and approximated billing fee.
        token_numbers = get_messages_token_number(messages_list)
        logger.debug(f'token num: {token_numbers}')

        # if the approximated billing fee exceeds the limit, raise an exception.
        approximated_fee = self.estimate_fee(messages_list)
        logger.debug(f'Approximated billing fee: {approximated_fee:.4f} USD')
        self.api_fees += [0]  # Actual fee for this translation call.
        if approximated_fee > self.fee_limit:
            raise ChatBotException(f'Approximated billing fee {approximated_fee} '
                                   f'exceeds the limit: {self.fee_limit}$.')

        try:
            results = self.create_achat(
                messages_list, output_checker=output_checker)
        except ChatBotException as e:
            logger.debug(f'Failed to message with GPT. Error: {e}')
            raise e
        else:
            logger.debug(
                f'Translation fee for this call: {self.api_fees[-1]:.4f} USD')
            logger.debug(
                f'Total bot translation fee: {sum(self.api_fees):.4f} USD')

        return results


class GPTTranslator(BaseBot):
    def __init__(self, prompter: str = 'base_trans', fee_limit=0.1, chunk_size=30, intercept_line=None, translator_config=None):
        """
        :param prompter: Translate prompter, choices can be found in `prompter_map` from prompter.py.
        :param fee_limit: Fee limit (USD) for OpenAI API.
        :param chunk_size: Use small (<20) chunk size for speed (more async call), and enhance translation
                    stability (keep audio timeline consistency).
        :param intercept_line: Intercepted text line number.
        """
        if prompter not in prompter_map:
            raise ValueError(f'Prompter {prompter} not found.')

        self.prompter = prompter
        self.fee_limit = fee_limit
        self.chunk_size = chunk_size
        self.api_fee = 0
        self.total_tokens = 0
        self.intercept_line = intercept_line
        self.summaries = []
        self.scenes = []
        self.translator_config = translator_config
        self.default_temperature = GptTemperatureEnum.medium.value

    def print_messages(self, messages):
        m_list = []
        for m in messages:
            m_list.append(f"{m['role']} - {m['content']}")
        print('\n'.join(m_list))

    def translate(self, texts, src_lang, target_lang):
        gpt_model = self.translator_config.get('gpt_model')
        if gpt_model == GptDomainTypeEnum.other:
            gpt_model = self.translator_config.get('custom_model')
        logger.debug(f'use model : {gpt_model}')
        prompter: BaseTranslatePrompter = prompter_map[self.prompter](
            src_lang, target_lang)
        translate_bot = GPTBot(
            model=gpt_model, fee_limit=self.fee_limit, retry=2, timeout=self.translator_config['timeout'])
        temperature = self.translator_config.get(
            'gpt_temperature', self.default_temperature)
        logger.debug(f'{temperature=}')
        translate_bot.update(temperature=temperature)

        chunks = self.make_chunks(texts, chunk_size=self.chunk_size)
        logger.debug(
            f'Translating : {len(chunks)} chunks, {len(texts)} lines in total.')

        # Start chunk-by-chunk translation
        translations = []
        # should be self.summaries, now it's useless , always empty
        summaries = []
        summary, scene = '', ''
        for i, chunk in enumerate(chunks, start=1):
            user_input = prompter.format_texts(chunk)
            user_instrctions = self.translator_config.get(
                'gpt_instructions', '')
            messages_list = [
                {'role': 'system', 'content': prompter.system(
                )},
                {'role': 'user', 'content': prompter.user(
                    i, user_input, user_instrctions, summaries, scene)}
            ]
            # debug messagesc
            # with open('messages.json', 'w', encoding='utf-8') as f:
            #     json.dump(messages_list, f, ensure_ascii=False, indent=4)
            # self.print_messages(messages_list)
            response = translate_bot.message(
                messages_list, output_checker=prompter.check_format)
            summary, scene, translated = self.parse_responses(
                content=response.choices[0].message.content)
            translations.extend(translated)
            self.summaries.append(summary or scene)
            self.scenes.append(scene)
            if response.usage:
                self.total_tokens += response.usage.total_tokens
            logger.debug(f'Translating : {i}/{len(chunks)}')
            logger.debug(f'summary: {summary}')
            logger.debug(f'scene: {scene}')

        logger.debug(f'total cost tokens: {self.total_tokens}')
        self.api_fee += sum(translate_bot.api_fees)
        logger.debug(f'api fee : {self.api_fee}')
        return translations
