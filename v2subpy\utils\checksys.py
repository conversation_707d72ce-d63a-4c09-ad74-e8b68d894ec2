"""
some system check functions
"""
import os
import sys
from v2subpy.utils import config
from v2subpy.utils.log import logger
from v2subpy.utils.common import is_win

gpu_info: dict | None = None
MINIMUM_GPU_MEMORY = 3.8

def byte_to_gb(memory: int) -> float:
    factor = 1024 *1024 * 1024
    return memory / factor

def check_gpu() -> bool:
    """
    check if gpu is supported

    """
    info = get_gpu_info()
    return info['enabled']

def get_gpu_info() -> dict:
    '''
    根据平台返回相关gpu信息
    macos 直接返回默认
    '''
    global gpu_info
    if gpu_info is not None:
        return gpu_info
    gpu_info = {'enabled': False}
    if not is_win():
        return gpu_info
    else:
        gpu_info = get_nvidia_info()
        return gpu_info

def get_nvidia_info() -> dict:
    '''
    检测nvidia-gpu信息
    '''
    import pynvml
    info = {}
    info['enabled'] = False
    try:
        pynvml.nvmlInit()
        device_count = pynvml.nvmlDeviceGetCount()
        # only handle index 0
        if device_count > 0:
            index = 0
            handle = pynvml.nvmlDeviceGetHandleByIndex(index)
            product_name = pynvml.nvmlDeviceGetName(handle)
            memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            total_memory = memory_info.total
            driver_version = pynvml.nvmlSystemGetDriverVersion()
            cuda_version = pynvml.nvmlSystemGetCudaDriverVersion()
            info['product_name'] = product_name
            total_memory = byte_to_gb(total_memory)
            info['total_memory'] = total_memory
            info['driver_version'] = driver_version
            info['cuda_version'] = str(cuda_version)
            info['enabled'] = total_memory > MINIMUM_GPU_MEMORY
    except:
        pass

    return info

def print_info():
    logger.debug(f"VERSION: {config.APP_VERSION}")
    logger.debug(f"os.getcwd: {os.getcwd()}")
    logger.debug(f"python: , {sys.executable}")
    logger.debug(f"sys.path, {sys.path}")
    logger.debug(f"__file__: , {__file__}")
    cfg = config.read_config()
    logger.debug(str(cfg))
