# V2subpy Qt集成优化设计文档

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    GUI应用程序层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Qt应用程序     │  │  Tkinter应用程序 │  │   其他GUI     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   V2subpy集成层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Qt集成模块      │  │ Tkinter集成模块  │  │  通用集成接口 │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   V2subpy异步核心                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  任务管理器      │  │   回调管理器     │  │  资源管理器   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  处理管道        │  │   线程池管理     │  │  错误处理器   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   底层处理模块                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  音频处理        │  │   语音识别       │  │   文本翻译    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件设计

#### 1. 异步任务管理器 (AsyncTaskManager)

```python
class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, TranslationTask] = {}
        self.task_lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def create_task(self, config: TranslationConfig) -> str:
        """创建翻译任务"""
        task_id = self._generate_task_id()
        task = TranslationTask(task_id, config)
        
        with self.task_lock:
            self.tasks[task_id] = task
        
        return task_id
    
    def start_task(self, task_id: str, video_path: str, callbacks: TranslationCallbacks) -> bool:
        """启动翻译任务"""
        task = self._get_task(task_id)
        if not task:
            return False
        
        # 在线程池中异步执行
        future = self.executor.submit(self._run_task, task, video_path, callbacks)
        task.set_future(future)
        
        return True
    
    def stop_task(self, task_id: str) -> bool:
        """停止翻译任务"""
        task = self._get_task(task_id)
        if not task:
            return False
        
        task.request_stop()
        return True
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task = self._get_task(task_id)
        return task.get_status() if task else None
```

#### 2. 翻译任务 (TranslationTask)

```python
class TranslationTask:
    """翻译任务"""
    
    def __init__(self, task_id: str, config: TranslationConfig):
        self.task_id = task_id
        self.config = config
        self.status = TaskStatus.CREATED
        self.pipeline: Optional[ProcessingPipeline] = None
        self.future: Optional[Future] = None
        self.stop_requested = threading.Event()
        self.pause_requested = threading.Event()
    
    def start_processing(self, video_path: str, callbacks: TranslationCallbacks):
        """开始处理"""
        try:
            self.status = TaskStatus.RUNNING
            self.pipeline = ProcessingPipeline(self.config, callbacks)
            
            # 非阻塞启动处理管道
            self.pipeline.start_async(video_path)
            
            # 监控处理状态
            self._monitor_processing()
            
        except Exception as e:
            self.status = TaskStatus.ERROR
            callbacks.on_error(self.task_id, {"error": str(e)})
    
    def _monitor_processing(self):
        """监控处理状态（非阻塞）"""
        while not self.stop_requested.is_set():
            if self.pause_requested.is_set():
                self.pipeline.pause()
                self.status = TaskStatus.PAUSED
                self.pause_requested.wait()  # 等待恢复信号
                self.pipeline.resume()
                self.status = TaskStatus.RUNNING
            
            if self.pipeline.is_finished():
                self.status = TaskStatus.COMPLETED
                break
            
            time.sleep(0.1)  # 短暂休眠，避免占用过多CPU
```

#### 3. 非阻塞处理管道 (NonBlockingProcessingPipeline)

```python
class NonBlockingProcessingPipeline:
    """非阻塞处理管道"""
    
    def __init__(self, config: TranslationConfig, callbacks: TranslationCallbacks):
        self.config = config
        self.callbacks = callbacks
        self.is_running = False
        self.is_paused = False
        self.worker_threads: List[threading.Thread] = []
        self.result_queue = queue.Queue()
        self.stop_event = threading.Event()
    
    def start_async(self, video_path: str):
        """异步启动处理"""
        self.is_running = True
        
        # 启动音频处理线程
        audio_thread = threading.Thread(
            target=self._audio_processing_worker,
            args=(video_path,),
            daemon=True
        )
        audio_thread.start()
        self.worker_threads.append(audio_thread)
        
        # 启动结果处理线程
        result_thread = threading.Thread(
            target=self._result_processing_worker,
            daemon=True
        )
        result_thread.start()
        self.worker_threads.append(result_thread)
    
    def _audio_processing_worker(self, video_path: str):
        """音频处理工作线程"""
        try:
            # 生成音频段
            for segment in self._generate_audio_segments(video_path):
                if self.stop_event.is_set():
                    break
                
                # 处理音频段
                result = self._process_audio_segment(segment)
                self.result_queue.put(result)
                
        except Exception as e:
            self.callbacks.on_error(self.task_id, {"error": str(e)})
    
    def _result_processing_worker(self):
        """结果处理工作线程"""
        try:
            while self.is_running and not self.stop_event.is_set():
                try:
                    result = self.result_queue.get(timeout=1.0)
                    self.callbacks.on_subtitle_ready(self.task_id, result)
                except queue.Empty:
                    continue
                    
        except Exception as e:
            self.callbacks.on_error(self.task_id, {"error": str(e)})
    
    def stop(self):
        """停止处理"""
        self.stop_event.set()
        self.is_running = False
        
        # 等待所有工作线程结束
        for thread in self.worker_threads:
            thread.join(timeout=5.0)
```

#### 4. Qt集成模块 (QtIntegration)

```python
from PySide6.QtCore import QObject, Signal, QThread, QTimer

class QtTranslationManager(QObject):
    """Qt集成的翻译管理器"""
    
    # Qt信号
    subtitle_ready = Signal(str, dict)  # task_id, subtitle_data
    progress_updated = Signal(str, dict)  # task_id, progress_data
    error_occurred = Signal(str, dict)  # task_id, error_data
    task_finished = Signal(str, dict)  # task_id, result_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_manager = AsyncTaskManager()
        self.active_tasks: Dict[str, QTimer] = {}
    
    def start_translation(self, video_path: str, config: TranslationConfig) -> str:
        """启动翻译任务"""
        task_id = self.task_manager.create_task(config)
        
        # 创建回调对象
        callbacks = QtTranslationCallbacks(self, task_id)
        
        # 启动任务
        success = self.task_manager.start_task(task_id, video_path, callbacks)
        
        if success:
            # 创建状态监控定时器
            timer = QTimer()
            timer.timeout.connect(lambda: self._check_task_status(task_id))
            timer.start(100)  # 每100ms检查一次状态
            self.active_tasks[task_id] = timer
        
        return task_id if success else ""
    
    def stop_translation(self, task_id: str) -> bool:
        """停止翻译任务"""
        success = self.task_manager.stop_task(task_id)
        
        if success and task_id in self.active_tasks:
            self.active_tasks[task_id].stop()
            del self.active_tasks[task_id]
        
        return success
    
    def _check_task_status(self, task_id: str):
        """检查任务状态"""
        status = self.task_manager.get_task_status(task_id)
        if status and status.is_finished():
            # 任务完成，停止监控
            if task_id in self.active_tasks:
                self.active_tasks[task_id].stop()
                del self.active_tasks[task_id]

class QtTranslationCallbacks(TranslationCallbacks):
    """Qt专用的回调实现"""
    
    def __init__(self, manager: QtTranslationManager, task_id: str):
        self.manager = manager
        self.task_id = task_id
    
    def on_subtitle_ready(self, task_id: str, subtitle: Dict[str, Any]):
        """字幕准备就绪回调"""
        # 使用Qt信号发送到主线程
        self.manager.subtitle_ready.emit(task_id, subtitle)
    
    def on_progress_update(self, task_id: str, progress: Dict[str, Any]):
        """进度更新回调"""
        self.manager.progress_updated.emit(task_id, progress)
    
    def on_error(self, task_id: str, error: Dict[str, Any]):
        """错误回调"""
        self.manager.error_occurred.emit(task_id, error)
    
    def on_finished(self, task_id: str, result: Dict[str, Any]):
        """完成回调"""
        self.manager.task_finished.emit(task_id, result)
```

## 关键技术点

### 1. 线程模型

- **主线程**：GUI事件循环，不执行耗时操作
- **工作线程池**：执行翻译任务，与主线程解耦
- **监控线程**：监控任务状态，通过信号与主线程通信
- **回调线程**：处理回调函数，确保线程安全

### 2. 异步通信

- 使用队列进行线程间通信
- 使用信号/槽机制与Qt主线程通信
- 避免直接的跨线程调用

### 3. 资源管理

- 自动资源清理机制
- 共享模型资源池
- 内存使用监控和优化

### 4. 错误处理

- 分层错误处理机制
- 错误恢复和重试策略
- 详细的错误信息和日志

## 实现细节

### 文件结构

```
v2subpy/
├── realtime/
│   ├── __init__.py
│   ├── async_core/           # 异步核心模块
│   │   ├── __init__.py
│   │   ├── task_manager.py   # 任务管理器
│   │   ├── pipeline.py       # 非阻塞处理管道
│   │   ├── callbacks.py      # 回调接口
│   │   └── models.py         # 数据模型
│   ├── integrations/         # 集成模块
│   │   ├── __init__.py
│   │   ├── qt_integration.py # Qt集成
│   │   ├── tkinter_integration.py # Tkinter集成
│   │   └── generic_integration.py # 通用集成
│   ├── legacy/               # 兼容性模块
│   │   ├── __init__.py
│   │   └── realtime_trans.py # 原有接口的兼容实现
│   └── utils/                # 工具模块
│       ├── __init__.py
│       ├── threading_utils.py
│       └── error_handling.py
```

### 迁移策略

1. **保持向后兼容**：原有的`realtime_trans`函数继续可用
2. **渐进式迁移**：提供新旧接口的对比文档
3. **示例代码**：提供完整的迁移示例
4. **测试覆盖**：确保新旧接口都有完整测试

### 性能优化

1. **模型复用**：多个任务共享已加载的模型
2. **内存池**：复用音频缓冲区和处理对象
3. **批处理**：合并小的处理任务
4. **优先级调度**：根据任务重要性调度资源

## 测试策略

### 单元测试
- 任务管理器功能测试
- 处理管道异步测试
- 回调机制测试
- 错误处理测试

### 集成测试
- Qt应用程序集成测试
- 多任务并发测试
- 长时间运行测试
- 资源泄漏测试

### 压力测试
- 大量并发任务测试
- 长视频处理测试
- 内存压力测试
- CPU密集型测试

这个设计文档提供了完整的技术方案，可以指导后台团队进行v2subpy的重构，使其更好地与Qt等GUI框架集成。