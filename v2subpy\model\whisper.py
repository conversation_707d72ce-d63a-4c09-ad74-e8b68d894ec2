"""
all whisper related function
"""
from pathlib import Path
import psutil
from faster_whisper.audio import decode_audio
from v2subpy.model.writers import format_timestamp
from v2subpy.trans import langutil
from v2subpy.utils.log import logger
from v2subpy.utils import config
from v2subpy.utils.config import get_text
from v2subpy.model.result import WhisperResult
from v2subpy.model.format import merge_sentences
from v2subpy.model.options import load_initial_prompt
from v2subpy.sub.subutils import handle_chinese_segment
from v2subpy.utils.version_manage import VersionManager, Fn, fn_is_enabled
from v2subpy.utils.common import TranscribeError, is_task_stopped, TaskStoppedError, get_share_value, SubtitleFormatEnum
from v2subpy.model.sent_split import SplitArgs, update_split_result, is_supported_lang
class ModelLoadError(Exception):
    '''
    model load error
    '''


def calculate_cpu_count() -> int:
    '''
    计算需要的cpu线程数， 用来加载模型
    根据config里的cpu_balance参数决定
    'true : 平衡模式， 只是用4个线程
    'false': 全速模式， 使用所有线程 - 1
    '''
    cfg = config.get_sys_config()
    cpu_balance_mode = cfg.get('model')['cpu_balance']
    if cpu_balance_mode:
        if psutil.cpu_count(logical=False) > 3:
            cpu_threads = 4
        else:
            cpu_threads = psutil.cpu_count(logical=False)
    else:
        cpu_threads = psutil.cpu_count() - 1

    logger.debug(f'use {cpu_threads=}')
    return cpu_threads


def load_vad_options() -> dict:
    '''
    读取vad相关参数并返回
    '''
    vad_enabled = False
    vad_options = None
    options = {}
    # 特别优化开启
    if config.fn_enabled(Fn.SPE_ENHANCE):
        # from whisper-standalone-win
        vad_enabled = True
        # vad_options = dict(
        #     threshold=0.45,
        #     min_speech_duration_ms=350,
        #     min_silence_duration_ms=3000,
        #     speech_pad_ms=900,
        #     window_size_samples=1024,
        # )
        vad_options = dict(
            threshold=0.35,
            min_speech_duration_ms=100,
            min_silence_duration_ms=20,
            speech_pad_ms=30,
        )
    options['vad_filter'] = vad_enabled
    logger.debug(f'{vad_enabled=}')
    if vad_enabled:
        # logger.debug(f'using {vad_options=}')
        logger.info(config.get_text('info.EnableSpeEnhance'))
        options['vad_parameters'] = vad_options

    return options


def load_protected_mode_options() -> dict:
    '''
    获取保护模式相关参数
    '''
    options = {}
    if config.fn_enabled(Fn.PROTECTED_MODE):
        # test some options
        options['condition_on_previous_text'] = False
        options['temperature'] = 0.0
        options['repetition_penalty'] = 1.2
        options['no_repeat_ngram_size'] = 3

        # options['suppress_tokens'] = []
        logger.info(config.get_text('info.EnableProtectedMode'))
        logger.debug('enabled protected_mode')

    return options


def load_options(options):
    '''
    根据情况修改options
    比如根据语言使用不同option，
    启用vad等
    '''
    language_code = options['language']
    # 设置默认参数
    options['word_timestamps'] = True

    # 设置vad参数
    options.update(load_vad_options())

    # 设置保护模式参数
    options.update(load_protected_mode_options())

    # 设置prompt
    options.update(load_initial_prompt(language_code))

def detect_language(model, file_path) -> str:
    '''
    处理前检测语言
    '''
    audio = decode_audio(file_path, sampling_rate=model.feature_extractor.sampling_rate)
    language, language_probability, all_language_probs = model.detect_language(audio)
    logger.debug(f"Detected language: {language}")
    language_code = language
    language_name = config.get_lang_name(language_code)
    logger.info(config.get_text('info.DetectLanguage').format(
        language_name, language_probability))
    return language

def transcribe(model, file_path, options, debug_json=False):
    load_options(options)
    logger.debug(f'using transcribe options\n: {options}')

    try:
        segments, info = model.transcribe(
            file_path, **options)

        result = prepare_result(segments, info)
        # 生成debug json文件
        if debug_json:
            write_json(file_path, result)
        result = post_process_result(result)
        return result
    except TaskStoppedError:
        raise
    except IndexError:
        errmsg = config.get_text('error.TranscribeAudioError')
        message = config.get_text('error.TranscribeError').format(errmsg)
        logger.debug(message)
        raise TranscribeError(message)
    except TranscribeError:
        raise
    except Exception as e:
        message = config.get_text('error.TranscribeError').format(str(e))
        logger.exception(message)
        logger.debug(message)
        raise TranscribeError(message)


def print_segment(segment, language_code):
    '''
    打印转录字幕到前台， 需要进行一些处理， 比如中文简繁体
    '''
    text = segment.text
    if language_code == 'zh':
        text = handle_chinese_segment(segment)
    line = f'''[{format_timestamp(segment.start)} --> {format_timestamp(segment.end)}]  {text}'''
    print(line)


def prepare_result(segments, info):
    segments_list = []
    total_length = info.duration
    logger.debug(f'total audio length: {total_length}')
    logger.debug(f'total audio after vad length: {info.duration_after_vad}')
    status_message = get_text("info.StatusTranscribeProgress")

    update_progress = get_share_value('update_progress')
    for segment in segments:
        # 检查是否需要停止任务
        if is_task_stopped():
            raise TaskStoppedError()
        print_segment(segment, info.language)
        segments_list.append(segment)
        # 查看是否设置共享变量， update_progress
        if update_progress:
            progress_info = (segment.end, total_length)
            update_progress(status_message, progress_info)
            
    if len(segments_list) == 0:
        # 没检测到声音, 抛出异常退出
        raise TranscribeError(config.get_text('exec.NoAudioDetected'))
    if update_progress:
        update_progress(status_message, (total_length, total_length))

    list_segments = []
    for segment in segments_list:
        segment_dict = segment._asdict()
        word_dicts = []
        for word in segment.words:
            word_dict = {
                'start': word.start,
                'end': word.end,
                'word': word.word
            }
            word_dicts.append(word_dict)
        if len(word_dicts) > 0:
            # 去掉空行, 防止出错
            segment_dict["words"] = word_dicts
            list_segments.append(segment_dict)
    result_dict = dict(
        segments=list_segments,
        language=info.language
    )
    # logger.debug(f"{result_dict=}")
    result = WhisperResult(result_dict)
    result.language_probability = info.language_probability

    return result


def post_process_result(result):
    '''
    对转录完成字幕进行必要处理， 比如合并行等等
    '''
    # 检查是否需要跳过后处理
    if not fn_is_enabled(Fn.SUB_POST_FORMAT):
        logger.debug(f'skip post process result')
        return result
    try:
        lang_code = result.language
        logger.debug(f'before regroup: {len(result.segments)}')
        # 如果支持AI断句功能, 且当前语言支持
        if VersionManager.check_fn(Fn.SUB_AI_FORMAT) and is_supported_lang(lang_code):
            result = handle_result_spacy(result, lang_code)
        else:
            # 否则使用算法断句
            handle_result(result, lang_code)
        logger.debug(f'after regroup: {len(result.segments)}')
        logger.info(config.get_text('info.PostTranscribeFormat'))
    except Exception as e:
        logger.debug(f'regroup error: {e}')

    return result

def handle_result_spacy(result: WhisperResult, lang_code: str):
    '''
    根据语言类型处理result， 格式化，重新断句, 使用spacy
    '''
    if langutil.is_cjk_lang(lang_code):
        maxlen = config.get_key(Fn.SUB_MAXLEN_CJK)
    else:
        maxlen = config.get_key(Fn.SUB_MAXLEN_OTHER)
    split_args = SplitArgs(width=maxlen, lines=1)
    logger.info(config.get_text("info.PostFormatAI"))
    result = update_split_result(result, lang_code, split_args)
    return result

def handle_result(result: WhisperResult, lang_code: str):
    '''
    根据语言类型处理result， 格式化，重新断句
    '''
    max_split_duration = 15
    logger.info(config.get_text("info.PostFormatAlgo"))
    if langutil.is_cjk_lang(lang_code):
        # 中日韩语言
        punctuations = [' ', '。', '?', '？', ',',
                        '，', '!', '！', '：', ':', '”', '、']
        (result
         .split_by_punctuation(punctuations)
         .split_by_duration(max_dur=max_split_duration, even_split=True)
         )
        if fn_is_enabled(Fn.SUB_MAXLENCUT):
            maxlen = config.get_key(Fn.SUB_MAXLEN_CJK)
            logger.debug(f'cut maxlen cjk: {maxlen}')
            result.split_by_length(max_chars=maxlen)
    else:
        # 处理英文等
        punctuations = ['。', '?', '？', ',', '，', '!', '！']
        (result
         .merge_all_segments()
         .split_by_punctuation([('.', ' ')] + punctuations)
         .merge_by_punctuation(punctuations, max_words=2)
         .split_by_duration(max_dur=max_split_duration, even_split=True)
         )
        if fn_is_enabled(Fn.SUB_MAXLENCUT):
            maxlen = config.get_key(Fn.SUB_MAXLEN_OTHER)
            logger.debug(f'cut maxlen other: {maxlen}')
            result.split_by_length(max_chars=maxlen, max_words=10)

def write_result(output_file, result):
    '''
    写入结果， srt等
    '''
    write_srt(output_file, result)
    if fn_is_enabled(Fn.SUB_KARAOKE):
        write_karaoke(output_file, result)

def write_srt(output_file, result):
    '''
    生成字幕文件, 根据格式但用不同函数
    '''
    ext = Path(output_file).suffix
    if ext[1:] == SubtitleFormatEnum.ASS.value:
        #  write ass
        result.to_ass(output_file, word_level=False)
    else:
        # write srt, vtt
        result.to_srt_vtt(output_file, word_level=False)


def write_json(output_file, result):
    '''
    生成json文件, 用于进一步处理生成字幕
    '''
    # write json for test
    pf = Path(output_file)
    json_file = str(pf.with_suffix('.json'))
    result.save_as_json(json_file)
    
   
def write_karaoke(output_file, result):
    '''
    生成卡拉ok模式字幕
    '''
    # write karaok
    srt_kara_file = Path(output_file).with_suffix('.kara.srt')
    result.to_srt_vtt(str(srt_kara_file))
    logger.info(get_text("info.GenerateKaraoke").format(str(srt_kara_file)))

 