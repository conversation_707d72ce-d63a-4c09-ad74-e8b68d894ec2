#!/usr/bin/env python3
"""
调试实时翻译功能

专门用于调试实时翻译过程中的问题。
"""

import sys
import time
import threading

# 添加项目路径
sys.path.insert(0, '.')

def debug_realtime_translation():
    """调试实时翻译功能"""
    print("🔍 调试实时翻译功能")
    print("=" * 50)
    
    # 设置日志级别为 DEBUG
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    VIDEO_PATH = r"d:\testvideo\en1.mp4"
    
    # 简化的配置
    config = {
        'whisper_model': 'base',  # 使用较小的模型
        'translator_type': 'default',
        'target_language': 'zh',
        'segment_duration': 10.0,  # 较长的段落
        'max_concurrent_segments': 1,  # 单线程处理
    }
    
    # 回调函数计数器
    callback_counts = {
        'subtitle_ready': 0,
        'progress_update': 0,
        'error': 0,
        'finished': 0
    }
    
    def on_subtitle_ready(subtitle_dict):
        callback_counts['subtitle_ready'] += 1
        print(f"\n🎯 收到字幕 #{callback_counts['subtitle_ready']}:")
        print(f"   时间: {subtitle_dict['start_time']:.1f}s - {subtitle_dict['end_time']:.1f}s")
        print(f"   原文: {subtitle_dict['original_text']}")
        print(f"   译文: {subtitle_dict['translated_text']}")
        print(f"   状态: {subtitle_dict['status']}")
    
    def on_progress_update(status_message, progress_info):
        callback_counts['progress_update'] += 1
        current, total = progress_info
        percentage = (current / total * 100) if total > 0 else 0
        print(f"\r📊 进度 #{callback_counts['progress_update']}: {percentage:.1f}% - {status_message}", 
              end="", flush=True)
    
    def on_error(error_message):
        callback_counts['error'] += 1
        print(f"\n❌ 错误 #{callback_counts['error']}: {error_message}")
    
    def on_finished():
        callback_counts['finished'] += 1
        print(f"\n✅ 完成 #{callback_counts['finished']}")
    
    try:
        from v2subpy.realtime import realtime_trans
        
        print(f"🚀 开始调试处理: {VIDEO_PATH}")
        print(f"📋 配置: {config}")
        
        # 启动处理
        realtime_trans(
            video_path=VIDEO_PATH,
            config=config,
            on_subtitle_ready=on_subtitle_ready,
            on_progress_update=on_progress_update,
            on_error=on_error,
            on_finished=on_finished
        )
        
        print(f"\n📊 回调统计:")
        for name, count in callback_counts.items():
            print(f"  - {name}: {count} 次")
        
        if callback_counts['subtitle_ready'] > 0:
            print("✅ 调试成功：收到了字幕回调")
        else:
            print("❌ 调试失败：没有收到字幕回调")
            
    except Exception as e:
        print(f"\n💥 调试异常: {e}")
        import traceback
        traceback.print_exc()

def debug_components():
    """调试各个组件"""
    print("\n🔧 调试各个组件")
    print("-" * 30)
    
    try:
        from v2subpy.realtime.models import RealtimeConfig
        from v2subpy.realtime.audio_segmenter import AudioSegmenter
        
        # 测试配置
        config = RealtimeConfig(
            whisper_model='base',
            segment_duration=10.0
        )
        print("✅ 配置创建成功")
        
        # 测试音频分段器
        segmenter = AudioSegmenter(config)
        print("✅ 音频分段器创建成功")
        
        # 测试分段信息获取
        VIDEO_PATH = r"d:\testvideo\en1.mp4"
        segment_info = segmenter.get_segment_info(VIDEO_PATH)
        print(f"📊 分段信息: {segment_info}")
        
        # 测试音频段生成（只生成前2个）
        print("🎵 测试音频段生成...")
        count = 0
        for segment in segmenter.extract_and_segment(VIDEO_PATH):
            count += 1
            print(f"  段落 {count}: {segment.segment_id}, {segment.start_time:.1f}s-{segment.end_time:.1f}s")
            if count >= 2:  # 只测试前2个段落
                break
        
        print("✅ 组件调试完成")
        
    except Exception as e:
        print(f"❌ 组件调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🐛 实时翻译调试工具")
    print("=" * 50)
    
    # 首先调试组件
    debug_components()
    
    # 然后调试完整流程
    debug_realtime_translation()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 调试被用户中断")
    except Exception as e:
        print(f"\n💥 调试工具异常: {e}")
        import traceback
        traceback.print_exc()