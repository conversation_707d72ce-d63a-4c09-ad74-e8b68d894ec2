## 版本说明
v2sub软件分为免费版和收费版， 免费版有部分功能限制

### 免费版
- 只能处理视频前10分钟
在v2sub.py直接修改
- 只支持部分视频格式（avi， mp4， mkv）
在ff.py配置
- 源语言和目标语言只支持（中文， 日文， 英文）
v2sutqt前端config.py 配置
- 只支持部分模型（小型）
v2sutqt前端config.py 配置

### 收费版
- 没有视频时间限制
- 支持更多视频格式
- 支持更多语言， 源语言支持自动探测
- 支持大模型
- 支持单独字幕翻译

专业版支持后缀列表
```
avi
wmv
asf
mpeg
mpg
ts
mp4
m4v
3gp
mkv
rm
rmvb
webm
f4v
divx
vob

```


## 关于合并free版本和pro版本的开发
思路： 后端config.py定义全局变量，APP_VERSION， 和枚举 Version
默认为Version.FREE
方法设置版本set_app_version, 
方法app_is_free， 使前后端统一使用此方法判断版本
配置文件config.toml, 参数free_version = true， 对应免费版本
免费版前端启动后， 如果注册成功， 调用set_app_versio(Version.PRO)
其他所有有差异的地方均调用app_is_free， 根据结果然后进行差异处理

修改文件列表
v2subpy
- config.py
- v2sub.py
- ff.py
- checksys.py


v2subqt
- main_window.py
- config.py
- 
