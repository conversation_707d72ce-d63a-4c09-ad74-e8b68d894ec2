# 实时字幕播放器需求文档

## 项目简介

当前 v2sub 已经能够输入视频文件，通过 `faster-whisper` 生成原始语言字幕，并通过各种翻译器（如 `openai`）翻译成用户需要的语言。用户可以一键将英语视频生成中文字幕，完成后加载观看。

但现有流程需要等待完整处理完成后才能观看。本功能旨在开发一个内置播放器，实现实时观看翻译字幕的能力。

## 需求

### 需求 1：实时播放与字幕生成

**用户故事：** 作为用户，我希望能够选择视频文件并立即开始播放，同时实时生成和显示翻译字幕，这样我就不需要等待完整处理完成。

#### 验收标准

1. WHEN 用户选择视频文件并启动播放器 THEN 系统 SHALL 开始后台音频提取和缓冲处理
2. WHEN 音频提取完成 THEN 系统 SHALL 将音频分段并通过 faster-whisper 转换为字幕
3. WHEN 原始字幕生成完成 THEN 系统 SHALL 发送到指定翻译引擎获取翻译
4. WHEN 翻译字幕准备就绪 THEN 播放器 SHALL 开始播放视频并显示字幕
5. WHEN 播放过程中 THEN 系统 SHALL 持续处理后续音频段并实时提供翻译字幕

### 需求 2：音频分段与缓冲管理

**用户故事：** 作为系统，我需要智能地处理音频分段和缓冲，确保播放流畅性和字幕连续性。

#### 验收标准

1. WHEN 处理音频时 THEN 系统 SHALL 将音频分成合适的时间段（建议 5-10 秒）
2. WHEN 分段处理时 THEN 系统 SHALL 维护足够的缓冲区以应对处理延迟
3. WHEN 缓冲区字幕不足时 THEN 播放器 SHALL 自动暂停等待新字幕
4. WHEN 新字幕到达时 THEN 播放器 SHALL 自动恢复播放
5. IF 音频分段边界导致字幕截断 THEN 系统 SHALL 智能合并相关字幕片段

### 需求 3：异常处理与自动恢复

**用户故事：** 作为用户，我希望系统能够自动处理网络延迟、翻译引擎故障等异常情况，无需我手动干预。

#### 验收标准

1. WHEN 翻译引擎响应超时 THEN 系统 SHALL 自动重试最多 3 次
2. WHEN 网络连接中断 THEN 系统 SHALL 暂停处理并显示缓冲状态
3. WHEN 网络恢复 THEN 系统 SHALL 自动继续处理流程
4. IF 翻译引擎持续失败 THEN 系统 SHALL 显示原始字幕作为备选
5. WHEN 发生异常时 THEN 系统 SHALL 记录详细日志便于调试

### 需求 4：播放器基础功能

**用户故事：** 作为用户，我希望播放器具备标准的媒体播放功能，让我能够自由控制播放进度和设置。

#### 验收标准

1. WHEN 用户操作播放控件 THEN 播放器 SHALL 支持播放、暂停、停止功能
2. WHEN 用户拖动进度条 THEN 播放器 SHALL 跳转到指定时间位置
3. WHEN 用户调整音量 THEN 播放器 SHALL 实时改变音频输出音量
4. WHEN 用户请求快进/快退 THEN 播放器 SHALL 支持 10 秒步进控制
5. WHEN 用户切换全屏 THEN 播放器 SHALL 进入/退出全屏模式
6. WHEN 用户跳转播放位置 THEN 系统 SHALL 重新同步字幕显示

### 需求 5：字幕显示与管理

**用户故事：** 作为用户，我希望能够清晰地看到字幕，并能够管理字幕的显示方式和已生成的字幕文件。

#### 验收标准

1. WHEN 字幕准备就绪 THEN 播放器 SHALL 在视频下方显示翻译后字幕
2. WHEN 用户选择 THEN 播放器 SHALL 支持同时显示原始字幕和翻译字幕
3. WHEN 用户调整 THEN 播放器 SHALL 支持字幕字体大小、颜色、位置设置
4. WHEN 播放完成 THEN 系统 SHALL 自动保存完整的字幕文件（原始+翻译）
5. IF 已存在对应字幕文件 THEN 播放器 SHALL 优先加载已有字幕而非重新生成

### 需求 6：性能与用户体验

**用户故事：** 作为用户，我希望播放器响应迅速，字幕延迟最小，整体体验流畅。

#### 验收标准

1. WHEN 启动播放器 THEN 系统 SHALL 在 3 秒内开始视频播放
2. WHEN 处理字幕时 THEN 字幕延迟 SHALL 不超过 10 秒
3. WHEN 播放视频时 THEN 视频帧率 SHALL 保持稳定不卡顿
4. WHEN 多任务处理时 THEN CPU 使用率 SHALL 保持在合理范围内
5. WHEN 长时间播放时 THEN 内存使用 SHALL 保持稳定不泄漏

### 需求 7：集成与兼容性

**用户故事：** 作为用户，我希望新的播放器功能能够与现有的 v2sub 功能无缝集成。

#### 验收标准

1. WHEN 使用播放器 THEN 系统 SHALL 复用现有的 faster-whisper 配置
2. WHEN 选择翻译引擎 THEN 系统 SHALL 支持现有的所有翻译器选项
3. WHEN 生成字幕 THEN 输出格式 SHALL 与现有批处理模式兼容
4. WHEN 保存设置 THEN 播放器配置 SHALL 集成到现有配置系统
5. IF 用户从批处理切换到实时模式 THEN 界面切换 SHALL 保持用户设置