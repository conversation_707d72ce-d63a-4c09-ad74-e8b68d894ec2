from setuptools import setup, find_packages
# coding: utf-8
import os
import fnmatch
import sysconfig

from Cython.Build import cythonize
from setuptools.command.build_py import build_py as _build_py

# START
from distutils.command import build_ext


def get_export_symbols(self, ext):
    parts = ext.name.split(".")
    print('parts', parts)
    if parts[-1] == "__init__":
        initfunc_name = "PyInit_" + parts[-2]
    else:
        initfunc_name = "PyInit_" + parts[-1]


build_ext.build_ext.get_export_symbols = get_export_symbols
# END


class build_py(_build_py):

    def find_package_modules(self, package, package_dir):
        ext_suffix = sysconfig.get_config_var('EXT_SUFFIX')
        modules = super().find_package_modules(package, package_dir)
        filtered_modules = []
        for (pkg, mod, filepath) in modules:
            if os.path.exists(filepath.replace('.py', ext_suffix)):
                continue
            filtered_modules.append((pkg, mod, filepath, ))
        return filtered_modules


EXCLUDE_FILES = []


def get_ext_paths(root_dir, exclude_files):
    """get filepaths for compilation"""
    paths = []

    for root, dirs, files in os.walk(root_dir):
        for filename in files:
            if os.path.splitext(filename)[1] != '.py':
                continue

            file_path = os.path.join(root, filename)
            if file_path in exclude_files:
                continue

            paths.append(file_path)
    return paths


def read_version(fname="v2subpy/version.py"):
    exec(compile(open(fname, encoding="utf-8").read(), fname, "exec"))
    return locals()["__version__"]


setup(
    name='v2subpy',
    version=read_version(),
    description='Ai powered video to subtitle tool',
    author='<EMAIL>',
    python_requires=">=3.9",
    packages=find_packages(exclude=["tests*"]),
    ext_modules=cythonize(
        get_ext_paths('v2subpy', EXCLUDE_FILES),
        compiler_directives={'language_level': 3}
    ),
    cmdclass={
        'build_py': build_py
    }
)
