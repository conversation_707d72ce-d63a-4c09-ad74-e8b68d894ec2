from v2subpy.utils import config
from v2subpy.utils.version_manage import VersionManager, Version, Fn


def test_init_ver():
    '''
    初始化应为FREE
    '''
    assert config.APP_VERSION is config.Version.FREE


def test_add_ver():
    ver = Version.PRO
    VersionManager.add_version(ver)
    assert VersionManager.VERSIONS_DICT.get(ver) is not None


def test_add_fn():
    ver = Version.PRO
    VersionManager.add_version(ver)
    fn = 'vad_filter'
    VersionManager.add_fn(ver, fn, set())
    config.set_app_version(Version.PRO)
    assert VersionManager.check_fn(fn) is True


def test_check_fn_value():
    ver = Version.PRO
    VersionManager.add_version(ver)
    fn = 'sub_formats'
    fn_values = {'txt', 'vtt', 'srt'}
    VersionManager.add_fn(ver, fn, fn_values)
    config.set_app_version(Version.PRO)
    check_vals = {'txt'}
    assert VersionManager.check_fn_value(fn, check_vals)


def test_check_fn():
    ver = Version.MAX
    config.set_app_version(ver)
    fn = Fn.GPT_SUMMARY
    if VersionManager.check_fn(fn):
        print(f'{fn} enabled for {ver}')
