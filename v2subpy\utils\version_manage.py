'''
版本管理模块， 根据不同版本分配相应的功能
'''
from typing import Any
from copy import deepcopy
from v2subpy.utils.common import StrEnum
from v2subpy.utils.common import SubtitleFormatEnum
from v2subpy.utils.common import GptDomainTypeEnum, MODELS_SUPPORTED_NAMES


class Version(StrEnum):
    FREE = 'free'
    PRO = 'pro'
    MAX = 'max'
    ULTRA = 'ultra'


class Fn(StrEnum):
    SPE_ENHANCE = 'model.spe_enhance'
    SUB_FORMAT = 'sub.format'
    GPT_SUMMARY = 'gpt_summary'
    GPT_DOMAIN_TYPE = 'gpt_domain_type'
    GENERATE_TXT = 'sub.generate_txt'
    TRANSLATOR = 'translate.current'
    PROTECTED_MODE = 'model.protected_mode'
    MEDIA_AUTO_AUDIO = 'media.auto_make_audio'
    MEDIA_AUTO_VIDEO = 'media.auto_make_video'
    USE_MERGE_SUBTITLE = 'media.use_merge_subtitle'
    MODEL_LIST = 'model.list'
    SUB_POST_FORMAT = 'sub.need_format'
    SUB_KARAOKE = 'sub.karaoke'
    SUB_MAXLENCUT = 'sub.maxlen_cut'
    SUB_MAXLEN_OTHER = 'sub.maxlen_other'
    SUB_MAXLEN_CJK = 'sub.maxlen_cjk'
    SUB_AI_FORMAT = 'sub.ai_format'
    SUBTRANS_SINGLE = 'subtrans.single'
    SUBTRANS_BATCH = 'subtrans.batch'


def is_paid_version(version):
    return version in [ver for ver in Version if ver is not Version.FREE]


class VersionManager:
    '''
    版本管理
    '''
    VERSIONS_DICT = {}

    @classmethod
    def add_version(cls, ver: Version) -> None:
        '''
        添加一个版本， 初始化
        '''
        if cls.VERSIONS_DICT.get(ver) is None:
            cls.VERSIONS_DICT[ver] = {}

    @classmethod
    def add_fn(cls, ver: Version, fn_name: Fn, fn_values: Any):
        '''
        添加一个功能到一个版本，以及对应的值set
        '''
        cls.VERSIONS_DICT[ver][fn_name] = fn_values

    @classmethod
    def get_ver(cls):
        from . import config
        return config.APP_VERSION

    @classmethod
    def check_fn(cls, fn_name: Fn) -> bool:
        '''
        根据当前版本检查某个fn是否允许
        '''
        ver = cls.get_ver()
        return cls.VERSIONS_DICT[ver].get(fn_name) is not None

    @classmethod
    def get_values(cls, fn_name: Fn) -> list:
        '''
        返回某个功能支持的所有值
        '''
        ver = cls.get_ver()
        values = cls.VERSIONS_DICT[ver].get(fn_name)
        if values is not None:
            if isinstance(values, set):
                return sorted(list(values))
            else:
                return values
        else:
            return []

    @classmethod
    def check_fn_value(cls, fn_name: Fn, fn_values: Any) -> bool:
        '''
        根据当前版本检查某个fn， 以及传入的值列表是否允许
        '''
        ver = cls.get_ver()
        all_values = cls.VERSIONS_DICT[ver].get(fn_name)
        if all_values is None:
            return False
        if not isinstance(fn_values, set):
            if isinstance(fn_values, list):
                fn_values = set(fn_values)
            else:
                fn_values = set([fn_values])
        return all_values >= fn_values


def init_verions():
    '''
    初始化所有版本
    '''
    for ver in Version:
        VersionManager.add_version(ver)


def init_fns():
    '''
    针对不同版本初始化不同的功能
    '''
    from v2subpy.trans import TranslatorEnum
    # free
    free_dict = {
        Fn.SUB_FORMAT: set([SubtitleFormatEnum.SRT.value]),
        Fn.TRANSLATOR: [TranslatorEnum.DEFAULT],
        Fn.MODEL_LIST: MODELS_SUPPORTED_NAMES[:1],
    }
    for fn, values in free_dict.items():
        VersionManager.add_fn(Version.FREE, fn, values)

    # Pro
    pro_dict = {
        Fn.SUB_FORMAT: set([SubtitleFormatEnum.SRT.value]),
        Fn.TRANSLATOR: [TranslatorEnum.DEFAULT,
                        TranslatorEnum.GOOGLE, TranslatorEnum.CHATGPT,TranslatorEnum.DEEPSEEK, ],
        Fn.GPT_DOMAIN_TYPE: [GptDomainTypeEnum.openai, GptDomainTypeEnum.other],
        Fn.MODEL_LIST: MODELS_SUPPORTED_NAMES[:3],

    }
    for fn, values in pro_dict.items():
        VersionManager.add_fn(Version.PRO, fn, values)

    # Max
    max_dict = {
        Fn.SUB_FORMAT: set([SubtitleFormatEnum.SRT.value, SubtitleFormatEnum.VTT.value, SubtitleFormatEnum.ASS.value]),
        Fn.GPT_SUMMARY: set(),
        Fn.GENERATE_TXT: set(),
        Fn.SPE_ENHANCE: set(),
        Fn.TRANSLATOR: [TranslatorEnum.DEFAULT, TranslatorEnum.GOOGLE, TranslatorEnum.CHATGPT,
                        TranslatorEnum.DEEPL, TranslatorEnum.GEMINI, TranslatorEnum.CLAUDE, 
                        TranslatorEnum.DEEPSEEK, TranslatorEnum.MODELBAO, TranslatorEnum.CUSTOMLLM],
        Fn.PROTECTED_MODE: set(),
        Fn.GPT_DOMAIN_TYPE: [GptDomainTypeEnum.openai, GptDomainTypeEnum.v2sub, GptDomainTypeEnum.other],
        # 强化版不支持large-turbo
        Fn.MODEL_LIST: MODELS_SUPPORTED_NAMES[:-1],
        Fn.SUB_POST_FORMAT: set(),
        Fn.SUBTRANS_SINGLE: set(),
    }
    for fn, values in max_dict.items():
        VersionManager.add_fn(Version.MAX, fn, values)

    # Ultra
    ultra_dict = deepcopy(max_dict)
    # 豪华版支持所有模型
    ultra_dict[Fn.MODEL_LIST] = MODELS_SUPPORTED_NAMES
    ultra_dict[Fn.MEDIA_AUTO_AUDIO] = set()
    ultra_dict[Fn.MEDIA_AUTO_VIDEO] = set()
    ultra_dict[Fn.SUB_KARAOKE] = set()
    ultra_dict[Fn.SUB_MAXLENCUT] = set()
    ultra_dict[Fn.SUB_AI_FORMAT] = set()
    ultra_dict[Fn.SUBTRANS_BATCH] = set()

    for fn, values in ultra_dict.items():
        VersionManager.add_fn(Version.ULTRA, fn, values)


def fn_is_enabled(fn: Fn) -> bool:
    '''
    检查选项是否有效， 并已打开

    Args:

    Returns:
        True - enabled
        Flase - not enabled

    '''
    from v2subpy.utils.config import get_key
    if VersionManager.check_fn(fn):
        return get_key(fn)
    return False


def init_verson_manage():
    init_verions()
    init_fns()
